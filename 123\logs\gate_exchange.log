2025-08-11 10:22:41.085 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:41.085 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:41.093 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:41.095 [INFO] [exchanges.gate_exchange] 🚀 初始化Gate.io交易所（分离账户模式）...
2025-08-11 10:22:41.301 [WARNING] [exchanges.gate_exchange] 获取Gate.io服务器时间失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'，使用本地时间
2025-08-11 10:22:41.303 [INFO] [exchanges.gate_exchange] ✅ Gate.io连接成功，服务器时间: 1754900561301
2025-08-11 10:22:41.303 [INFO] [exchanges.gate_exchange] ✅ Gate.io使用分离账户模式
2025-08-11 10:22:41.304 [INFO] [exchanges.gate_exchange] 🔍 获取现货余额...
2025-08-11 10:22:41.349 [ERROR] [exchanges.gate_exchange] 获取Gate.io余额失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.225 [INFO] [exchanges.gate_exchange] 🔍 获取期货余额...
2025-08-11 10:22:42.277 [ERROR] [exchanges.gate_exchange] 获取Gate.io余额失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.278 [INFO] [exchanges.gate_exchange] ✅ Gate.io初始余额: 现货0.00 + 期货0.00 = 总计0.00 USDT
2025-08-11 10:22:42.279 [INFO] [exchanges.gate_exchange] ✅ Gate.io分离账户模式已确认
2025-08-11 10:22:42.279 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所初始化完成
2025-08-11 10:22:42.416 [ERROR] [exchanges.gate_exchange] 获取Gate.io余额失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.462 [ERROR] [exchanges.gate_exchange] 获取Gate.io余额失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.744 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:42.745 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:42.745 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:42.789 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.792 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:42.792 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:42.792 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:42.835 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.200 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:43.200 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:43.200 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:43.297 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.300 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:43.300 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:43.300 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:43.342 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.546 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:43.546 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:43.546 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:43.640 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.646 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:43.646 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:43.646 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:43.724 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.022 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:44.022 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:44.022 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.065 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.076 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:44.076 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:44.076 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.119 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.337 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:44.337 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:44.337 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.379 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.381 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:44.381 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:44.381 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.427 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.619 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:44.619 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:44.619 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.690 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.693 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:44.693 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:44.693 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.739 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.004 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:45.005 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:45.005 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.063 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.069 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:45.069 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:45.069 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.132 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.372 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:45.372 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:45.372 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.463 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.466 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:45.466 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:45.466 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.537 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.731 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:45.731 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:45.731 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.780 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.782 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:45.782 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:45.782 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.830 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.040 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:46.040 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:46.040 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.092 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.095 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:46.095 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:46.095 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.143 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.377 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:46.379 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:46.381 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.443 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.445 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:46.445 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:46.445 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.491 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.712 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:46.712 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:46.712 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.756 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.758 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:46.758 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:46.759 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.807 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.025 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:47.025 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:47.025 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:47.071 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.074 [INFO] [exchanges.gate_exchange] ✅ Gate API限速使用统一配置: 15次/秒
2025-08-11 10:22:47.074 [INFO] [exchanges.gate_exchange] 初始化Gate.io交易所接口，API请求限制: 15/秒
2025-08-11 10:22:47.075 [INFO] [exchanges.gate_exchange] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:47.122 [ERROR] [exchanges.gate_exchange] 获取Gate.io现货交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.455 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 ADA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.646 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 DOGE-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.919 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 SOL-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.065 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 AVAX-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.225 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 DOT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.376 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 BNB-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.520 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 AI16Z-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.664 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 BONK-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.814 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 PENGU-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.973 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 ERA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.125 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 PUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.310 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 TRUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.649 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 POPCAT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.887 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 ADA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.107 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 DOGE-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.257 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 SOL-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.418 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 AVAX-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.599 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 DOT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.794 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 BNB-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.956 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 AI16Z-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.120 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 BONK-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.289 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 PENGU-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.435 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 ERA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.587 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 PUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.765 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 TRUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.939 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 POPCAT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.085 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 ADA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.301 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 DOGE-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.454 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 SOL-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.621 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 AVAX-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.766 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 DOT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.906 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 BNB-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.089 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 AI16Z-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.336 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 BONK-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.529 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 PENGU-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.696 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 ERA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.836 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 PUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.993 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 TRUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:54.197 [ERROR] [exchanges.gate_exchange] ❌ Gate.io获取合约信息失败 POPCAT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:13.218 [ERROR] [exchanges.gate_exchange] 获取Gate.io余额失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:13.267 [ERROR] [exchanges.gate_exchange] 获取Gate.io余额失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
