好——下面给你一套**可直接落地**、工程化且实盘可测的 **BTC/ETH（及扩展）统计套利完整方案**。我把方案分成：**总体设计 → 数据与配对筛选 → 信号生成（统计/动态）→ 执行/滑点/延迟处理 → 风控与资金管理 → 回测与仿真 → 部署监控**，并给出关键代码片段与推荐参数。我要把学术/实务的最佳实践融合进来（并在关键点给出处）。如果你想，我可以把代码直接生成到你现有框架里的 module stub。

---

# 1) 总体设计要点（一句话版）

用\*\*协整残差（或卡尔曼/自适应模型）\*\*作为主价差指标，基于残差的 z-score 产生进出场信号；用滚动窗口与均值漂移检测动态调整阈值；执行侧严格控制延迟、分片限价/IOC、滑点预估并做实时撤单策略；全面的时间止损与保证金/仓位不对称处理。研究支持：动态协整/OU 半衰期方法在加密对中被频繁使用与验证。([arXiv][1], [repositorio.ucp.pt][2])

---

# 2) 数据与配对筛选（必须严格）

1. 数据频率：优先使用 **1m 或 5m** 级别（回测阶段可用1m，实盘建议5m作为信号频率以降低噪声与延迟敏感性）。
2. 数据来源：同一源（或同一时间基准）获取 BTC/USDT 与 ETH/USDT 的 **交易撮合价（trade price）与 orderbook top-10 深度快照**。绝对不要只用 Kline close（会掩盖执行价差问题）。
3. 时间同步：统一使用 NTP 校准服务器时间，对比交易所返回的 `trade_time` 与本地接收时间，计算并记录每个数据源的平均延迟与抖动。延迟 > 200ms 的源要标记为低优先级/备用。
4. 协整筛选流程（候选配对选择）：

   * 用 **滚动窗口（lookback）检验协整**：例如 60-180 天（或 2000-8000 条 1m 数据）窗口做 Engle–Granger 或 Johansen 测试；保留 p-value < 0.05 的配对。
   * 计算残差序列并检验平稳性（ADF）。仅使用通过平稳性检验的配对。([repositorio.ucp.pt][2], [DIVA Portal][3])

> 说明：在加密市场，相关性不等于协整 —— 要用协整残差作为交易变量。学术/实证文献与实务教程都强烈建议以协整或 OU 残差为主。([arXiv][1], [wundertrading.com][4])

---

# 3) 信号生成（核心算法，三选一，推荐 1 或 2）

**方案 A（推荐 - 协整 + OU 残差）**
流程：

1. 对两序列（BTC, ETH）做回归： `BTC_t = α + β * ETH_t + ε_t`（OLS 或滚动 OLS）。
2. 残差 `ε_t` 作为 spread，检验其均值回复性；拟合 Ornstein–Uhlenbeck (OU) 模型，估算 **半衰期 (half-life)**。用半衰期确定 z-score 的合适 lookback（如 half-life\*1\~3）。
3. z = (ε\_t - μ\_roll) / σ\_roll，进出场：

   * 开仓：|z| > z\_open（推荐 z\_open = 2.0，波动小对可降到 1.5）
   * 平仓：|z| < z\_close（推荐 z\_close = 0.5）
   * 时间止损：持仓超过 `T_max = max(half_life*5, 4 hours)` 强制平仓或减仓。
4. 每次开仓按残差方向按比例做多/空两腿：Size\_BTC / Size\_ETH 以 β（或用动态 hedge ratio）调整，确保对冲。

> 关键：用协整残差（而非单纯比值）生成 z-score，更具统计基础与稳定性。([arXiv][1], [Medium][5])

**方案 B（卡尔曼滤波 - 动态对冲比率）**

* 使用 Kalman Filter 在线估计 `β_t`（动态回归系数），残差为 `ε_t = BTC_t - α_t - β_t * ETH_t`。适合 hedge ratio 随时间漂移的场景，能减少回测与实盘的误差。对高频与中频都适用。([Medium][6])

**方案 C（机器学习增强）**

* 在 z-score 基础上加入波动率特征（ATR）、orderflow、资金费率、成交量差等，训练一个轻量 XGBoost/LightGBM 预测短期回归概率并作为信号过滤器。注意过拟合风险，必须在滚动真实时间窗口做 walk-forward 验证。

---

# 4) 执行层（最容易翻车的地方）

1. **时间戳与延迟**：所有信号生成和下单逻辑必须用同一路径的数据（或统一对齐）。如果跨交易所执行，先做下单延迟/成交率基准测试（TTF）并写入回测模型。论文与研究都指出微秒/毫秒延迟会显著影响策略回测真实性。([arXiv][1])
2. **下单策略**：

   * 优先使用 **限价单分段（slice）+ IOC/FOO fallback**：以顶级挂单价 ± 滑点预算提交限价，若未成交则用市价/IOC 执行（取决于滑点）。
   * 对流动性较差时：按成交量百分比（例如 taker 不超过 5% of 5-min ADV）动态缩小尺寸。
3. **滑点估计**：基于 orderbook depth 模拟市场冲击（30 档深度），并在下单前计算预期滑点成本，若成本超阈值则缩仓或跳过信号。实务指南强烈建议用深度快照做滑点模型。([CoinAPI][7], [wundertrading.com][8])
4. **跨所资金限制**：若需要跨所套保（比如现货在 A，期货在 B），优先使用统一账户/保证金机制，若不能，则必须提前预置两端资金并维护最小流动性 buffer，避免资金划转延迟导致无法对冲。

---

# 5) 风控与资金管理（必须强制）

1. **仓位上限**：每笔交易风险为账户净值的 `R`（例如 R = 0.5%），根据预估回撤/滑点换算仓位。
2. **净暴露控制**：虽然是对冲策略，但短期可能存在净多/净空，给出净暴露上限（例如 ≤ 5% 净多或净空）。
3. **最大回撤触发器**：策略池累计回撤超过 8%（可调）时自动降仓或停策略。
4. **时间止损 & 最大持仓周期**：若持仓超过 `T_max` 仍未回归，按 50% 危险平仓或完全平仓。
5. **保证金/强平预警**：实时计算保证金比例，如接近交易所强平阈值（比如 1.2x）即刻部分减仓并发告警。
6. **方向不对称费用调整**：不同腿的交易费/资金费率差异要加入净收益计算与仓位调整（例如做多 BTC 做空 ETH 的资金成本不同，要映射到仓位 sizing）。([wundertrading.com][8])

---

# 6) 回测与仿真（真实度要高）

1. **数据粒度**：回测至少使用 **tick 或 1s 交易流**（如果可得）或最少 1m trade-level 数据加上 orderbook 快照。
2. **模拟成交模型**：基于 orderbook depth 模拟实际执行价（包括虚假挂单、部分成交、撮合延迟）。将网络与执行延迟加入回测（用历史分布采样）。文献与实务建议把执行成本建模进回测。([arXiv][1], [repositorio.ucp.pt][2])
3. **Walk-forward / Rolling backtest**：用滚动窗口做参数再校准，避免 look-ahead bias。
4. **压力测试**：跑 2020-2021、2022 崩盘期、2024 年等极端行情场景，记录最大回撤、持仓天数分布、超额亏损次数。

---

# 7) 关键参数（起点推荐）

* lookback (协整检验)：90~~180 天（或 10000~~20000 条 1m）用于初筛。
* signal lookback for z-score：`L = max(half_life*2, 120)` bars。
* z\_open = 2.0；z\_close = 0.5；z\_stop = 4.0（极端反向强迫退出）。
* minimum liquidity threshold：5-min ADV >= X（按你的资金规模设定，举例：若单笔目标 100k USD，5-min ADV 应 >= 2M）。
* max position risk per trade R = 0.5% equity（可调整）。

---

# 8) 模块化工程实现（与现有框架的对接）

建议模块（每个模块单独进程/容器）：

1. Data Ingestor（统一时间对齐、延迟记录）
2. Pair Selector（滚动协整测试、ADF、half-life 估计）
3. Signal Engine（协整残差/OU / Kalman）
4. Execution Engine（限价切片、IOC fallback、滑点预算）
5. Risk Manager（仓位计算、保证金、净暴露）
6. Backtester / Simulator（orderbook 驱动）
7. Monitor & Alert（延迟、未成交率、回撤、资金费率）

核心交互：Signal Engine -> Risk Manager（返回 final sizes）-> Execution Engine -> Orderbook snapshot -> fill feedback -> PnL module。

---

# 9) 示例伪代码（关键段 — 协整+OU z-score）

```python
# 获取 price series: btc, eth (aligned)
beta, alpha = rolling_ols(eth, btc, window=lookback)  # or kalman
spread = btc - (alpha + beta * eth)
half_life = estimate_half_life(spread)  # OU-fitting
z = (spread - spread.rolling(window=hl*2).mean()) / spread.rolling(window=hl*2).std()

if z > z_open:
    # 做空 BTC，做多 ETH
    size_btc, size_eth = sizing(beta, risk_per_trade)
    place_orders(side_btc='sell', side_eth='buy', sizes=...)
elif z < -z_open:
    # 做多 BTC，做空 ETH
...
# 平仓
if abs(z) < z_close or time_in_trade > T_max:
    close_positions()
```

---

# 10) 实盘监控与自检（必须）

* 实时监控：信号延迟（signal->order时间）、order fill rate、avg slippage vs predicted、未成交订单比率、资金费率变化、交易所异常（503/限流）alarm。
* 自动降级：当延迟或未成交率超过阈值时自动暂停下单并发警告。
* 日志化：每笔交易保存快照（signal 时间、used α/β、orderbook 快照、成交填充明细、实际成交价、滑点对比）。这对后期回溯分析至关重要。([arXiv][1])

---

# 11) 常见陷阱（清单）

1. 只看相关系数不做协整检验。
2. 使用全历史固定 β 而不检测漂移（应使用滚动或卡尔曼）。
3. 回测用 Kline close 做成交价近似（会严重高估收益）。
4. 忽视 orderbook depth 导致实盘因滑点爆亏。
5. 忽视资金费率/交易费差异。
   （这些问题在学术和实务案例里很常见并被反复指出）。([repositorio.ucp.pt][2], [wundertrading.com][8])

---

# 12) 推荐阅读 / 实现参考（快速上手）

* 动态协整与 pairs trading 论文（arXiv）：用于半衰期、动态窗口的实现示例。([arXiv][1])
* “Pairs trading in crypto” 学位论文：对回测现实性、 hourly vs minute 的实践对比。([thesis.eur.nl][9], [repositorio.ucp.pt][2])
* Kalman filter 在线估计 hedge ratio 教程（Medium 博客） — 实战可参考。([Medium][6])
* 执行与滑点建模实务（CoinAPI/Wundertrading 指南）。([CoinAPI][7], [wundertrading.com][10])

---

# 结论（要点回顾）

* **必须**用协整残差或卡尔曼残差做 z-score（比单纯比值或比率稳健得多）。([arXiv][1], [Medium][5])
* **必须**把执行延迟、orderbook depth 和滑点模型纳入回测并作为实时风控的一部分。([arXiv][1], [CoinAPI][7])
* 通过滚动协整、half-life、自适应阈值与时间止损，可以把策略变得既可行又稳健。([repositorio.ucp.pt][2], [arXiv][1])

---

要不要我现在：

1. 把上面全部做成一个**工程化的 GitHub 风格模板**（目录 + module stubs +关键实现文件如 `pair_selector.py`、`signal_engine.py`、`execution_engine.py`、`backtester.py`）？
2. 或者直接把**协整+OU**版本写成可运行的 Python 脚本（含回测，使用 CCXT 或本地 CSV 数据接口）并附带样例配置与参数？

你选一个，我就把可执行代码、测试数据读取、以及一个最小 Docker 部署脚本给你（含监控告警 stub）。

[1]: https://arxiv.org/abs/2109.10662?utm_source=chatgpt.com "Evaluation of Dynamic Cointegration-Based Pairs Trading Strategy in the Cryptocurrency Market"
[2]: https://repositorio.ucp.pt/bitstream/10400.14/40628/1/203132505.pdf?utm_source=chatgpt.com "[PDF] Pairs Trading in Crypto Currencies - A cointegration based application"
[3]: https://www.diva-portal.org/smash/get/diva2%3A1331682/FULLTEXT01.pdf?utm_source=chatgpt.com "[PDF] Cointegration among cryptocurrencies - DiVA portal"
[4]: https://wundertrading.com/journal/en/learn/article/crypto-pairs-trading-strategy?utm_source=chatgpt.com "Crypto Pairs Trading Strategy Explained: A Market-Neutral Approach"
[5]: https://janelleturing.medium.com/python-ornstein-uhlenbeck-for-crypto-mean-reversion-trading-287856264f7a?utm_source=chatgpt.com "Python Ornstein-Uhlenbeck for Crypto Mean Reversion Trading"
[6]: https://medium.com/%40saeedroshanbox/pair-and-triplet-spread-trading-on-cryptos-using-kalman-filter-65cdacbee5a2?utm_source=chatgpt.com "Pair and Triplet Spread Trading on Cryptos Using Kalman Filter"
[7]: https://www.coinapi.io/blog/3-statistical-arbitrage-strategies-in-crypto?utm_source=chatgpt.com "Crypto Arbitrage Strategy: 3 Core Statistical Approaches - CoinAPI.io"
[8]: https://wundertrading.com/journal/en/learn/article/statistical-arbitrage-crypto-bot?utm_source=chatgpt.com "What Is a Statistical Arbitrage Crypto Bot? - WunderTrading"
[9]: https://thesis.eur.nl/pub/67552/Thesis-Pairs-trading-.pdf?utm_source=chatgpt.com "[PDF] Pairs Trading in the Cryptocurrency Market: An Empirical Analysis of ..."
[10]: https://wundertrading.com/journal/en/learn/article/statistical-arbitrage-strategies?utm_source=chatgpt.com "Top Statistical Arbitrage Strategies Explained - WunderTrading"
