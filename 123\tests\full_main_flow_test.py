#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 完整main.py流程测试 - 验证预热机制在真实流程中的效果
"""

import asyncio
import time
import sys
import os

# 设置项目路径
project_root = '/root/myproject/123/77 修复了开仓差价变化错误的问题/123'
sys.path.insert(0, project_root)
os.chdir(project_root)

async def test_full_main_flow():
    """测试完整main.py流程 - 从预热到ArbitrageEngine启动"""
    
    print("🚀 开始完整main.py流程测试...")
    print("=== 模拟main.py启动流程 ===")
    
    total_start = time.time()
    
    try:
        # 🔥 **步骤1**: 启动连接预热 (模拟main.py中的预热)
        print("⚡ 步骤1: 启动交易所连接预热...")
        preheat_start = time.time()
        
        from core.exchange_connection_preheater import get_connection_preheater
        preheater = get_connection_preheater()
        
        # 启动预热
        preheat_success = await preheater.start_preheating_all_exchanges()
        preheat_time = (time.time() - preheat_start) * 1000
        
        if preheat_success:
            print(f"🎉 连接预热成功: {preheat_time:.1f}ms")
            print("⚡ 预热连接可零延迟获取")
        else:
            print(f"⚠️ 连接预热失败: {preheat_time:.1f}ms")
            return False
        
        # 🔥 **步骤2**: 使用统一初始化模块 (模拟main.py)
        print("🔥 步骤2: 使用统一初始化模块...")
        init_start = time.time()
        
        from core.trading_system_initializer import get_trading_system_initializer
        initializer = get_trading_system_initializer()
        
        # 统一初始化 - 应该使用预热连接
        success = await initializer.initialize_all_systems()
        init_time = (time.time() - init_start) * 1000
        
        if not success:
            print(f"❌ 统一初始化失败: {init_time:.1f}ms")
            return False
        
        print(f"✅ 统一初始化完成: {init_time:.1f}ms")
        
        # 🔥 **验证结果**: 检查是否使用了预热连接
        if hasattr(initializer, 'exchanges') and initializer.exchanges:
            print(f"✅ 获得交易所实例: {list(initializer.exchanges.keys())}")
        
        if hasattr(initializer, 'engine') and initializer.engine:
            print("✅ ArbitrageEngine已启动")
            if hasattr(initializer.engine, 'execution_engine') and initializer.engine.execution_engine:
                print("✅ ExecutionEngine已集成")
        
        total_time = (time.time() - total_start) * 1000
        
        print("=== 测试结果 ===")
        print(f"📊 总执行时间: {total_time:.1f}ms ({total_time/1000:.3f}秒)")
        print(f"📊 预热时间: {preheat_time:.1f}ms")  
        print(f"📊 初始化时间: {init_time:.1f}ms")
        print(f"📊 是否达到1秒目标: {'✅' if total_time < 1000 else '❌'}")
        
        # 性能分析
        if total_time < 1000:
            print("🎉 成功！预热机制消除了4.7秒瓶颈！")
            return True
        elif init_time < preheat_time / 2:  # 初始化时间明显小于预热时间
            print("🎯 部分成功：预热连接生效，但总时间仍超1秒")
            print("💡 可能需要进一步优化其他环节")
            return True
        else:
            print("❌ 预热机制未生效，仍在重复创建连接")
            return False
        
    except Exception as e:
        total_time = (time.time() - total_start) * 1000 if 'total_start' in locals() else 0
        print(f"❌ 测试异常: {e}")
        print(f"📊 异常时总耗时: {total_time:.1f}ms")
        return False

async def main():
    """主函数"""
    print("🚀 完整main.py流程性能验证")
    print("=" * 50)
    
    success = await test_full_main_flow()
    
    print("=" * 50)
    if success:
        print("✅ 测试通过：预热机制工作正常")
    else:
        print("❌ 测试失败：需要进一步修复")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)