#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一资金费率数据获取器
为Gate.io、Bybit、OKX三大交易所提供统一的资金费率数据获取接口

数据格式统一处理：
- Gate.io：字符串格式 "0.000100"，时间戳为秒
- Bybit：历史记录数组，需提取最新，时间戳为毫秒  
- OKX：数组格式，时间戳为毫秒

统一输出：float格式，6位精度，时间戳为UTC秒
"""

import aiohttp
import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from utils.logger import get_logger
from core.funding_rate_awareness import FundingRateInfo


@dataclass
class UnifiedFundingRateResponse:
    """统一资金费率响应"""
    exchange: str
    symbol: str
    funding_rate: float
    timestamp: float
    next_settlement_time: float
    success: bool
    error_message: Optional[str] = None
    raw_data: Optional[Dict] = None


class UnifiedFundingRateProvider:
    """统一资金费率数据提供器"""
    
    def __init__(self, exchanges: Dict = None, session_manager=None):
        """初始化资金费率提供器
        
        Args:
            exchanges: 交易所实例字典
            session_manager: HTTP会话管理器
        """
        self.logger = get_logger(self.__class__.__name__)
        self.exchanges = exchanges or {}
        
        # 使用统一HTTP会话管理器
        if session_manager:
            self.session_manager = session_manager
        else:
            # 回退到独立会话
            from core.unified_http_session_manager import get_unified_session_manager
            self.session_manager = get_unified_session_manager()
            
        # API端点配置
        self.api_endpoints = {
            "gate": {
                "funding_rate": "/futures/usdt/funding_rate",
                "base_url": "https://api.gateio.ws/api/v4"
            },
            "bybit": {
                "funding_rate": "/v5/market/funding/history",
                "base_url": "https://api.bybit.com"
            },
            "okx": {
                "funding_rate": "/api/v5/public/funding-rate",
                "base_url": "https://www.okx.com"
            }
        }
        
        # 请求参数映射
        self.symbol_mapping = {
            "gate": self._convert_symbol_for_gate,
            "bybit": self._convert_symbol_for_bybit, 
            "okx": self._convert_symbol_for_okx
        }
        
        # 错误重试配置
        self.max_retries = 3
        self.retry_delay = 1.0  # 秒
        self.timeout = 5.0  # 请求超时
        
        self.logger.info("✅ 统一资金费率数据提供器初始化完成")
        
    def _convert_symbol_for_gate(self, symbol: str) -> str:
        """转换symbol为Gate.io格式
        
        例: ADA-USDT -> ADA_USDT
        """
        return symbol.replace("-", "_")
        
    def _convert_symbol_for_bybit(self, symbol: str) -> str:
        """转换symbol为Bybit格式
        
        例: ADA-USDT -> ADAUSDT
        """
        return symbol.replace("-", "")
        
    def _convert_symbol_for_okx(self, symbol: str) -> str:
        """转换symbol为OKX格式
        
        例: ADA-USDT -> ADA-USDT-SWAP (永续合约)
        """
        return f"{symbol}-SWAP"
        
    async def get_funding_rate_gate(self, symbol: str) -> UnifiedFundingRateResponse:
        """获取Gate.io资金费率"""
        try:
            gate_symbol = self.symbol_mapping["gate"](symbol)
            url = f"{self.api_endpoints['gate']['base_url']}{self.api_endpoints['gate']['funding_rate']}"
            
            params = {"contract": gate_symbol}
            
            session = await self.session_manager.get_session("gate")
            async with session.get(url, params=params, timeout=self.timeout) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if isinstance(data, list) and len(data) > 0:
                        # Gate.io返回数组，取最新的一个
                        latest_data = data[0]
                        
                        # 解析数据
                        funding_rate = float(latest_data.get('r', '0'))  # 资金费率字符串转float
                        timestamp = float(latest_data.get('t', 0))  # 时间戳已经是秒
                        
                        # 计算下次结算时间（Gate.io每8小时结算一次）
                        next_settlement = self._calculate_next_settlement_time(timestamp)
                        
                        return UnifiedFundingRateResponse(
                            exchange="gate",
                            symbol=symbol,
                            funding_rate=round(funding_rate, 6),  # 6位精度
                            timestamp=timestamp,
                            next_settlement_time=next_settlement,
                            success=True,
                            raw_data=latest_data
                        )
                    else:
                        return UnifiedFundingRateResponse(
                            exchange="gate",
                            symbol=symbol,
                            funding_rate=0.0,
                            timestamp=time.time(),
                            next_settlement_time=0.0,
                            success=False,
                            error_message="No funding rate data returned"
                        )
                else:
                    error_text = await response.text()
                    return UnifiedFundingRateResponse(
                        exchange="gate",
                        symbol=symbol,
                        funding_rate=0.0,
                        timestamp=time.time(),
                        next_settlement_time=0.0,
                        success=False,
                        error_message=f"HTTP {response.status}: {error_text}"
                    )
                    
        except Exception as e:
            self.logger.error(f"❌ Gate.io资金费率获取异常: {symbol} - {e}")
            return UnifiedFundingRateResponse(
                exchange="gate",
                symbol=symbol,
                funding_rate=0.0,
                timestamp=time.time(),
                next_settlement_time=0.0,
                success=False,
                error_message=str(e)
            )
            
    async def get_funding_rate_bybit(self, symbol: str) -> UnifiedFundingRateResponse:
        """获取Bybit资金费率"""
        try:
            bybit_symbol = self.symbol_mapping["bybit"](symbol)
            url = f"{self.api_endpoints['bybit']['base_url']}{self.api_endpoints['bybit']['funding_rate']}"
            
            params = {
                "category": "linear",
                "symbol": bybit_symbol,
                "limit": 1  # 只获取最新的一条记录
            }
            
            session = await self.session_manager.get_session("bybit")
            async with session.get(url, params=params, timeout=self.timeout) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get('retCode') == 0 and 'result' in data:
                        result = data['result']
                        if 'list' in result and len(result['list']) > 0:
                            latest_data = result['list'][0]
                            
                            # 解析数据
                            funding_rate = float(latest_data.get('fundingRate', '0'))
                            timestamp_ms = int(latest_data.get('fundingRateTimestamp', 0))
                            timestamp = timestamp_ms / 1000.0  # 毫秒转秒
                            
                            # 计算下次结算时间
                            next_settlement = self._calculate_next_settlement_time(timestamp)
                            
                            return UnifiedFundingRateResponse(
                                exchange="bybit",
                                symbol=symbol,
                                funding_rate=round(funding_rate, 6),
                                timestamp=timestamp,
                                next_settlement_time=next_settlement,
                                success=True,
                                raw_data=latest_data
                            )
                    
                    return UnifiedFundingRateResponse(
                        exchange="bybit",
                        symbol=symbol,
                        funding_rate=0.0,
                        timestamp=time.time(),
                        next_settlement_time=0.0,
                        success=False,
                        error_message=f"API返回错误: {data.get('retMsg', 'Unknown error')}"
                    )
                else:
                    error_text = await response.text()
                    return UnifiedFundingRateResponse(
                        exchange="bybit",
                        symbol=symbol,
                        funding_rate=0.0,
                        timestamp=time.time(),
                        next_settlement_time=0.0,
                        success=False,
                        error_message=f"HTTP {response.status}: {error_text}"
                    )
                    
        except Exception as e:
            self.logger.error(f"❌ Bybit资金费率获取异常: {symbol} - {e}")
            return UnifiedFundingRateResponse(
                exchange="bybit",
                symbol=symbol,
                funding_rate=0.0,
                timestamp=time.time(),
                next_settlement_time=0.0,
                success=False,
                error_message=str(e)
            )
            
    async def get_funding_rate_okx(self, symbol: str) -> UnifiedFundingRateResponse:
        """获取OKX资金费率"""
        try:
            okx_symbol = self.symbol_mapping["okx"](symbol)
            url = f"{self.api_endpoints['okx']['base_url']}{self.api_endpoints['okx']['funding_rate']}"
            
            params = {"instId": okx_symbol}
            
            session = await self.session_manager.get_session("okx")
            async with session.get(url, params=params, timeout=self.timeout) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get('code') == '0' and 'data' in data:
                        data_list = data['data']
                        if len(data_list) > 0:
                            latest_data = data_list[0]
                            
                            # 解析数据
                            funding_rate = float(latest_data.get('fundingRate', '0'))
                            timestamp_ms = int(latest_data.get('fundingTime', 0))
                            timestamp = timestamp_ms / 1000.0  # 毫秒转秒
                            
                            # 计算下次结算时间
                            next_settlement = self._calculate_next_settlement_time(timestamp)
                            
                            return UnifiedFundingRateResponse(
                                exchange="okx",
                                symbol=symbol,
                                funding_rate=round(funding_rate, 6),
                                timestamp=timestamp,
                                next_settlement_time=next_settlement,
                                success=True,
                                raw_data=latest_data
                            )
                    
                    return UnifiedFundingRateResponse(
                        exchange="okx",
                        symbol=symbol,
                        funding_rate=0.0,
                        timestamp=time.time(),
                        next_settlement_time=0.0,
                        success=False,
                        error_message=f"API返回错误: {data.get('msg', 'Unknown error')}"
                    )
                else:
                    error_text = await response.text()
                    return UnifiedFundingRateResponse(
                        exchange="okx",
                        symbol=symbol,
                        funding_rate=0.0,
                        timestamp=time.time(),
                        next_settlement_time=0.0,
                        success=False,
                        error_message=f"HTTP {response.status}: {error_text}"
                    )
                    
        except Exception as e:
            self.logger.error(f"❌ OKX资金费率获取异常: {symbol} - {e}")
            return UnifiedFundingRateResponse(
                exchange="okx",
                symbol=symbol,
                funding_rate=0.0,
                timestamp=time.time(),
                next_settlement_time=0.0,
                success=False,
                error_message=str(e)
            )
            
    def _calculate_next_settlement_time(self, current_timestamp: float) -> float:
        """计算下次结算时间
        
        三大交易所统一结算时间：00:00, 08:00, 16:00 UTC
        """
        from datetime import datetime, timezone, timedelta
        
        current_dt = datetime.fromtimestamp(current_timestamp, tz=timezone.utc)
        current_hour = current_dt.hour
        
        # 找到下一个结算时间
        settlement_hours = [0, 8, 16]
        next_settlement_hour = None
        
        for hour in settlement_hours:
            if hour > current_hour:
                next_settlement_hour = hour
                break
                
        if next_settlement_hour is None:
            # 下一个结算时间是明天的00:00
            next_settlement_dt = (current_dt + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            # 今天的结算时间
            next_settlement_dt = current_dt.replace(hour=next_settlement_hour, minute=0, second=0, microsecond=0)
            
        return next_settlement_dt.timestamp()
        
    async def get_funding_rate_with_retry(self, exchange: str, symbol: str) -> UnifiedFundingRateResponse:
        """带重试机制获取资金费率"""
        for attempt in range(self.max_retries):
            try:
                if exchange.lower() == "gate":
                    response = await self.get_funding_rate_gate(symbol)
                elif exchange.lower() == "bybit":
                    response = await self.get_funding_rate_bybit(symbol)
                elif exchange.lower() == "okx":
                    response = await self.get_funding_rate_okx(symbol)
                else:
                    return UnifiedFundingRateResponse(
                        exchange=exchange,
                        symbol=symbol,
                        funding_rate=0.0,
                        timestamp=time.time(),
                        next_settlement_time=0.0,
                        success=False,
                        error_message=f"不支持的交易所: {exchange}"
                    )
                    
                if response.success:
                    return response
                    
                # 失败但还有重试次数
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))  # 指数退避
                    
            except Exception as e:
                self.logger.error(f"❌ 获取{exchange}资金费率第{attempt+1}次尝试失败: {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    
        # 所有重试都失败了
        return UnifiedFundingRateResponse(
            exchange=exchange,
            symbol=symbol,
            funding_rate=0.0,
            timestamp=time.time(),
            next_settlement_time=0.0,
            success=False,
            error_message=f"经过{self.max_retries}次重试后仍然失败"
        )
        
    async def get_funding_rate(self, exchange: str, symbol: str, use_cache: bool = True) -> UnifiedFundingRateResponse:
        """获取资金费率（支持缓存）"""
        # 检查缓存（如果启用）
        if use_cache:
            from core.funding_rate_awareness import get_funding_rate_awareness
            awareness = get_funding_rate_awareness()
            
            # 计算结算阶段以确定缓存策略
            time_to_settlement, _ = awareness.calculate_time_to_next_settlement()
            settlement_phase = awareness.determine_settlement_phase(time_to_settlement)
            
            cache_key = f"{exchange}_{symbol}"
            if awareness.is_funding_rate_cached(cache_key, settlement_phase):
                cached_info = awareness.get_cached_funding_rate(cache_key)
                if cached_info:
                    self.logger.debug(f"📊 使用缓存资金费率: {cache_key}")
                    return UnifiedFundingRateResponse(
                        exchange=exchange,
                        symbol=symbol,
                        funding_rate=cached_info.funding_rate,
                        timestamp=cached_info.timestamp,
                        next_settlement_time=cached_info.settlement_time,
                        success=True,
                        raw_data={"cached": True}
                    )
                    
        # 获取新数据
        response = await self.get_funding_rate_with_retry(exchange, symbol)
        
        # 缓存成功的响应
        if response.success and use_cache:
            from core.funding_rate_awareness import get_funding_rate_awareness
            awareness = get_funding_rate_awareness()
            
            # 创建FundingRateInfo用于缓存
            funding_info = awareness.create_funding_rate_info(
                exchange=exchange,
                symbol=symbol,
                funding_rate=response.funding_rate,
                timestamp=response.timestamp
            )
            
            cache_key = f"{exchange}_{symbol}"
            awareness.cache_funding_rate(cache_key, funding_info)
            
        return response
        
    async def get_multiple_funding_rates(self, requests: List[Tuple[str, str]], 
                                       use_cache: bool = True) -> Dict[str, UnifiedFundingRateResponse]:
        """批量获取多个资金费率"""
        tasks = []
        keys = []
        
        for exchange, symbol in requests:
            key = f"{exchange}_{symbol}"
            keys.append(key)
            task = self.get_funding_rate(exchange, symbol, use_cache)
            tasks.append(task)
            
        # 并发获取所有资金费率
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        result = {}
        for i, response in enumerate(responses):
            key = keys[i]
            if isinstance(response, Exception):
                exchange, symbol = requests[i]
                result[key] = UnifiedFundingRateResponse(
                    exchange=exchange,
                    symbol=symbol,
                    funding_rate=0.0,
                    timestamp=time.time(),
                    next_settlement_time=0.0,
                    success=False,
                    error_message=str(response)
                )
            else:
                result[key] = response
                
        return result
        
    def get_supported_exchanges(self) -> List[str]:
        """获取支持的交易所列表"""
        return list(self.api_endpoints.keys())
        
    async def validate_exchange_symbol(self, exchange: str, symbol: str) -> bool:
        """验证交易所和交易对是否支持资金费率获取"""
        try:
            response = await self.get_funding_rate(exchange, symbol, use_cache=False)
            return response.success
        except Exception as e:
            self.logger.error(f"❌ 验证{exchange}_{symbol}失败: {e}")
            return False


# 全局单例
_funding_rate_provider_instance = None

def get_funding_rate_provider(exchanges: Dict = None, session_manager=None) -> UnifiedFundingRateProvider:
    """获取统一资金费率提供器单例"""
    global _funding_rate_provider_instance
    if _funding_rate_provider_instance is None:
        _funding_rate_provider_instance = UnifiedFundingRateProvider(exchanges, session_manager)
    return _funding_rate_provider_instance