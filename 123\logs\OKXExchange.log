2025-08-11 10:22:41.454 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:41 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-11 10:22:41 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-11 10:22:41 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:41 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:41.455 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:41 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-11 10:22:41.500 [ERROR] [exchanges.okx_exchange] OKX获取账户配置失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:41 [WARNING] [OKXExchange] OKX设置持仓模式失败（可能已经是正确模式）: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:41.558 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:22:41.674 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:41.675 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:22:41.719 [WARNING] [exchanges.okx_exchange] OKX获取持仓模式失败，使用net模式设置杠杆: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:41.769 [ERROR] [exchanges.okx_exchange] OKX设置杠杆失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:41 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ADA-USDT
2025-08-11 10:22:41.773 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:22:41.823 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:41.823 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:22:41.865 [WARNING] [exchanges.okx_exchange] OKX获取持仓模式失败，使用net模式设置杠杆: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:41.908 [ERROR] [exchanges.okx_exchange] OKX设置杠杆失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:41 [DEBUG] [OKXExchange] OKX预设置杠杆成功: DOGE-USDT
2025-08-11 10:22:41.911 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:22:41.957 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:41.958 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:22:42.021 [WARNING] [exchanges.okx_exchange] OKX获取持仓模式失败，使用net模式设置杠杆: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.068 [ERROR] [exchanges.okx_exchange] OKX设置杠杆失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SOL-USDT
2025-08-11 10:22:42.072 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:22:42.122 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.123 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:22:42.165 [WARNING] [exchanges.okx_exchange] OKX获取持仓模式失败，使用net模式设置杠杆: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.221 [ERROR] [exchanges.okx_exchange] OKX设置杠杆失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42 [DEBUG] [OKXExchange] OKX预设置杠杆成功: AVAX-USDT
2025-08-11 10:22:42 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-11 10:22:42.556 [WARNING] [exchanges.okx_exchange] OKX统一账户API失败，尝试资金账户API: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.611 [ERROR] [exchanges.okx_exchange] OKX所有余额API都失败: 统一账户=TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay', 资金账户=TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.611 [ERROR] [exchanges.okx_exchange] 获取OKX余额失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.955 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:42 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:42.955 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:43.069 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.081 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:43 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:43.081 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:43.196 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.443 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:43 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:43.444 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:43.489 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.491 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:43 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:43.491 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:43.537 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.923 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:43 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:43.926 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:43.973 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.975 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:43 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:43.976 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:44.019 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.215 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:44.220 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:44.288 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.291 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:44.291 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:44.334 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.523 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:44.524 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:44.572 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.575 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:44.575 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:44.617 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.891 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:44.891 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:44.945 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.948 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:44.948 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:45.002 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.251 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:45.252 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:45.304 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.308 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:45.309 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:45.370 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.637 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:45.637 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:45.682 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.685 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:45.685 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:45.728 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.935 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:45.935 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:45.982 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.986 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:45.986 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:46.037 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.255 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:46 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:46.255 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:46.318 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.321 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:46 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:46.321 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:46.371 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.615 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:46 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:46.615 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:46.660 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.666 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:46 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:46.666 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:46.710 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.922 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:46 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:46.922 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:46.968 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.970 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:46 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:46.971 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:47.020 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.248 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:47 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:47.248 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:47.294 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.306 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:22:47 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:22:47.307 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:22:47.391 [ERROR] [exchanges.okx_exchange] 获取OKX交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.599 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 ADA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.855 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 DOGE-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.020 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 SOL-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.169 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 AVAX-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.331 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 DOT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.472 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 BNB-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.618 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 AI16Z-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.762 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 BONK-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.922 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 PENGU-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.080 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 ERA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.236 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 PUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.458 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 TRUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.781 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 POPCAT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.060 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 ADA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.208 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 DOGE-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.372 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 SOL-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.529 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 AVAX-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.720 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 DOT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.911 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 BNB-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.062 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 AI16Z-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.232 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 BONK-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.391 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 PENGU-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.541 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 ERA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.718 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 PUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.896 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 TRUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.032 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 POPCAT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.256 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 ADA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.408 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 DOGE-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.567 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 SOL-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.718 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 AVAX-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.860 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 DOT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.019 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 BNB-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.269 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 AI16Z-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.483 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 BONK-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.640 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 PENGU-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.789 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 ERA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.942 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 PUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:54.099 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 TRUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:54.287 [ERROR] [exchanges.okx_exchange] ❌ OKX获取合约信息失败 POPCAT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:08.373 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:08.373 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:08.630 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:08.630 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:08.842 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:08.842 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:09.073 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:09.073 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:09.295 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:09.295 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:09.501 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:09.502 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:09.702 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:09.702 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:09.965 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:09.966 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:10.247 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:10.247 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:10.470 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:10.471 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:10.818 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:10.818 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:11.143 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:11.146 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:11.457 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:11.457 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:11.754 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:11.754 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:12.050 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:12.050 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:12.399 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:12.400 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:12.711 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:12.714 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:13.026 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:13.026 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:23:13.412 [WARNING] [exchanges.okx_exchange] OKX统一账户API失败，尝试资金账户API: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:13.456 [ERROR] [exchanges.okx_exchange] OKX所有余额API都失败: 统一账户=TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay', 资金账户=TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:13.456 [ERROR] [exchanges.okx_exchange] 获取OKX余额失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
