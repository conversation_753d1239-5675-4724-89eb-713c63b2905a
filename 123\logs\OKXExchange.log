2025-08-11 10:49:04.133 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:04 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-11 10:49:04 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-11 10:49:04 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:49:04 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:04.134 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:04 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-11 10:49:04 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-11 10:49:04 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-11 10:49:04 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-11 10:49:04.780 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:49:05.298 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:49:05.298 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:49:05.886 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:49:05.886 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:49:05 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ADA-USDT
2025-08-11 10:49:05.886 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:49:06.355 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:49:06.357 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:49:06.871 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:49:06.871 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:49:06 [DEBUG] [OKXExchange] OKX预设置杠杆成功: DOGE-USDT
2025-08-11 10:49:06.872 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:49:07.369 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:49:07.369 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:49:07.866 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:49:07.867 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:49:07 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SOL-USDT
2025-08-11 10:49:07.895 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:49:08.413 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:49:08.413 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:49:08.863 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:49:08.863 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:49:08 [DEBUG] [OKXExchange] OKX预设置杠杆成功: AVAX-USDT
2025-08-11 10:49:08 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-11 10:49:08.936 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.936 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.937 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.937 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.939 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.939 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.939 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.940 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.942 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.942 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.942 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.942 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.945 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.945 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.945 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.946 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.948 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.948 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.948 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.948 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.950 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.951 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.951 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.951 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.953 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.953 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.953 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.953 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.955 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.955 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.956 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.956 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.958 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.958 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.958 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.958 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.960 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.960 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.961 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.961 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.965 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.965 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.966 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.966 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.969 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.969 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.969 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.969 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.972 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.972 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:08.972 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:49:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:49:08.972 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:49:09.398 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-11 10:49:09.465 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-11 10:49:09.471 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ERA-USDT
2025-08-11 10:49:09.478 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: PUMP-USDT -> 最大杠杆=20x
2025-08-11 10:49:09.484 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-11 10:49:09.645 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-11 10:49:09.648 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-11 10:49:09.732 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: TRUMP-USDT -> 最大杠杆=20x
2025-08-11 10:49:09.733 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: PENGU-USDT -> 最大杠杆=20x
2025-08-11 10:49:09.733 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-11 10:49:09.735 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-11 10:49:09.750 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: POPCAT-USDT -> 最大杠杆=20x
2025-08-11 10:49:09.775 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BONK-USDT -> 最大杠杆=20x
2025-08-11 10:49:10.051 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ERA-USDT
2025-08-11 10:49:11.565 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ERA-USDT
2025-08-11 10:51:32.691 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-11 10:51:34.364 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-11 10:51:34.698 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-11 10:51:34.716 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-11 10:51:36.809 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-11 10:51:38.303 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-11 10:51:38.802 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BONK-USDT -> 最大杠杆=20x
2025-08-11 10:51:38.821 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: PENGU-USDT -> 最大杠杆=20x
2025-08-11 10:51:38.833 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-11 10:51:40.935 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ERA-USDT
2025-08-11 10:51:42.416 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: PUMP-USDT -> 最大杠杆=20x
2025-08-11 10:51:42.925 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: TRUMP-USDT -> 最大杠杆=20x
2025-08-11 10:51:42.929 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: POPCAT-USDT -> 最大杠杆=20x
2025-08-11 10:51:43.936 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:44.018 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:44.018 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:44.520 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:44.523 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:45.440 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.441 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.441 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.441 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.442 [INFO] [exchanges.okx_exchange] OKX设置杠杆: BNB-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.442 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.442 [INFO] [exchanges.okx_exchange] OKX设置杠杆: BONK-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.442 [INFO] [exchanges.okx_exchange] OKX设置杠杆: PENGU-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.442 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ERA-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.442 [INFO] [exchanges.okx_exchange] OKX设置杠杆: PUMP-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.442 [INFO] [exchanges.okx_exchange] OKX设置杠杆: TRUMP-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.442 [INFO] [exchanges.okx_exchange] OKX设置杠杆: POPCAT-USDT-SWAP 3倍，保证金模式: cross
2025-08-11 10:51:45.542 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:45.544 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:46.044 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:46.044 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:46.067 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:46.067 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:46.125 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:46.126 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:46.129 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:46.133 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:46.248 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:46.291 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:46.291 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:46.291 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:46.292 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-11 10:51:46.345 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-11 10:51:46.345 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-11 10:51:46.345 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-11 10:51:46.345 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-11 10:51:46.345 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-11 10:51:46.346 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-11 10:51:46.346 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-11 10:51:46 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.346 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.389 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-11 10:51:46.398 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-11 10:51:46.414 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-11 10:51:46.414 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-11 10:51:46.414 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-11 10:51:46.414 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-11 10:51:46.414 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-11 10:51:46.414 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-11 10:51:46 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.415 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.459 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-11 10:51:46.459 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-11 10:51:46.475 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-11 10:51:46.475 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-11 10:51:46.475 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-11 10:51:46.475 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-11 10:51:46.499 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-11 10:51:46.499 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-11 10:51:46 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.499 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.500 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-11 10:51:46.500 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-11 10:51:46.500 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-11 10:51:46.500 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-11 10:51:46.500 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-11 10:51:46.500 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-11 10:51:46.501 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-11 10:51:46.501 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-11 10:51:46 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.501 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.501 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-11 10:51:46.503 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-11 10:51:46.504 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-11 10:51:46.504 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-11 10:51:46.504 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-11 10:51:46.504 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-11 10:51:46.504 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-11 10:51:46.504 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-11 10:51:46 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.505 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.506 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-11 10:51:46.506 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-11 10:51:46.506 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-11 10:51:46.508 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-11 10:51:46.508 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-11 10:51:46.508 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-11 10:51:46.509 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-11 10:51:46.509 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-11 10:51:46 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.509 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-11 10:51:46.624 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:46.625 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:46.653 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:46.658 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:46.659 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:46.659 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:46.660 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:46.660 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:46.660 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:46.670 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:48.429 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:48.429 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:48.927 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:48.927 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:48.929 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:48.929 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:48.932 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:48.933 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:48.933 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:48.933 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:48.933 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-11 10:51:48.934 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-11 10:51:48.934 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-11 10:51:48.934 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-11 10:51:48.934 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-11 10:51:48.934 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-11 10:51:48.934 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-11 10:51:48.934 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-11 10:51:48 [WARNING] [OKXExchange] 🚨 OKX API限速错误，4.0秒后重试 (尝试 2/4)
2025-08-11 10:51:48.934 [WARNING] [OKXExchange] 🚨 OKX API限速错误，4.0秒后重试 (尝试 2/4)
2025-08-11 10:51:48.957 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:48.957 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:49.430 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:49.430 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:49.435 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:49.435 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:49.440 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:49.440 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:49.450 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:49.453 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:51:53.040 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:51:53.040 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:51:53.527 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-11 10:51:53.527 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-11 10:52:12.354 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.354 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.355 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.355 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.357 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.357 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.358 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.358 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.360 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.360 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.360 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.360 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.362 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.362 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.362 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.362 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.365 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.365 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.365 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.365 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.367 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.368 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.368 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.368 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.370 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.370 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.370 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.371 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.372 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.372 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.373 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.373 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.375 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.375 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.375 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.376 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.377 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.378 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.378 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.378 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.379 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.379 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.380 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.380 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.382 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.382 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.382 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.382 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.384 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.385 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.385 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:12.385 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:12.937 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-11 10:52:13.209 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-11 10:52:13.238 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-11 10:52:13.239 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-11 10:52:13.240 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ERA-USDT
2025-08-11 10:52:13.240 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: TRUMP-USDT -> 最大杠杆=20x
2025-08-11 10:52:13.241 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-11 10:52:13.242 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: POPCAT-USDT -> 最大杠杆=20x
2025-08-11 10:52:13.242 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BONK-USDT -> 最大杠杆=20x
2025-08-11 10:52:13.243 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-11 10:52:13.244 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: PUMP-USDT -> 最大杠杆=20x
2025-08-11 10:52:13.285 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: PENGU-USDT -> 最大杠杆=20x
2025-08-11 10:52:13.321 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-11 10:52:13.898 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ERA-USDT
2025-08-11 10:52:15.006 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ERA-USDT
2025-08-11 10:52:39.207 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:39.212 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:40.187 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:40.204 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:41.260 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:41.260 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:42.445 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:42.445 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:43.704 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:43.704 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:44.819 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:44.820 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:45.953 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:45.953 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:46.944 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:46.945 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:47.996 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:47.998 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:49.074 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:49.074 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:50.236 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:50.236 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:51.397 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:51.399 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:52.395 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:52.395 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:53.416 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:53.416 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:54.458 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:54.459 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:55.548 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:55.550 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:56.604 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:56.606 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:57.716 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-11 10:52:57.717 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-11 10:52:58.640 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.640 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.641 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.641 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.643 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.643 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.643 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.644 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.646 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.646 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.646 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.646 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.648 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.648 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.649 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.649 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.651 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.651 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.651 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.651 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.653 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.653 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.653 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.653 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.655 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.655 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.656 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.656 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.658 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.658 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.658 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.658 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.660 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.660 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.661 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.661 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.663 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.663 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.663 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.663 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.666 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.666 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.666 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.666 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.668 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.668 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.669 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.669 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.671 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.671 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.671 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 30次/秒
2025-08-11 10:52:58 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-11 10:52:58.671 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=30次/秒
2025-08-11 10:52:58.997 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-11 10:52:59.181 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-11 10:52:59.515 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-11 10:52:59.522 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-11 10:52:59.537 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BONK-USDT -> 最大杠杆=20x
2025-08-11 10:52:59.538 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-11 10:52:59.554 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ERA-USDT
2025-08-11 10:52:59.555 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-11 10:52:59.555 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: TRUMP-USDT -> 最大杠杆=20x
2025-08-11 10:52:59.559 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: PENGU-USDT -> 最大杠杆=20x
2025-08-11 10:52:59.561 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: PUMP-USDT -> 最大杠杆=20x
2025-08-11 10:52:59.565 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: POPCAT-USDT -> 最大杠杆=20x
2025-08-11 10:52:59.565 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-11 10:53:01.341 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ERA-USDT
2025-08-11 10:53:02.588 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ERA-USDT
