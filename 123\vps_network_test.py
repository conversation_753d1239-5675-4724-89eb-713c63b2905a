#!/usr/bin/env python3
"""
🔥 VPS网络优化测试脚本
专门针对VPS环境测试网络优化效果，解决340ms延迟问题

使用方法：
python vps_network_test.py

测试内容：
1. 对比优化前后的API延迟
2. 验证重试机制效果
3. 测试连接池优化
4. 生成详细报告
"""

import asyncio
import time
import statistics
import json
import os
import sys
from typing import Dict, List, Tuple, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class VPSNetworkTester:
    """VPS网络优化测试器"""
    
    def __init__(self):
        self.test_endpoints = {
            "gate": "https://api.gateio.ws/api/v4/spot/time",
            "bybit": "https://api.bybit.com/v3/public/time",
            "okx": "https://www.okx.com/api/v5/public/time",
            "binance": "https://api.binance.com/api/v3/time",
            "bitget": "https://api.bitget.com/api/v2/public/time",
            "mexc": "https://api.mexc.com/api/v3/time"
        }
        
        self.results = {}
        
    async def test_old_method(self, exchange: str, url: str, count: int = 10) -> Dict:
        """测试优化前的方法（传统aiohttp）"""
        import aiohttp
        
        latencies = []
        success_count = 0
        errors = []
        
        print(f"  🔍 测试优化前方法 ({exchange})...")
        
        for i in range(count):
            try:
                start_time = time.time()
                
                # 传统方法：每次创建新session，长超时
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, timeout=aiohttp.ClientTimeout(total=30)) as resp:
                        if resp.status == 200:
                            await resp.json()
                            latency = (time.time() - start_time) * 1000
                            latencies.append(latency)
                            success_count += 1
                            print(f"    第{i+1}次: {latency:.1f}ms")
                        else:
                            errors.append(f"HTTP {resp.status}")
                            
            except Exception as e:
                errors.append(str(e)[:50])
                print(f"    第{i+1}次: 失败 - {str(e)[:30]}")
            
            await asyncio.sleep(0.1)  # 100ms间隔
        
        return {
            "latencies": latencies,
            "avg": statistics.mean(latencies) if latencies else 9999,
            "std": statistics.stdev(latencies) if len(latencies) > 1 else 0,
            "min": min(latencies) if latencies else 9999,
            "max": max(latencies) if latencies else 9999,
            "success_rate": success_count / count * 100,
            "errors": errors
        }
    
    async def test_new_method(self, exchange: str, url: str, count: int = 10) -> Dict:
        """测试优化后的方法（统一会话管理器+重试机制）"""
        try:
            from core.unified_http_session_manager import get_unified_session_manager
            session_manager = get_unified_session_manager()
        except ImportError:
            print(f"  ❌ 无法导入统一会话管理器，跳过优化测试")
            return {"error": "无法导入统一会话管理器"}
        
        latencies = []
        success_count = 0
        errors = []
        
        print(f"  🚀 测试优化后方法 ({exchange})...")
        
        for i in range(count):
            try:
                start_time = time.time()
                
                # 优化方法：统一会话管理器+重试机制
                result = await session_manager.fetch_with_retry(
                    exchange_name=exchange,
                    url=url,
                    method="GET"
                )
                
                if result:
                    latency = (time.time() - start_time) * 1000
                    latencies.append(latency)
                    success_count += 1
                    print(f"    第{i+1}次: {latency:.1f}ms ✅")
                else:
                    errors.append("重试机制失败")
                    print(f"    第{i+1}次: 重试失败")
                    
            except Exception as e:
                errors.append(str(e)[:50])
                print(f"    第{i+1}次: 异常 - {str(e)[:30]}")
            
            await asyncio.sleep(0.1)  # 100ms间隔
        
        return {
            "latencies": latencies,
            "avg": statistics.mean(latencies) if latencies else 9999,
            "std": statistics.stdev(latencies) if len(latencies) > 1 else 0,
            "min": min(latencies) if latencies else 9999,
            "max": max(latencies) if latencies else 9999,
            "success_rate": success_count / count * 100,
            "errors": errors
        }
    
    async def test_exchange(self, exchange: str, url: str) -> Dict:
        """测试单个交易所"""
        print(f"\n📊 测试 {exchange.upper()} 交易所")
        print(f"   API端点: {url}")
        
        # 测试优化前
        old_result = await self.test_old_method(exchange, url, count=15)
        
        # 等待一下
        await asyncio.sleep(1)
        
        # 测试优化后
        new_result = await self.test_new_method(exchange, url, count=15)
        
        # 计算改善效果
        if "error" not in new_result and old_result["avg"] > 0:
            improvement = (old_result["avg"] - new_result["avg"]) / old_result["avg"] * 100
            jitter_reduction = (old_result["std"] - new_result["std"]) / old_result["std"] * 100 if old_result["std"] > 0 else 0
        else:
            improvement = 0
            jitter_reduction = 0
        
        result = {
            "exchange": exchange,
            "url": url,
            "old": old_result,
            "new": new_result,
            "improvement_percent": improvement,
            "jitter_reduction_percent": jitter_reduction
        }
        
        return result
    
    def print_results(self, results: Dict):
        """打印测试结果"""
        print("\n" + "="*60)
        print("🔥 VPS网络优化测试结果")
        print("="*60)
        
        total_improvement = 0
        total_jitter_reduction = 0
        successful_tests = 0
        
        # 准备排名数据
        ranking_data = []
        
        for exchange, result in results.items():
            if "error" in result.get("new", {}):
                print(f"\n❌ {exchange.upper()}: 优化测试失败")
                continue
            
            old = result["old"]
            new = result["new"]
            
            # 添加到排名数据
            ranking_data.append({
                "exchange": exchange,
                "latency": new["avg"],
                "success_rate": new["success_rate"],
                "improvement": result["improvement_percent"]
            })
            
            print(f"\n📊 {exchange.upper()} 测试结果:")
            print(f"   优化前: {old['avg']:.1f}ms ± {old['std']:.1f}ms (成功率: {old['success_rate']:.1f}%)")
            print(f"   优化后: {new['avg']:.1f}ms ± {new['std']:.1f}ms (成功率: {new['success_rate']:.1f}%)")
            print(f"   延迟改善: {result['improvement_percent']:.1f}%")
            print(f"   抖动减少: {result['jitter_reduction_percent']:.1f}%")
            
            # 判断是否达到目标
            if new['avg'] < 100 and result['improvement_percent'] > 0:
                print(f"   ✅ 优化效果良好")
            elif result['improvement_percent'] > 20:
                print(f"   ✅ 有明显改善")
            else:
                print(f"   ⚠️ 改善有限")
            
            if old['errors']:
                print(f"   优化前错误: {len(old['errors'])}个")
            if new['errors']:
                print(f"   优化后错误: {len(new['errors'])}个")
            
            total_improvement += result['improvement_percent']
            total_jitter_reduction += result['jitter_reduction_percent']
            successful_tests += 1
        
        # 显示网络排名
        if ranking_data:
            print(f"\n" + "="*60)
            print("🏆 VPS网络性能排名 (按延迟从低到高)")
            print("="*60)
            
            # 按延迟排序
            ranking_data.sort(key=lambda x: x["latency"])
            
            for i, data in enumerate(ranking_data, 1):
                emoji = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                print(f"{emoji} {data['exchange'].upper():<8} - {data['latency']:.1f}ms (成功率: {data['success_rate']:.1f}%, 改善: {data['improvement']:.1f}%)")
        
        if successful_tests > 0:
            avg_improvement = total_improvement / successful_tests
            avg_jitter_reduction = total_jitter_reduction / successful_tests
            
            print(f"\n📈 总体效果:")
            print(f"   平均延迟改善: {avg_improvement:.1f}%")
            print(f"   平均抖动减少: {avg_jitter_reduction:.1f}%")
            
            if avg_improvement > 50:
                print(f"   🎉 优化效果显著！")
            elif avg_improvement > 20:
                print(f"   ✅ 优化效果良好")
            else:
                print(f"   ⚠️ 优化效果有限，可能需要进一步调整")
    
    def save_results(self, results: Dict, filename: str = "vps_network_test_results.json"):
        """保存测试结果到文件"""
        try:
            # 确保reports目录存在
            os.makedirs("reports", exist_ok=True)
            
            filepath = os.path.join("reports", filename)
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"\n📝 测试结果已保存到: {filepath}")
            
        except Exception as e:
            print(f"\n⚠️ 保存结果失败: {e}")
    
    async def run_test(self):
        """运行完整测试"""
        print("🚀 开始VPS网络优化测试")
        print(f"📊 测试范围: {len(self.test_endpoints)}个交易所")
        print("⏱️ 预计耗时: 2-3分钟")
        
        results = {}
        
        for exchange, url in self.test_endpoints.items():
            try:
                result = await self.test_exchange(exchange, url)
                results[exchange] = result
                
            except Exception as e:
                print(f"\n❌ {exchange.upper()} 测试异常: {e}")
                results[exchange] = {"error": str(e)}
        
        # 打印结果
        self.print_results(results)
        
        # 保存结果
        self.save_results(results)
        
        return results

async def main():
    """主函数"""
    print("🔥 VPS网络优化效果测试")
    print("=" * 40)
    
    tester = VPSNetworkTester()
    
    try:
        results = await tester.run_test()
        
        # 检查是否需要清理资源
        try:
            from core.unified_http_session_manager import get_unified_session_manager
            session_manager = get_unified_session_manager()
            await session_manager.close_all_sessions()
            print("\n✅ 资源清理完成")
        except:
            pass
        
        print("\n🎯 测试完成！")
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
