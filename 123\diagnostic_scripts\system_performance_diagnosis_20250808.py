#!/usr/bin/env python3
"""
🔍 专业日志审查与 Bug 定位诊断脚本
基于深度代码审查和实际验证结果的系统诊断
"""

import os
import json
import time
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging
import re
import statistics

class SystemPerformanceDiagnosisAnalyzer:
    """系统性能诊断分析器"""
    
    def __init__(self, logs_dir: str = None):
        self.logs_dir = logs_dir or "/root/myproject/123/79 优化了系统预热 大幅度优化性能/123/logs"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.diagnosis_results = {
            "timestamp": self.timestamp,
            "analysis_results": {},
            "critical_issues": [],
            "high_issues": [],
            "medium_issues": [],
            "recommendations": []
        }
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler(f'system_diagnosis_{self.timestamp}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def analyze_websocket_performance(self) -> Dict[str, Any]:
        """分析WebSocket性能问题"""
        self.logger.info("🔍 分析WebSocket性能问题...")
        
        perf_log_path = os.path.join(self.logs_dir, "websocket_performance_20250808.log")
        results = {
            "latency_violations": [],
            "timestamp_sync_issues": [],
            "message_delay_stats": {},
            "gate_futures_data_analysis": {}
        }
        
        if not os.path.exists(perf_log_path):
            self.logger.warning(f"⚠️ 性能日志文件不存在: {perf_log_path}")
            return results
            
        try:
            with open(perf_log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 分析消息延迟超标
            p99_violations = []
            p95_violations = []
            timestamp_sync_errors = []
            
            for line in lines:
                if "消息延迟超过P99标准" in line:
                    match = re.search(r"'latency_ms': ([\d.]+)", line)
                    if match:
                        p99_violations.append(float(match.group(1)))
                        
                elif "消息延迟超过P95标准" in line:
                    match = re.search(r"'latency_ms': ([\d.]+)", line)
                    if match:
                        p95_violations.append(float(match.group(1)))
                        
                elif "价格数据时间戳不同步" in line:
                    # 提取时间戳同步问题
                    if "gate_spot" in line or "gate_futures" in line:
                        match = re.search(r"time_diff_ms': (\d+)", line)
                        if match:
                            timestamp_sync_errors.append({
                                "time_diff_ms": int(match.group(1)),
                                "line": line.strip()
                            })
            
            # 统计分析
            if p99_violations:
                results["message_delay_stats"]["p99_violations"] = {
                    "count": len(p99_violations),
                    "avg_latency": statistics.mean(p99_violations),
                    "max_latency": max(p99_violations),
                    "min_latency": min(p99_violations)
                }
            
            if p95_violations:
                results["message_delay_stats"]["p95_violations"] = {
                    "count": len(p95_violations),
                    "avg_latency": statistics.mean(p95_violations),
                    "max_latency": max(p95_violations),
                    "min_latency": min(p95_violations)
                }
            
            results["timestamp_sync_issues"] = timestamp_sync_errors[:10]  # 保留前10个
            
            # Gate期货数据分析
            gate_futures_issues = [err for err in timestamp_sync_errors 
                                 if "gate" in err["line"] and "futures" in err["line"]]
            
            results["gate_futures_data_analysis"] = {
                "sync_errors_count": len(gate_futures_issues),
                "avg_time_diff": statistics.mean([err["time_diff_ms"] for err in gate_futures_issues]) if gate_futures_issues else 0,
                "analysis": "Gate期货数据时间戳同步问题严重" if len(gate_futures_issues) > 10 else "Gate期货数据同步正常"
            }
            
        except Exception as e:
            self.logger.error(f"❌ 分析WebSocket性能时出错: {e}")
            
        return results

    def analyze_api_errors(self) -> Dict[str, Any]:
        """分析API错误"""
        self.logger.info("🔍 分析API错误问题...")
        
        results = {
            "okx_rate_limit_errors": 0,
            "bybit_errors": [],
            "gate_errors": [],
            "critical_error_patterns": []
        }
        
        # 分析错误日志
        error_log_path = os.path.join(self.logs_dir, "error_20250808.log")
        if os.path.exists(error_log_path):
            with open(error_log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            for line in lines:
                if "Too Many Requests" in line and "okx" in line.lower():
                    results["okx_rate_limit_errors"] += 1
                elif "bybit" in line.lower() and "error" in line.lower():
                    results["bybit_errors"].append(line.strip())
                elif "gate" in line.lower() and "error" in line.lower():
                    results["gate_errors"].append(line.strip())
        
        # 分析具体交易所日志
        for exchange in ["OKXExchange", "bybit_exchange", "gate_exchange"]:
            log_path = os.path.join(self.logs_dir, f"{exchange}.log")
            if os.path.exists(log_path):
                with open(log_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if "Too Many Requests" in content:
                    results[f"{exchange}_rate_limit_count"] = content.count("Too Many Requests")
                    
                if "symbol invalid" in content:
                    results[f"{exchange}_invalid_symbols"] = content.count("symbol invalid")
        
        return results

    def analyze_precision_architecture_impact(self) -> Dict[str, Any]:
        """分析精度架构修复的影响"""
        self.logger.info("🔍 分析精度架构修复的影响...")
        
        results = {
            "precision_fixes_applied": True,
            "potential_side_effects": [],
            "performance_impact": {},
            "data_consistency_issues": []
        }
        
        # 基于分析审查报告的发现，检查可能的副作用
        results["potential_side_effects"] = [
            "WebSocket数据流精度转换修复可能影响实时数据处理",
            "ExecutionEngine精度修复可能影响订单执行",
            "UnifiedAmountCalculator修复可能影响交易量计算",
            "三交易所返回值统一可能导致类型不匹配"
        ]
        
        # 检查是否有数据不一致的迹象
        if self.diagnosis_results.get("analysis_results", {}).get("websocket_performance", {}).get("timestamp_sync_issues"):
            results["data_consistency_issues"].append("时间戳同步问题可能与精度修复相关")
        
        return results

    def simulate_failure_scenarios(self) -> Dict[str, Any]:
        """模拟失败场景"""
        self.logger.info("🔍 模拟失败场景...")
        
        scenarios = {
            "scenario_1_message_delay": {
                "description": "消息延迟超过P99标准导致套利机会丢失",
                "trigger_condition": "WebSocket消息处理延迟 > 25ms",
                "impact": "套利机会被系统丢弃，影响盈利能力",
                "frequency": "HIGH - 基于日志显示频繁发生"
            },
            
            "scenario_2_timestamp_sync": {
                "description": "三交易所时间戳不同步导致价格数据不可信",
                "trigger_condition": "交易所间时间差 > 1000ms",
                "impact": "价格套利计算不准确，可能导致错误交易",
                "frequency": "CRITICAL - 大量Gate相关组合受影响"
            },
            
            "scenario_3_api_rate_limit": {
                "description": "API限速错误导致交易执行失败",
                "trigger_condition": "OKX/Bybit API返回'Too Many Requests'",
                "impact": "无法获取实时数据或执行交易，系统功能受限",
                "frequency": "HIGH - OKX特别严重"
            },
            
            "scenario_4_precision_side_effects": {
                "description": "精度架构修复引入的副作用",
                "trigger_condition": "数据类型转换或格式不匹配",
                "impact": "可能导致WebSocket数据处理异常或API调用失败",
                "frequency": "UNKNOWN - 需要进一步监控"
            }
        }
        
        return scenarios

    def generate_recommendations(self) -> List[Dict[str, Any]]:
        """生成修复建议"""
        recommendations = [
            {
                "priority": "CRITICAL",
                "issue": "消息延迟超过P99标准",
                "recommendation": "优化WebSocket消息处理链路，减少延迟",
                "actions": [
                    "检查WebSocket消息处理管道是否有阻塞点",
                    "优化数据格式化和转换逻辑",
                    "考虑使用更高效的数据结构",
                    "增加异步处理能力"
                ]
            },
            
            {
                "priority": "CRITICAL", 
                "issue": "Gate期货数据时间戳同步问题",
                "recommendation": "修复Gate期货数据源时间戳同步机制",
                "actions": [
                    "检查Gate期货WebSocket连接状态",
                    "验证Gate期货数据接收链路",
                    "修复时间戳处理逻辑",
                    "增加数据新鲜度验证"
                ]
            },
            
            {
                "priority": "HIGH",
                "issue": "API限速错误频发", 
                "recommendation": "优化API调用频率和重试策略",
                "actions": [
                    "调整API调用间隔，特别是OKX",
                    "实现更智能的重试退避算法",
                    "增加API调用缓存机制",
                    "监控API配额使用情况"
                ]
            },
            
            {
                "priority": "HIGH",
                "issue": "精度架构修复潜在副作用",
                "recommendation": "验证精度修复后的系统一致性",
                "actions": [
                    "全面测试WebSocket数据处理流程",
                    "验证API调用参数格式正确性",
                    "检查数据类型转换的一致性",
                    "增加精度相关的监控指标"
                ]
            },
            
            {
                "priority": "MEDIUM",
                "issue": "交易对支持状态检查",
                "recommendation": "动态检查和过滤不支持的交易对",
                "actions": [
                    "实现交易对可用性检查",
                    "过滤已关闭或不支持的合约",
                    "增加交易对状态监控",
                    "动态更新支持的交易对列表"
                ]
            }
        ]
        
        return recommendations

    async def run_comprehensive_diagnosis(self) -> Dict[str, Any]:
        """运行综合诊断"""
        self.setup_logging()
        self.logger.info("🚀 开始系统综合诊断...")
        
        # 执行各项分析
        websocket_analysis = self.analyze_websocket_performance()
        api_errors_analysis = self.analyze_api_errors()
        precision_impact_analysis = self.analyze_precision_architecture_impact()
        failure_scenarios = self.simulate_failure_scenarios()
        recommendations = self.generate_recommendations()
        
        # 汇总结果
        self.diagnosis_results.update({
            "analysis_results": {
                "websocket_performance": websocket_analysis,
                "api_errors": api_errors_analysis,
                "precision_impact": precision_impact_analysis,
                "failure_scenarios": failure_scenarios
            },
            "recommendations": recommendations
        })
        
        # 分类问题严重程度
        self.classify_issues()
        
        # 保存结果
        self.save_diagnosis_results()
        
        self.logger.info("✅ 系统诊断完成")
        return self.diagnosis_results
    
    def classify_issues(self):
        """分类问题严重程度"""
        # CRITICAL级别问题
        websocket_perf = self.diagnosis_results["analysis_results"]["websocket_performance"]
        if websocket_perf.get("message_delay_stats", {}).get("p99_violations", {}).get("count", 0) > 5:
            self.diagnosis_results["critical_issues"].append("消息延迟超过P99标准次数过多")
            
        if websocket_perf.get("gate_futures_data_analysis", {}).get("sync_errors_count", 0) > 10:
            self.diagnosis_results["critical_issues"].append("Gate期货数据时间戳同步问题严重")
        
        # HIGH级别问题
        api_errors = self.diagnosis_results["analysis_results"]["api_errors"]
        if api_errors.get("okx_rate_limit_errors", 0) > 10:
            self.diagnosis_results["high_issues"].append("OKX API限速错误频发")
            
        if len(api_errors.get("bybit_errors", [])) > 5:
            self.diagnosis_results["high_issues"].append("Bybit API错误较多")
        
        # MEDIUM级别问题
        if api_errors.get("OKXExchange_invalid_symbols", 0) > 0:
            self.diagnosis_results["medium_issues"].append("存在无效的交易对符号")
    
    def save_diagnosis_results(self):
        """保存诊断结果"""
        output_file = f"system_diagnosis_results_{self.timestamp}.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.diagnosis_results, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"📋 诊断结果已保存至: {output_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存诊断结果失败: {e}")

def main():
    """主函数"""
    print("🔍 系统性能诊断分析器")
    print("=" * 50)
    
    analyzer = SystemPerformanceDiagnosisAnalyzer()
    
    # 运行异步诊断
    try:
        results = asyncio.run(analyzer.run_comprehensive_diagnosis())
        
        # 输出关键发现
        print("\n📊 关键发现:")
        print(f"CRITICAL级别问题: {len(results['critical_issues'])}")
        for issue in results['critical_issues']:
            print(f"  🚨 {issue}")
            
        print(f"HIGH级别问题: {len(results['high_issues'])}")
        for issue in results['high_issues']:
            print(f"  ⚠️ {issue}")
            
        print(f"MEDIUM级别问题: {len(results['medium_issues'])}")
        for issue in results['medium_issues']:
            print(f"  💡 {issue}")
        
        print(f"\n📋 生成了 {len(results['recommendations'])} 条修复建议")
        print("详细结果请查看生成的JSON文件")
        
    except Exception as e:
        print(f"❌ 诊断过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())