#!/usr/bin/env python3
"""
🧹 全面缓存清理工具
删除所有类型的缓存文件和文件夹

优化功能：
1. 🐍 Python缓存: __pycache__, *.pyc, *.pyo
2. 📝 日志文件: *.log
3. 🗂️ 临时文件: *.tmp, *.bak, *~, *.swp
4. 🧪 测试缓存: .pytest_cache, .coverage, .mypy_cache
5. 💻 IDE缓存: .vscode, .idea, .vs
6. 🖥️ 系统缓存: .DS_Store, Thumbs.db
7. 🔨 构建缓存: build/, dist/, *.egg-info, node_modules/
"""

import os
import shutil
import time

def get_directory_size(path):
    """获取目录大小"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            try:
                total_size += os.path.getsize(filepath)
            except:
                pass
    return total_size

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.2f} {size_names[i]}"

def should_delete_file(filename):
    """判断文件是否应该删除"""
    # Python缓存
    if filename.endswith(('.pyc', '.pyo', '.pyd')):
        return True
    
    # 日志文件
    if filename.endswith(('.log', '.LOG')):
        return True
    
    # 临时文件
    temp_extensions = ['.tmp', '.temp', '.TMP', '.TEMP', '.bak', '.backup', '.BAK']
    if any(filename.endswith(ext) for ext in temp_extensions):
        return True
    
    # 编辑器临时文件
    if filename.endswith(('~', '.swp', '.swo')) or filename.startswith('.#'):
        return True
    
    # 系统缓存文件
    system_files = ['.DS_Store', 'Thumbs.db', 'desktop.ini', '.directory']
    if filename in system_files:
        return True
    
    # 构建文件
    if filename.endswith(('.whl', '.egg')):
        return True
    
    # IDE工作区文件
    if filename.endswith(('.code-workspace', '.sublime-project', '.sublime-workspace')):
        return True
    
    # 测试覆盖率文件
    if filename in ['coverage.xml'] or filename.startswith('.coverage'):
        return True
    
    return False

def should_delete_directory(dirname):
    """判断目录是否应该删除"""
    # Python缓存目录
    if dirname == '__pycache__':
        return True
    
    # 测试缓存目录
    test_cache_dirs = ['.pytest_cache', '.mypy_cache', '.tox', '.cache', 'htmlcov']
    if dirname in test_cache_dirs:
        return True
    
    # IDE缓存目录
    ide_dirs = ['.vscode', '.idea', '.vs']
    if dirname in ide_dirs:
        return True
    
    # 构建目录
    build_dirs = ['build', 'dist', '.eggs', 'node_modules', '.npm', '.yarn']
    if dirname in build_dirs:
        return True
    
    # egg-info目录
    if dirname.endswith('.egg-info'):
        return True
    
    return False

def clean_all_cache():
    """清理所有缓存"""
    print("🧹 全面缓存清理工具")
    print("=" * 70)
    
    start_time = time.time()
    initial_size = get_directory_size('.')
    print(f"📊 清理前项目大小: {format_size(initial_size)}")
    
    deleted_files = 0
    deleted_dirs = 0
    
    print(f"\n🚀 开始扫描和清理...")
    
    # 收集要删除的项目
    files_to_delete = []
    dirs_to_delete = []
    
    for root, dirs, files in os.walk('.', topdown=False):  # topdown=False确保先处理子目录
        # 收集要删除的文件
        for filename in files:
            if should_delete_file(filename):
                filepath = os.path.join(root, filename)
                files_to_delete.append(filepath)
        
        # 收集要删除的目录
        for dirname in dirs:
            if should_delete_directory(dirname):
                dirpath = os.path.join(root, dirname)
                dirs_to_delete.append(dirpath)
    
    # 删除文件
    if files_to_delete:
        print(f"\n📄 删除 {len(files_to_delete)} 个缓存文件:")
        for filepath in files_to_delete:
            try:
                os.remove(filepath)
                print(f"   ✅ {filepath}")
                deleted_files += 1
            except Exception as e:
                print(f"   ❌ {filepath} - {e}")
    else:
        print(f"\n📄 未找到需要删除的缓存文件")
    
    # 删除目录
    if dirs_to_delete:
        print(f"\n📁 删除 {len(dirs_to_delete)} 个缓存目录:")
        for dirpath in dirs_to_delete:
            try:
                shutil.rmtree(dirpath)
                print(f"   ✅ {dirpath}")
                deleted_dirs += 1
            except Exception as e:
                print(f"   ❌ {dirpath} - {e}")
    else:
        print(f"\n📁 未找到需要删除的缓存目录")
    
    # 最终结果
    final_size = get_directory_size('.')
    saved_size = initial_size - final_size
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "=" * 70)
    print("📊 清理结果:")
    print(f"   清理前大小: {format_size(initial_size)}")
    print(f"   清理后大小: {format_size(final_size)}")
    print(f"   节省空间: {format_size(saved_size)}")
    print(f"   删除文件: {deleted_files} 个")
    print(f"   删除目录: {deleted_dirs} 个")
    print(f"   清理耗时: {duration:.2f} 秒")
    
    if saved_size > 0:
        print(f"   🎉 清理完成！节省了 {format_size(saved_size)} 空间")
    else:
        print("   ✅ 项目已经很干净了！")
    
    # 清理类型说明
    print(f"\n📋 清理的缓存类型:")
    print(f"   🐍 Python缓存: __pycache__, *.pyc, *.pyo")
    print(f"   📝 日志文件: *.log")
    print(f"   🗂️ 临时文件: *.tmp, *.bak, *~, *.swp")
    print(f"   🧪 测试缓存: .pytest_cache, .coverage, .mypy_cache")
    print(f"   💻 IDE缓存: .vscode, .idea, .vs")
    print(f"   🖥️ 系统缓存: .DS_Store, Thumbs.db")
    print(f"   🔨 构建缓存: build/, dist/, *.egg-info, node_modules/")
    
    print("=" * 70)

if __name__ == "__main__":
    try:
        clean_all_cache()
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断清理过程")
    except Exception as e:
        print(f"\n❌ 清理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
