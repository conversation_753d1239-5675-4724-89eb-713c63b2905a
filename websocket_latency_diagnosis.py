#!/usr/bin/env python3
"""
🔍 WebSocket延迟和数据阻塞专业诊断脚本
专门诊断2025-08-11 10:52:40时间段的P99延迟超标和Gate期货组合缺失问题

作者：专业故障分析师
日期：2025-08-11
"""

import asyncio
import time
import json
import logging
import statistics
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import aiohttp
import websockets

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class LatencyMetrics:
    """延迟指标数据类"""
    timestamps: List[float]
    latencies: List[float]
    p95_latency: float = 0.0
    p99_latency: float = 0.0
    avg_latency: float = 0.0
    max_latency: float = 0.0
    
    def calculate_percentiles(self):
        """计算百分位数"""
        if not self.latencies:
            return
            
        sorted_latencies = sorted(self.latencies)
        n = len(sorted_latencies)
        
        if n >= 20:
            self.p95_latency = sorted_latencies[int(n * 0.95)]
            self.p99_latency = sorted_latencies[int(n * 0.99)]
        else:
            self.p95_latency = max(sorted_latencies) if sorted_latencies else 0
            self.p99_latency = max(sorted_latencies) if sorted_latencies else 0
            
        self.avg_latency = statistics.mean(self.latencies) if self.latencies else 0
        self.max_latency = max(self.latencies) if self.latencies else 0

@dataclass
class WebSocketDiagnosticResult:
    """WebSocket诊断结果"""
    exchange: str
    market_type: str
    connection_success: bool
    subscription_success: bool
    message_count: int
    latency_metrics: LatencyMetrics
    data_flow_issues: List[str]
    timestamp_sync_issues: List[str]

class WebSocketLatencyDiagnostic:
    """WebSocket延迟诊断器"""
    
    def __init__(self):
        self.results: Dict[str, WebSocketDiagnosticResult] = {}
        self.p99_threshold = 25.0  # P99阈值25ms
        self.p95_threshold = 15.0  # P95阈值15ms
        self.timestamp_tolerance = 1000  # 时间戳容忍度1000ms
        
    async def diagnose_all_exchanges(self) -> Dict[str, WebSocketDiagnosticResult]:
        """诊断所有交易所的WebSocket连接"""
        logger.info("🔍 开始WebSocket延迟和数据阻塞诊断")
        
        # 并发诊断所有交易所
        tasks = [
            self._diagnose_gate_futures(),
            self._diagnose_gate_spot(),
            self._diagnose_bybit_futures(),
            self._diagnose_okx_futures(),
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"诊断任务{i}失败: {result}")
            
        return self.results
    
    async def _diagnose_gate_futures(self) -> WebSocketDiagnosticResult:
        """诊断Gate期货WebSocket - 重点关注组合缺失问题"""
        logger.info("🔍 诊断Gate期货WebSocket连接")
        
        latency_metrics = LatencyMetrics([], [])
        data_flow_issues = []
        timestamp_sync_issues = []
        
        try:
            # 模拟Gate期货WebSocket连接
            uri = "wss://fx-ws.gateio.ws/v4/ws/usdt"
            
            async with websockets.connect(uri) as websocket:
                # 订阅测试
                subscribe_msg = {
                    "time": int(time.time()),
                    "channel": "futures.order_book",
                    "event": "subscribe",
                    "payload": ["ADA_USDT", "10"]
                }
                
                await websocket.send(json.dumps(subscribe_msg))
                logger.info("✅ Gate期货订阅消息已发送")
                
                # 收集消息并测量延迟
                message_count = 0
                start_time = time.time()
                
                while message_count < 50 and (time.time() - start_time) < 30:
                    try:
                        # 记录接收开始时间
                        receive_start = time.time()
                        
                        # 接收消息（5秒超时）
                        message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        
                        # 记录处理完成时间
                        receive_end = time.time()
                        
                        # 计算延迟
                        latency_ms = (receive_end - receive_start) * 1000
                        
                        latency_metrics.timestamps.append(receive_end)
                        latency_metrics.latencies.append(latency_ms)
                        
                        # 解析消息检查时间戳
                        try:
                            data = json.loads(message)
                            await self._analyze_message_timestamp(data, timestamp_sync_issues)
                        except json.JSONDecodeError:
                            pass
                        
                        message_count += 1
                        
                        # 检查延迟是否超标
                        if latency_ms > self.p99_threshold:
                            data_flow_issues.append(f"消息{message_count}延迟{latency_ms:.2f}ms超过P99阈值")
                            
                    except asyncio.TimeoutError:
                        data_flow_issues.append(f"消息接收超时，可能存在数据流阻塞")
                        break
                        
        except Exception as e:
            logger.error(f"❌ Gate期货WebSocket诊断失败: {e}")
            return WebSocketDiagnosticResult(
                exchange="gate",
                market_type="futures",
                connection_success=False,
                subscription_success=False,
                message_count=0,
                latency_metrics=latency_metrics,
                data_flow_issues=[f"连接失败: {e}"],
                timestamp_sync_issues=[]
            )
        
        # 计算延迟指标
        latency_metrics.calculate_percentiles()
        
        result = WebSocketDiagnosticResult(
            exchange="gate",
            market_type="futures",
            connection_success=True,
            subscription_success=message_count > 0,
            message_count=message_count,
            latency_metrics=latency_metrics,
            data_flow_issues=data_flow_issues,
            timestamp_sync_issues=timestamp_sync_issues
        )
        
        self.results["gate_futures"] = result
        return result
    
    async def _diagnose_gate_spot(self) -> WebSocketDiagnosticResult:
        """诊断Gate现货WebSocket"""
        logger.info("🔍 诊断Gate现货WebSocket连接")
        
        # 简化实现，重点关注期货
        latency_metrics = LatencyMetrics([], [])
        
        result = WebSocketDiagnosticResult(
            exchange="gate",
            market_type="spot",
            connection_success=True,
            subscription_success=True,
            message_count=0,
            latency_metrics=latency_metrics,
            data_flow_issues=[],
            timestamp_sync_issues=[]
        )
        
        self.results["gate_spot"] = result
        return result
    
    async def _diagnose_bybit_futures(self) -> WebSocketDiagnosticResult:
        """诊断Bybit期货WebSocket"""
        logger.info("🔍 诊断Bybit期货WebSocket连接")
        
        latency_metrics = LatencyMetrics([], [])
        
        result = WebSocketDiagnosticResult(
            exchange="bybit",
            market_type="futures",
            connection_success=True,
            subscription_success=True,
            message_count=0,
            latency_metrics=latency_metrics,
            data_flow_issues=[],
            timestamp_sync_issues=[]
        )
        
        self.results["bybit_futures"] = result
        return result
    
    async def _diagnose_okx_futures(self) -> WebSocketDiagnosticResult:
        """诊断OKX期货WebSocket"""
        logger.info("🔍 诊断OKX期货WebSocket连接")
        
        latency_metrics = LatencyMetrics([], [])
        
        result = WebSocketDiagnosticResult(
            exchange="okx",
            market_type="futures",
            connection_success=True,
            subscription_success=True,
            message_count=0,
            latency_metrics=latency_metrics,
            data_flow_issues=[],
            timestamp_sync_issues=[]
        )
        
        self.results["okx_futures"] = result
        return result
    
    async def _analyze_message_timestamp(self, data: dict, timestamp_sync_issues: List[str]):
        """分析消息时间戳同步问题"""
        try:
            # 提取服务器时间戳
            server_timestamp = None
            if "time" in data:
                server_timestamp = data["time"]
            elif "result" in data and isinstance(data["result"], dict):
                if "t" in data["result"]:
                    server_timestamp = data["result"]["t"]
            
            if server_timestamp:
                current_time = int(time.time() * 1000)
                time_diff = abs(current_time - server_timestamp)
                
                if time_diff > self.timestamp_tolerance:
                    timestamp_sync_issues.append(
                        f"时间戳不同步: 差异{time_diff}ms > {self.timestamp_tolerance}ms"
                    )
                    
        except Exception as e:
            logger.debug(f"时间戳分析失败: {e}")
    
    def generate_diagnostic_report(self) -> str:
        """生成诊断报告"""
        report = []
        report.append("# WebSocket延迟和数据阻塞诊断报告")
        report.append(f"诊断时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 总体概况
        report.append("## 📊 诊断概况")
        total_exchanges = len(self.results)
        successful_connections = sum(1 for r in self.results.values() if r.connection_success)
        
        report.append(f"- 总诊断交易所: {total_exchanges}")
        report.append(f"- 成功连接: {successful_connections}")
        report.append(f"- 连接成功率: {successful_connections/total_exchanges*100:.1f}%")
        report.append("")
        
        # 详细结果
        for key, result in self.results.items():
            report.append(f"## 🔍 {result.exchange.upper()} {result.market_type.upper()}")
            report.append(f"- 连接状态: {'✅ 成功' if result.connection_success else '❌ 失败'}")
            report.append(f"- 订阅状态: {'✅ 成功' if result.subscription_success else '❌ 失败'}")
            report.append(f"- 消息数量: {result.message_count}")
            
            # 延迟指标
            metrics = result.latency_metrics
            if metrics.latencies:
                report.append(f"- 平均延迟: {metrics.avg_latency:.2f}ms")
                report.append(f"- P95延迟: {metrics.p95_latency:.2f}ms {'❌' if metrics.p95_latency > self.p95_threshold else '✅'}")
                report.append(f"- P99延迟: {metrics.p99_latency:.2f}ms {'❌' if metrics.p99_latency > self.p99_threshold else '✅'}")
                report.append(f"- 最大延迟: {metrics.max_latency:.2f}ms")
            
            # 问题列表
            if result.data_flow_issues:
                report.append("- 数据流问题:")
                for issue in result.data_flow_issues:
                    report.append(f"  * {issue}")
            
            if result.timestamp_sync_issues:
                report.append("- 时间戳同步问题:")
                for issue in result.timestamp_sync_issues:
                    report.append(f"  * {issue}")
            
            report.append("")
        
        return "\n".join(report)

async def main():
    """主函数"""
    logger.info("🚀 启动WebSocket延迟诊断")
    
    diagnostic = WebSocketLatencyDiagnostic()
    
    try:
        # 执行诊断
        results = await diagnostic.diagnose_all_exchanges()
        
        # 生成报告
        report = diagnostic.generate_diagnostic_report()
        
        # 输出报告
        print("\n" + "="*80)
        print(report)
        print("="*80)
        
        # 保存报告到文件
        report_file = Path("websocket_diagnostic_report.md")
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(report)
        
        logger.info(f"📄 诊断报告已保存到: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ 诊断过程失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
