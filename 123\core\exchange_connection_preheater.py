#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 交易所连接预热器 - 解决4.7秒初始化瓶颈
通用系统支持任意代币，消除初始化延迟

核心功能：
1. 🚀 启动时预热所有交易所连接
2. 🔄 连接池复用机制
3. ⚡ 零延迟获取已预热连接
4. 🌍 支持所有代币，零硬编码
"""

import asyncio
import time
import os
import logging
from typing import Dict, Optional, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import get_logger


class ConnectionStatus(Enum):
    """连接状态枚举"""
    COLD = "cold"               # 冷连接：未初始化
    PREHEATING = "preheating"   # 预热中：正在初始化
    HOT = "hot"                 # 热连接：已就绪
    ERROR = "error"             # 错误状态


@dataclass
class PreheatedConnection:
    """预热连接数据类"""
    exchange_name: str
    exchange_instance: Any
    status: ConnectionStatus
    preheat_start_time: Optional[float] = None
    preheat_end_time: Optional[float] = None
    preheat_duration_ms: Optional[float] = None
    last_used_time: Optional[float] = None
    use_count: int = 0
    error_message: Optional[str] = None


class ExchangeConnectionPreheater:
    """
    🔥 交易所连接预热器
    
    解决4.7秒初始化瓶颈的核心组件：
    - 启动时并行预热所有交易所连接
    - 运行时零延迟获取预热连接
    - 支持任意代币，无硬编码限制
    """

    def __init__(self):
        self.logger = get_logger("ExchangeConnectionPreheater")
        
        # 预热连接池
        self.preheated_connections: Dict[str, PreheatedConnection] = {}
        
        # 预热状态
        self.preheating_in_progress = False
        self.preheating_start_time: Optional[float] = None
        self.preheating_complete_time: Optional[float] = None
        
        # 配置
        self.max_preheat_time_seconds = float(os.getenv("MAX_PREHEAT_TIME_SECONDS", "10.0"))
        self.connection_check_interval = float(os.getenv("CONNECTION_CHECK_INTERVAL", "0.5"))
        
        self.logger.info("🔥 交易所连接预热器初始化完成")
        self.logger.info(f"   最大预热时间: {self.max_preheat_time_seconds}秒")
        self.logger.info(f"   连接检查间隔: {self.connection_check_interval}秒")

    async def start_preheating_all_exchanges(self) -> bool:
        """
        🚀 启动所有交易所连接预热
        这是解决4.7秒瓶颈的核心方法
        """
        try:
            if self.preheating_in_progress:
                self.logger.warning("⚠️ 连接预热已在进行中")
                return False

            self.logger.info("=== 🔥 开始交易所连接预热 ===")
            self.preheating_in_progress = True
            self.preheating_start_time = time.time()
            
            # 获取所有需要预热的交易所
            exchanges_to_preheat = self._get_exchanges_config()
            
            if not exchanges_to_preheat:
                self.logger.error("❌ 无可用交易所配置")
                return False
            
            self.logger.info(f"📋 将预热 {len(exchanges_to_preheat)} 个交易所")
            for exchange_name in exchanges_to_preheat.keys():
                self.logger.info(f"   - {exchange_name}")
            
            # 并行预热所有交易所
            preheat_tasks = []
            for exchange_name, config in exchanges_to_preheat.items():
                task = asyncio.create_task(
                    self._preheat_single_exchange(exchange_name, config),
                    name=f"preheat_{exchange_name}"
                )
                preheat_tasks.append(task)
            
            # 等待所有预热完成
            preheat_results = await asyncio.gather(*preheat_tasks, return_exceptions=True)
            
            # 统计预热结果
            successful_count = 0
            for i, (exchange_name, result) in enumerate(zip(exchanges_to_preheat.keys(), preheat_results)):
                if isinstance(result, Exception):
                    self.logger.error(f"❌ {exchange_name} 预热异常: {result}")
                    self._mark_connection_error(exchange_name, str(result))
                elif result:
                    successful_count += 1
                    self.logger.info(f"✅ {exchange_name} 预热成功")
                else:
                    self.logger.error(f"❌ {exchange_name} 预热失败")
                    self._mark_connection_error(exchange_name, "预热失败")
            
            self.preheating_complete_time = time.time()
            total_preheat_time = (self.preheating_complete_time - self.preheating_start_time) * 1000
            
            self.logger.info("=== 🔥 交易所连接预热完成 ===")
            self.logger.info(f"📊 成功预热: {successful_count}/{len(exchanges_to_preheat)} 个交易所")
            self.logger.info(f"📊 总预热时间: {total_preheat_time:.1f}ms")
            
            if successful_count > 0:
                self.logger.info(f"✅ 预热成功！运行时可零延迟获取连接")
                return True
            else:
                self.logger.error(f"❌ 所有交易所预热失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 预热过程异常: {e}")
            return False
        finally:
            self.preheating_in_progress = False

    async def _preheat_single_exchange(self, exchange_name: str, config: Dict[str, Any]) -> bool:
        """
        🔥 预热单个交易所连接
        """
        preheat_start = time.time()
        
        try:
            self.logger.info(f"🔥 开始预热 {exchange_name} 交易所...")
            
            # 创建预热连接记录
            connection = PreheatedConnection(
                exchange_name=exchange_name,
                exchange_instance=None,
                status=ConnectionStatus.PREHEATING,
                preheat_start_time=preheat_start
            )
            self.preheated_connections[exchange_name] = connection
            
            # 创建交易所实例
            exchange_instance = await self._create_exchange_instance(exchange_name, config)
            
            if not exchange_instance:
                self.logger.error(f"❌ {exchange_name} 交易所实例创建失败")
                return False
            
            # 初始化交易所连接 - 🔥 修复：Gate.io特殊处理避免预热阶段403错误
            if exchange_name.lower() == "gate":
                init_success = await self._preheat_gate_with_retry(exchange_instance)
            else:
                init_success = await exchange_instance.initialize()
            
            if init_success:
                preheat_end = time.time()
                preheat_duration = (preheat_end - preheat_start) * 1000
                
                # 更新连接状态
                connection.exchange_instance = exchange_instance
                connection.status = ConnectionStatus.HOT
                connection.preheat_end_time = preheat_end
                connection.preheat_duration_ms = preheat_duration
                
                self.logger.info(f"✅ {exchange_name} 预热成功: {preheat_duration:.1f}ms")
                return True
            else:
                self.logger.error(f"❌ {exchange_name} 初始化失败")
                self._mark_connection_error(exchange_name, "初始化失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ {exchange_name} 预热异常: {e}")
            self._mark_connection_error(exchange_name, str(e))
            return False

    async def _create_exchange_instance(self, exchange_name: str, config: Dict[str, Any]):
        """
        🔥 创建交易所实例 - 支持所有交易所
        """
        try:
            if exchange_name == "gate":
                from exchanges.gate_exchange import GateExchange
                return GateExchange(config["api_key"], config["api_secret"])
            elif exchange_name == "bybit":
                from exchanges.bybit_exchange import BybitExchange
                return BybitExchange(config["api_key"], config["api_secret"])
            elif exchange_name == "okx":
                from exchanges.okx_exchange import OKXExchange
                return OKXExchange(config["api_key"], config["api_secret"], config["passphrase"])
            else:
                self.logger.error(f"❌ 不支持的交易所: {exchange_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ 创建 {exchange_name} 实例异常: {e}")
            return None

    def _get_exchanges_config(self) -> Dict[str, Dict[str, Any]]:
        """
        🔥 获取交易所配置 - 从环境变量读取
        """
        exchanges_config = {}
        
        # Gate.io配置
        gate_api_key = os.getenv("GATE_API_KEY")
        gate_api_secret = os.getenv("GATE_API_SECRET")
        if gate_api_key and gate_api_secret:
            exchanges_config["gate"] = {
                "api_key": gate_api_key,
                "api_secret": gate_api_secret
            }
        
        # Bybit配置
        bybit_api_key = os.getenv("BYBIT_API_KEY")
        bybit_api_secret = os.getenv("BYBIT_API_SECRET")
        if bybit_api_key and bybit_api_secret:
            exchanges_config["bybit"] = {
                "api_key": bybit_api_key,
                "api_secret": bybit_api_secret
            }
        
        # OKX配置
        okx_api_key = os.getenv("OKX_API_KEY")
        okx_api_secret = os.getenv("OKX_API_SECRET")
        okx_passphrase = os.getenv("OKX_API_PASSPHRASE")
        if okx_api_key and okx_api_secret and okx_passphrase:
            exchanges_config["okx"] = {
                "api_key": okx_api_key,
                "api_secret": okx_api_secret,
                "passphrase": okx_passphrase
            }
        
        return exchanges_config

    def get_preheated_exchange(self, exchange_name: str) -> Optional[Any]:
        """
        ⚡ 零延迟获取预热连接
        这是解决4.7秒瓶颈的关键方法
        """
        connection = self.preheated_connections.get(exchange_name)
        
        if not connection:
            self.logger.warning(f"⚠️ {exchange_name} 未找到预热连接")
            return None
        
        if connection.status != ConnectionStatus.HOT:
            self.logger.warning(f"⚠️ {exchange_name} 连接未就绪，状态: {connection.status}")
            return None
        
        # 更新使用统计
        connection.last_used_time = time.time()
        connection.use_count += 1
        
        self.logger.info(f"⚡ 零延迟获取 {exchange_name} 预热连接 (使用次数: {connection.use_count})")
        return connection.exchange_instance

    def get_all_preheated_exchanges(self) -> Dict[str, Any]:
        """
        ⚡ 零延迟获取所有预热连接
        """
        exchanges = {}
        
        for exchange_name, connection in self.preheated_connections.items():
            if connection.status == ConnectionStatus.HOT and connection.exchange_instance:
                exchanges[exchange_name] = connection.exchange_instance
                
                # 更新使用统计
                connection.last_used_time = time.time()
                connection.use_count += 1
        
        self.logger.info(f"⚡ 零延迟获取 {len(exchanges)} 个预热连接")
        return exchanges

    async def _preheat_gate_with_retry(self, exchange_instance) -> bool:
        """
        Gate.io专用预热方法，避免并发冲突
        🔥 处理Gate.io分离账户模式的API频率限制
        """
        for attempt in range(3):
            try:
                self.logger.info(f"🔥 Gate.io预热尝试 {attempt + 1}/3...")
                
                # 使用修复后的initialize方法（内含延迟和重试机制）
                success = await exchange_instance.initialize()
                
                if success:
                    self.logger.info("✅ Gate.io预热成功")
                    return True
                else:
                    self.logger.warning(f"⚠️ Gate.io预热失败（第{attempt + 1}次）")
                    
            except Exception as e:
                error_msg = str(e).lower()
                
                if ("permission not open yet" in error_msg or 
                    "forbidden" in error_msg) and attempt < 2:
                    
                    # 渐进延迟: 0.2s, 0.4s
                    delay = 0.2 * (attempt + 1)
                    self.logger.warning(f"⚠️ Gate.io预热遇到权限限制，{delay}s后重试: {e}")
                    await asyncio.sleep(delay)
                    continue
                else:
                    self.logger.error(f"❌ Gate.io预热失败: {e}")
                    return False
                    
        self.logger.error("❌ Gate.io预热最终失败（已尝试3次）")
        return False

    def _mark_connection_error(self, exchange_name: str, error_message: str):
        """标记连接错误状态"""
        if exchange_name in self.preheated_connections:
            connection = self.preheated_connections[exchange_name]
            connection.status = ConnectionStatus.ERROR
            connection.error_message = error_message

    def get_preheating_status(self) -> Dict[str, Any]:
        """获取预热状态统计"""
        status_count = {}
        for status in ConnectionStatus:
            status_count[status.value] = 0
        
        for connection in self.preheated_connections.values():
            status_count[connection.status.value] += 1
        
        total_preheat_time = None
        if self.preheating_start_time and self.preheating_complete_time:
            total_preheat_time = (self.preheating_complete_time - self.preheating_start_time) * 1000
        
        return {
            "preheating_in_progress": self.preheating_in_progress,
            "total_exchanges": len(self.preheated_connections),
            "status_breakdown": status_count,
            "total_preheat_time_ms": total_preheat_time,
            "hot_connections": [
                name for name, conn in self.preheated_connections.items() 
                if conn.status == ConnectionStatus.HOT
            ]
        }

    def is_preheating_complete(self) -> bool:
        """检查预热是否完成"""
        return not self.preheating_in_progress and len([
            conn for conn in self.preheated_connections.values() 
            if conn.status == ConnectionStatus.HOT
        ]) > 0


# 🔥 全局实例管理
_connection_preheater = None

def get_connection_preheater() -> ExchangeConnectionPreheater:
    """获取连接预热器全局实例"""
    global _connection_preheater
    if _connection_preheater is None:
        _connection_preheater = ExchangeConnectionPreheater()
    return _connection_preheater

def reset_connection_preheater():
    """重置连接预热器全局实例"""
    global _connection_preheater
    _connection_preheater = None