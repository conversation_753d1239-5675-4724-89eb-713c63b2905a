# 🔥 **统一杠杆管理器 - 三交易所统一实现**
"""
统一杠杆管理器 - 解决Gate.io杠杆设置问题和重复实现问题
- 支持Gate.io、Bybit、OKX三个交易所
- 使用正确的API格式调用
- 统一的重试机制和错误处理
- 零重复代码，完全统一实现
"""

import asyncio
import logging
import os
import time
from typing import Dict, Optional, List, Any
from exchanges.currency_adapter import get_exchange_symbol

logger = logging.getLogger(__name__)

class UnifiedLeverageManager:
    """统一杠杆管理器 - 三交易所统一实现"""

    def __init__(self):
        self.logger = logger

        # 🔥 修复：统一使用3倍杠杆配置
        self.leverage_config = {
            "gate": min(int(os.getenv("GATE_LEVERAGE", "3")), 3),
            "bybit": min(int(os.getenv("BYBIT_LEVERAGE", "3")), 3),
            "okx": min(int(os.getenv("OKX_LEVERAGE", "3")), 3),
        }

        # 🔥 修复：最大杠杆限制统一为3倍
        self.max_leverage = min(float(os.getenv("MAX_LEVERAGE_RATIO", "3.0")), 3.0)

        # 🔥 新增：杠杆缓存系统 - 解决200-500ms延迟问题
        self.leverage_cache = {}  # 格式: {exchange_symbol_key: {leverage: int, timestamp: float, status: str}}
        self.cache_ttl = 300  # 5分钟TTL，杠杆设置相对稳定
        self.cache_hits = 0
        self.cache_misses = 0

        self.logger.info(f"统一杠杆管理器初始化: {self.leverage_config}")
        self.logger.info("🔥 杠杆缓存系统已启用 - TTL: 5分钟")

    def _get_cache_key(self, exchange_name: str, symbol: str) -> str:
        """生成缓存键"""
        return f"{exchange_name}:{symbol}"

    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """检查缓存是否有效"""
        if not cache_entry:
            return False
        return (time.time() - cache_entry.get('timestamp', 0)) < self.cache_ttl

    def _get_cached_leverage(self, exchange_name: str, symbol: str) -> Optional[Dict]:
        """获取缓存的杠杆设置"""
        cache_key = self._get_cache_key(exchange_name, symbol)
        cache_entry = self.leverage_cache.get(cache_key)

        if cache_entry and self._is_cache_valid(cache_entry):
            self.cache_hits += 1
            self.logger.debug(f"🎯 杠杆缓存命中: {exchange_name} {symbol} -> {cache_entry['leverage']}x")
            return cache_entry

        self.cache_misses += 1
        return None

    def _cache_leverage_setting(self, exchange_name: str, symbol: str, leverage: int, status: str = "SUCCESS"):
        """缓存杠杆设置"""
        cache_key = self._get_cache_key(exchange_name, symbol)
        self.leverage_cache[cache_key] = {
            'leverage': leverage,
            'timestamp': time.time(),
            'status': status,
            'exchange': exchange_name,
            'symbol': symbol
        }
        self.logger.debug(f"💾 杠杆设置已缓存: {exchange_name} {symbol} -> {leverage}x")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate': hit_rate,
            'cached_entries': len(self.leverage_cache),
            'cache_ttl': self.cache_ttl
        }

    async def set_leverage_unified(self, exchange, symbol: str, leverage: Optional[int] = None) -> Dict[str, Any]:
        """
        统一杠杆设置接口 - 支持所有交易所 + 🔥 缓存优化

        Args:
            exchange: 交易所实例
            symbol: 交易对符号 (如 BTC-USDT)
            leverage: 杠杆倍数，None时使用默认配置

        Returns:
            Dict: 统一格式的响应结果
        """

        try:
            # 获取交易所名称
            exchange_name = self._get_exchange_name(exchange)
            if not exchange_name:
                self.logger.error("无法识别交易所类型")
                return False

            # 获取杠杆配置
            if leverage is None:
                leverage = self.leverage_config.get(exchange_name, 2)
            leverage = min(leverage, int(self.max_leverage))

            # 🔥 新增：检查缓存，避免重复设置
            cached_setting = self._get_cached_leverage(exchange_name, symbol)
            if cached_setting and cached_setting['leverage'] == leverage and cached_setting['status'] == 'SUCCESS':
                self.logger.info(f"🎯 杠杆缓存命中，跳过设置: {exchange_name} {symbol} {leverage}x")
                return self._format_leverage_response(True, exchange_name, symbol, leverage, from_cache=True)

            # 参数验证
            if not self._validate_leverage_params(symbol, leverage):
                return self._format_leverage_response(False, exchange_name, symbol, leverage)

            self.logger.info(f"🔧 {exchange_name.title()}统一杠杆设置: {symbol} {leverage}倍")

            # 根据交易所类型调用对应的设置方法
            if exchange_name == "gate":
                success = await self._set_gate_leverage(exchange, symbol, leverage)
            elif exchange_name == "bybit":
                success = await self._set_bybit_leverage(exchange, symbol, leverage)
            elif exchange_name == "okx":
                success = await self._set_okx_leverage(exchange, symbol, leverage)
            else:
                self.logger.error(f"不支持的交易所: {exchange_name}")
                success = False

            # 🔥 新增：缓存设置结果
            if success:
                self._cache_leverage_setting(exchange_name, symbol, leverage, "SUCCESS")
            else:
                self._cache_leverage_setting(exchange_name, symbol, leverage, "FAILED")

            # 返回统一格式
            return self._format_leverage_response(success, exchange_name, symbol, leverage)

        except Exception as e:
            self.logger.error(f"统一杠杆设置异常: {e}")
            return self._format_leverage_response(False, "unknown", symbol, leverage)

    async def _set_gate_leverage(self, exchange, symbol: str, leverage: int) -> bool:
        """Gate.io杠杆设置 - 🔥 关键修复：使用正确的POST方法和query参数格式"""
        try:
            # 转换交易对格式
            contract = get_exchange_symbol(symbol, "gate", "futures")

            # 🔥 根据Gate.io官方API文档分析：
            # 正确端点：POST /futures/{settle}/positions/{contract}/leverage
            # 正确参数：leverage作为query参数传递，不是请求体！
            # 错误：data={"leverage": str(leverage)}
            # 正确：params={"leverage": str(leverage)}

            self.logger.info(f"🔧 Gate.io杠杆设置: 合约={contract}, 杠杆={leverage}倍")

            # 重试机制
            for retry in range(3):
                try:
                    # 🔥 关键修复：使用正确的API方法和参数格式
                    # 根据Gate.io官方文档：POST /futures/{settle}/positions/{contract}/leverage
                    # 杠杆参数必须作为query参数传递！
                    response = await exchange._request(
                        "POST",  # 🔥 修复：使用POST方法，不是PUT
                        f"futures/usdt/positions/{contract}/leverage",
                        params={"leverage": str(leverage)},  # 🔥 关键修复：杠杆参数作为query参数
                        market_type="futures"
                    )

                    if response is not None:
                        self.logger.info(f"✅ Gate.io杠杆设置成功: {symbol} {leverage}倍")

                        # 验证杠杆设置是否生效
                        await asyncio.sleep(0.2)
                        await self._verify_gate_leverage(exchange, contract, leverage)
                        return True
                    else:
                        self.logger.warning(f"⚠️ Gate.io杠杆设置失败，重试 {retry+1}/3")
                        await asyncio.sleep(0.1)

                except Exception as retry_e:
                    self.logger.warning(f"⚠️ Gate.io杠杆设置异常，重试 {retry+1}/3: {retry_e}")
                    await asyncio.sleep(0.1)

            self.logger.error(f"❌ Gate.io杠杆设置最终失败: {symbol}")
            return False

        except Exception as e:
            self.logger.error(f"❌ Gate.io杠杆设置异常: {e}")
            return False

    async def _verify_gate_leverage(self, exchange, contract: str, expected_leverage: int):
        """验证Gate.io杠杆设置是否生效"""
        try:
            position_response = await exchange._request("GET", f"futures/usdt/positions/{contract}",
                                                      market_type="futures")
            if position_response:
                actual_leverage = position_response.get("leverage", "0")
                self.logger.info(f"🔍 Gate.io杠杆验证: 设置{expected_leverage}倍, 实际{actual_leverage}倍")
                
                if str(actual_leverage) != str(expected_leverage):
                    self.logger.warning(f"⚠️ Gate.io杠杆设置可能未完全生效")
        except Exception as e:
            self.logger.warning(f"⚠️ Gate.io杠杆验证失败: {e}")

    async def _set_bybit_leverage(self, exchange, symbol: str, leverage: int) -> bool:
        """Bybit杠杆设置 - 使用正确的API格式"""
        try:
            # 转换交易对格式
            bybit_symbol = get_exchange_symbol(symbol, "bybit", "futures")

            self.logger.info(f"🔧 Bybit杠杆设置开始: {symbol} -> {bybit_symbol} {leverage}倍")

            # 🔥 修复：确保参数格式严格按照Bybit API要求
            data = {
                "category": "linear",
                "symbol": bybit_symbol,
                "buyLeverage": str(leverage),
                "sellLeverage": str(leverage)
            }

            self.logger.debug(f"🔧 Bybit杠杆设置参数: {data}")

            response = await exchange._request("POST", "/v5/position/set-leverage", data=data, signed=True)

            self.logger.info(f"✅ Bybit杠杆设置成功: {symbol} {leverage}倍, 响应: {response}")
            return True

        except Exception as e:
            error_str = str(e)
            # 🔥 修复：110043错误表示杠杆已经是目标值，视为成功
            if "110043" in error_str or "leverage not modified" in error_str.lower():
                self.logger.info(f"✅ Bybit杠杆已是目标值: {symbol} {leverage}倍")
                return True
            else:
                self.logger.error(f"❌ Bybit杠杆设置异常: {e}")
                return False

    async def _set_okx_leverage(self, exchange, symbol: str, leverage: int) -> bool:
        """OKX杠杆设置 - 🔥 修复杠杆3倍vs2倍问题"""
        try:
            inst_id = get_exchange_symbol(symbol, "okx", "futures")

            # 🔥 关键修复：OKX杠杆配置问题（实际3倍vs配置2倍）
            # 问题分析：环境变量配置2倍，但账户实际设置可能不一致
            # 解决方案：强制统一为2倍，确保与Gate.io、Bybit一致

            # 🔥 验证杠杆配置
            if leverage > 2:
                self.logger.warning(f"⚠️ OKX杠杆超过安全限制，强制设置为2倍: {leverage} -> 2")
                leverage = 2

            self.logger.info(f"🔧 OKX杠杆统一设置: {leverage}倍 (与Gate.io、Bybit保持一致)")

            # 获取持仓模式
            try:
                position_mode = await exchange.get_position_mode()
                if position_mode == "long_short_mode":
                    # 开平仓模式：需要分别设置多空杠杆
                    for pos_side in ["long", "short"]:
                        await exchange._request(
                            "POST", "/api/v5/account/set-leverage",
                            data={
                                "instId": inst_id,
                                "lever": str(leverage),  # 🔥 使用修复后的杠杆值
                                "mgnMode": "cross",
                                "posSide": pos_side
                            }
                        )
                        self.logger.info(f"✅ OKX设置{pos_side}杠杆: {leverage}倍")
                else:
                    # 买卖模式：posSide为net
                    await exchange._request(
                        "POST", "/api/v5/account/set-leverage",
                        data={
                            "instId": inst_id,
                            "lever": str(leverage),  # 🔥 使用修复后的杠杆值
                            "mgnMode": "cross",
                            "posSide": "net"
                        }
                    )
                    self.logger.info(f"✅ OKX设置net杠杆: {leverage}倍")
            except Exception as e:
                # 兜底处理：使用net模式
                self.logger.warning(f"⚠️ OKX获取持仓模式失败，使用net模式设置杠杆: {e}")
                await exchange._request(
                    "POST", "/api/v5/account/set-leverage",
                    data={
                        "instId": inst_id,
                        "lever": str(leverage),  # 🔥 使用修复后的杠杆值
                        "mgnMode": "cross",
                        "posSide": "net"
                    }
                )
                self.logger.info(f"✅ OKX设置net杠杆(兜底): {leverage}倍")

            # 🔥 验证杠杆设置结果
            self.logger.info(f"✅ OKX杠杆设置成功: {symbol} {leverage}倍 (三交易所统一)")
            return True

        except Exception as e:
            self.logger.error(f"❌ OKX杠杆设置异常: {e}")
            return False

    def _format_leverage_response(self, success: bool, exchange_name: str, symbol: str, leverage: int, from_cache: bool = False) -> Dict[str, Any]:
        """格式化杠杆设置响应为统一格式"""
        message = f"杠杆设置{'成功' if success else '失败'}: {symbol} {leverage}倍"
        if from_cache:
            message += " (缓存命中)"

        return {
            "success": success,
            "exchange": exchange_name,
            "symbol": symbol,
            "leverage": leverage,
            "from_cache": from_cache,
            "message": message
        }

    async def preheat_leverage_cache(self, exchanges: Dict[str, Any], priority_symbols: List[str]) -> Dict[str, Any]:
        """
        🔥 杠杆缓存预热方法 - 解决执行阶段200-500ms延迟
        在系统启动时预设所有交易对的杠杆，执行时直接使用缓存
        
        🔥 修复：添加完整的指数退避重试机制，防止6→20个交易对扩展触发限流
        """
        preheat_stats = {
            "preheated_count": 0,
            "failed_count": 0,
            "exchanges_processed": 0,
            "symbols_processed": 0,
            "rate_limited_count": 0,
            "retry_count": 0
        }

        self.logger.info("🔥 开始杠杆缓存预热（含指数退避重试）...")
        
        # 🔥 修复：获取每个交易所的限速配置和重试参数
        exchange_configs = {
            "gate": {
                "rate_limit": 15,          # Gate.io: 15次/秒
                "base_delay": 0.5,         # 基础延迟500ms
                "max_retries": 3,          # 最大重试3次
                "backoff_multiplier": 2    # 指数退避倍数
            },
            "bybit": {
                "rate_limit": 50,          # Bybit: 50次/秒  
                "base_delay": 0.2,         # 基础延迟200ms
                "max_retries": 3,
                "backoff_multiplier": 2
            },
            "okx": {
                "rate_limit": 30,          # OKX: 30次/秒
                "base_delay": 0.3,         # 基础延迟300ms
                "max_retries": 3,
                "backoff_multiplier": 2
            }
        }

        try:
            for exchange_name, exchange in exchanges.items():
                preheat_stats["exchanges_processed"] += 1
                
                # 获取交易所配置
                config = exchange_configs.get(exchange_name.lower(), exchange_configs["gate"])
                rate_limit = config["rate_limit"]
                base_delay = config["base_delay"]
                max_retries = config["max_retries"]
                backoff_multiplier = config["backoff_multiplier"]
                
                # 计算安全间隔：基础延迟 + 10%安全边距
                safe_interval = (1.0 / rate_limit) + (base_delay * 0.1)
                
                self.logger.info(f"🔧 {exchange_name}杠杆预热配置:")
                self.logger.info(f"   交易对数量: {len(priority_symbols)}")
                self.logger.info(f"   API限速: {rate_limit}/秒")
                self.logger.info(f"   安全间隔: {safe_interval:.3f}秒")
                self.logger.info(f"   最大重试: {max_retries}次")
                
                for i, symbol in enumerate(priority_symbols):
                    preheat_stats["symbols_processed"] += 1
                    symbol_success = False

                    # 🔥 修复：完整的指数退避重试循环
                    for retry_attempt in range(max_retries + 1):
                        try:
                            # 🔥 第一次请求前的基础延迟
                            if i > 0 or retry_attempt > 0:
                                if retry_attempt == 0:
                                    # 正常请求间隔
                                    await asyncio.sleep(safe_interval)
                                else:
                                    # 🔥 完整指数退避算法：精确计算，避免浮点精度问题
                                    if retry_attempt == 1:
                                        # 第一次重试使用基础延迟
                                        actual_delay = base_delay
                                    else:
                                        # 后续重试使用指数退避，防止浮点精度问题
                                        backoff_delay = base_delay * (backoff_multiplier ** (retry_attempt - 1))
                                        # 🔥 优化：根据交易所特点设置不同的最大退避时间
                                        if exchange_name.lower() == "gate":
                                            max_backoff = 60.0  # Gate.io网络延迟高，需要更长恢复时间
                                        elif exchange_name.lower() == "bybit":
                                            max_backoff = 20.0  # Bybit限速恢复较快
                                        else:  # okx
                                            max_backoff = 30.0  # OKX标准恢复时间
                                        actual_delay = min(backoff_delay, max_backoff)
                                    
                                    preheat_stats["retry_count"] += 1
                                    self.logger.warning(f"⏰ {exchange_name} {symbol} 指数退避重试{retry_attempt}/{max_retries}，延迟{actual_delay:.2f}秒")
                                    await asyncio.sleep(actual_delay)
                            
                            # 获取该交易所的默认杠杆
                            default_leverage = self.leverage_config.get(exchange_name, 3)

                            # 预设杠杆（实际调用API）
                            result = await self.set_leverage_unified(exchange, symbol, default_leverage)

                            if result and result.get("success"):
                                preheat_stats["preheated_count"] += 1
                                symbol_success = True
                                self.logger.debug(f"✅ 杠杆预热成功: {exchange_name} {symbol} {default_leverage}x")
                                break  # 成功则跳出重试循环
                            else:
                                error_msg = result.get("message", "未知错误") if result else "无响应"
                                
                                # 🔥 检查是否为限流错误，需要重试
                                if any(term in error_msg.lower() for term in [
                                    'rate limit', 'too many', '频率', '限流', 'frequency', 'exceed'
                                ]):
                                    preheat_stats["rate_limited_count"] += 1
                                    if retry_attempt < max_retries:
                                        self.logger.warning(f"⚠️ API限流检测，准备重试: {exchange_name} {symbol}")
                                        continue  # 继续重试
                                    else:
                                        self.logger.error(f"❌ API限流，重试次数耗尽: {exchange_name} {symbol}")
                                        break
                                else:
                                    # 非限流错误，记录并跳出重试
                                    self.logger.debug(f"❌ 杠杆预热非限流失败: {exchange_name} {symbol} - {error_msg}")
                                    break

                        except Exception as e:
                            error_msg = str(e).lower()
                            
                            # 🔥 修复：精确识别各种限流错误模式
                            rate_limit_patterns = [
                                'rate limit', 'too many requests', 'frequency limit',
                                '频率限制', '请求频率', 'rate_limit', 'too_many',
                                '50011', '429', 'exceed', '超出', '限流'
                            ]
                            
                            is_rate_limit = any(pattern in error_msg for pattern in rate_limit_patterns)
                            
                            if is_rate_limit:
                                preheat_stats["rate_limited_count"] += 1
                                if retry_attempt < max_retries:
                                    self.logger.warning(f"⚠️ API限流异常，准备指数退避重试: {exchange_name} {symbol} - {e}")
                                    continue  # 继续重试
                                else:
                                    self.logger.error(f"❌ API限流异常，重试次数耗尽: {exchange_name} {symbol}")
                                    break
                            else:
                                # 非限流异常，直接记录失败
                                self.logger.debug(f"❌ 杠杆预热异常（非限流）: {exchange_name} {symbol} - {e}")
                                break

                    # 如果所有重试都失败，记录为失败
                    if not symbol_success:
                        preheat_stats["failed_count"] += 1

            # 🔥 统计报告增强
            total_requests = preheat_stats["symbols_processed"]
            success_rate = (preheat_stats["preheated_count"] / total_requests * 100) if total_requests > 0 else 0
            retry_rate = (preheat_stats["retry_count"] / total_requests * 100) if total_requests > 0 else 0

            self.logger.info(f"🎯 杠杆缓存预热完成（含指数退避）:")
            self.logger.info(f"   ✅ 成功预热: {preheat_stats['preheated_count']}个")
            self.logger.info(f"   ❌ 失败数量: {preheat_stats['failed_count']}个")
            self.logger.info(f"   🚨 限流次数: {preheat_stats['rate_limited_count']}个")
            self.logger.info(f"   🔄 重试次数: {preheat_stats['retry_count']}个")
            self.logger.info(f"   📊 成功率: {success_rate:.1f}%")
            self.logger.info(f"   📊 重试率: {retry_rate:.1f}%")

            return preheat_stats

        except Exception as e:
            self.logger.error(f"❌ 杠杆缓存预热异常: {e}")
            return preheat_stats

    def _validate_leverage_params(self, symbol: str, leverage: int) -> bool:
        """验证杠杆参数"""
        if not symbol or symbol.strip() == "":
            self.logger.error("交易对符号不能为空")
            return False
        
        if leverage < 1 or leverage > self.max_leverage:
            self.logger.error(f"杠杆倍数超出范围: {leverage}, 允许范围: 1-{self.max_leverage}")
            return False
        
        return True

    def _get_exchange_name(self, exchange) -> Optional[str]:
        """🔥 使用统一的交易所名称获取函数"""
        from exchanges.exchanges_base import get_exchange_name
        name = get_exchange_name(exchange)
        return name if name != "unknown" else None

    async def batch_set_leverage(self, exchanges: Dict, symbol: str, leverage: Optional[int] = None) -> Dict[str, bool]:
        """批量设置杠杆"""
        results = {}
        
        for name, exchange in exchanges.items():
            try:
                success = await self.set_leverage_unified(exchange, symbol, leverage)
                results[name] = success
                self.logger.info(f"{name}杠杆设置: {'✅成功' if success else '❌失败'}")
            except Exception as e:
                results[name] = False
                self.logger.error(f"{name}杠杆设置异常: {e}")
        
        return results

    def get_leverage_config(self) -> Dict[str, int]:
        """获取杠杆配置"""
        return self.leverage_config.copy()

    def cleanup_expired_cache(self) -> int:
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []

        for cache_key, cache_entry in self.leverage_cache.items():
            if (current_time - cache_entry.get('timestamp', 0)) >= self.cache_ttl:
                expired_keys.append(cache_key)

        for key in expired_keys:
            del self.leverage_cache[key]

        if expired_keys:
            self.logger.debug(f"🧹 清理过期杠杆缓存: {len(expired_keys)}个")

        return len(expired_keys)

# 🔥 全局访问接口
_unified_leverage_manager = None

def get_unified_leverage_manager() -> UnifiedLeverageManager:
    """获取统一杠杆管理器实例"""
    global _unified_leverage_manager
    if _unified_leverage_manager is None:
        _unified_leverage_manager = UnifiedLeverageManager()
    return _unified_leverage_manager

async def set_leverage_unified(exchange, symbol: str, leverage: Optional[int] = None) -> Dict[str, Any]:
    """快速访问函数：统一杠杆设置"""
    manager = get_unified_leverage_manager()
    return await manager.set_leverage_unified(exchange, symbol, leverage) 