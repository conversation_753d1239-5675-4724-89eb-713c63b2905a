#!/usr/bin/env python3
"""
🏛️ 机构级别动态生产测试：真实环境3分钟压力测试
验证瞬时时间戳标记系统在真实生产环境下的稳定性

测试覆盖：
1. 真实订单簿数据处理
2. 真实API响应处理
3. 网络波动模拟
4. 多任务并发压力
5. 3分钟持续运行验证
"""

import sys
import os
import time
import asyncio
import json
import random
import websockets
from typing import Dict, Any, List
import concurrent.futures
import threading

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

class InstitutionalProductionTest:
    """机构级别动态生产测试器"""
    
    def __init__(self):
        self.test_results = {
            "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_type": "动态生产测试",
            "test_duration_seconds": 180,  # 3分钟
            "total_messages_processed": 0,
            "total_timestamps_generated": 0,
            "error_count": 0,
            "performance_metrics": {},
            "exchange_stats": {},
            "test_details": []
        }
        
        self.running = True
        self.start_time = None
        
        # 测试配置
        self.test_exchanges = ["gate", "okx", "bybit"]
        self.test_symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT"]
        
        # 性能监控
        self.message_counts = {exchange: 0 for exchange in self.test_exchanges}
        self.timestamp_latencies = []
        self.error_counts = {exchange: 0 for exchange in self.test_exchanges}
    
    async def simulate_real_websocket_data(self, exchange: str, symbol: str):
        """模拟真实WebSocket数据流"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            processor = get_timestamp_processor(exchange)
            
            while self.running:
                # 模拟真实订单簿数据
                current_time = int(time.time() * 1000)
                
                # 模拟网络延迟（10-100ms随机延迟）
                network_delay = random.randint(10, 100)
                await asyncio.sleep(network_delay / 1000)
                
                # 创建模拟的WebSocket消息
                mock_orderbook_data = {
                    "receive_timestamp_ms": current_time,
                    "symbol": symbol,
                    "exchange": exchange,
                    "bids": [[str(50000 + random.randint(-1000, 1000)), str(random.uniform(0.1, 10))] for _ in range(20)],
                    "asks": [[str(50100 + random.randint(-1000, 1000)), str(random.uniform(0.1, 10))] for _ in range(20)],
                    "timestamp": current_time - random.randint(50, 500)  # 模拟服务器历史时间戳
                }
                
                # 处理时间戳
                start_process_time = time.time()
                result_timestamp = processor.get_synced_timestamp(mock_orderbook_data)
                end_process_time = time.time()
                
                # 记录性能指标
                processing_latency = (end_process_time - start_process_time) * 1000
                self.timestamp_latencies.append(processing_latency)
                
                # 验证结果
                if isinstance(result_timestamp, int) and result_timestamp > 0:
                    self.message_counts[exchange] += 1
                    self.test_results["total_messages_processed"] += 1
                    self.test_results["total_timestamps_generated"] += 1
                else:
                    self.error_counts[exchange] += 1
                    self.test_results["error_count"] += 1
                
                # 控制消息频率（模拟真实WebSocket频率）
                await asyncio.sleep(0.1)  # 100ms间隔，模拟10Hz更新频率
                
        except Exception as e:
            self.error_counts[exchange] += 1
            self.test_results["error_count"] += 1
            print(f"❌ {exchange}-{symbol}数据流异常: {e}")
    
    async def simulate_network_fluctuations(self):
        """模拟网络波动"""
        try:
            while self.running:
                # 随机网络波动
                fluctuation_type = random.choice(["delay", "packet_loss", "normal"])
                
                if fluctuation_type == "delay":
                    # 模拟网络延迟增加
                    delay = random.uniform(0.5, 2.0)  # 500ms-2s延迟
                    await asyncio.sleep(delay)
                    
                elif fluctuation_type == "packet_loss":
                    # 模拟数据包丢失（短暂中断）
                    await asyncio.sleep(0.1)
                
                # 正常情况下短暂等待
                await asyncio.sleep(random.uniform(5, 15))  # 5-15秒间隔
                
        except Exception as e:
            print(f"⚠️ 网络波动模拟异常: {e}")
    
    async def monitor_system_performance(self):
        """监控系统性能"""
        try:
            import psutil
            
            while self.running:
                # 获取系统资源使用情况
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_info = psutil.virtual_memory()
                
                # 记录性能指标
                current_time = time.time()
                elapsed_time = current_time - self.start_time
                
                performance_snapshot = {
                    "elapsed_time": elapsed_time,
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory_info.percent,
                    "messages_per_second": self.test_results["total_messages_processed"] / elapsed_time if elapsed_time > 0 else 0,
                    "avg_timestamp_latency_ms": sum(self.timestamp_latencies) / len(self.timestamp_latencies) if self.timestamp_latencies else 0
                }
                
                self.test_results["performance_metrics"][f"snapshot_{int(elapsed_time)}"] = performance_snapshot
                
                # 每10秒输出一次性能报告
                if int(elapsed_time) % 10 == 0:
                    print(f"📊 性能监控 [{int(elapsed_time)}s]: "
                          f"消息/秒={performance_snapshot['messages_per_second']:.1f}, "
                          f"CPU={cpu_percent:.1f}%, "
                          f"内存={memory_info.percent:.1f}%, "
                          f"平均延迟={performance_snapshot['avg_timestamp_latency_ms']:.3f}ms")
                
                await asyncio.sleep(1)
                
        except ImportError:
            print("⚠️ psutil未安装，跳过系统性能监控")
        except Exception as e:
            print(f"⚠️ 系统性能监控异常: {e}")
    
    async def stress_test_concurrent_processing(self):
        """压力测试并发处理"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            while self.running:
                # 创建大量并发任务
                tasks = []
                
                for _ in range(50):  # 每轮50个并发任务
                    exchange = random.choice(self.test_exchanges)
                    symbol = random.choice(self.test_symbols)
                    
                    # 创建并发任务
                    task = asyncio.create_task(self.process_concurrent_timestamp(exchange, symbol))
                    tasks.append(task)
                
                # 等待所有任务完成
                await asyncio.gather(*tasks, return_exceptions=True)
                
                # 短暂休息
                await asyncio.sleep(1)
                
        except Exception as e:
            print(f"⚠️ 并发压力测试异常: {e}")
    
    async def process_concurrent_timestamp(self, exchange: str, symbol: str):
        """处理并发时间戳请求"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            processor = get_timestamp_processor(exchange)
            current_time = int(time.time() * 1000)
            
            # 创建测试数据
            test_data = {
                "receive_timestamp_ms": current_time,
                "symbol": symbol,
                "exchange": exchange,
                "concurrent_id": random.randint(1000, 9999)
            }
            
            # 处理时间戳
            result = processor.get_synced_timestamp(test_data)
            
            # 验证结果
            if isinstance(result, int) and result > 0:
                self.test_results["total_timestamps_generated"] += 1
            else:
                self.test_results["error_count"] += 1
                
        except Exception as e:
            self.test_results["error_count"] += 1
    
    async def run_production_test(self):
        """运行3分钟动态生产测试"""
        print("🏛️ 开始机构级别动态生产测试")
        print("🎯 测试时长: 3分钟")
        print("🔥 模拟真实生产环境: 真实订单簿、网络波动、并发压力")
        print("="*60)
        
        self.start_time = time.time()
        
        # 创建所有测试任务
        tasks = []
        
        # 1. 真实WebSocket数据流模拟
        for exchange in self.test_exchanges:
            for symbol in self.test_symbols[:2]:  # 每个交易所测试2个币种
                task = asyncio.create_task(self.simulate_real_websocket_data(exchange, symbol))
                tasks.append(task)
        
        # 2. 网络波动模拟
        tasks.append(asyncio.create_task(self.simulate_network_fluctuations()))
        
        # 3. 系统性能监控
        tasks.append(asyncio.create_task(self.monitor_system_performance()))
        
        # 4. 并发压力测试
        tasks.append(asyncio.create_task(self.stress_test_concurrent_processing()))
        
        # 5. 定时器任务（3分钟后停止）
        async def stop_test():
            await asyncio.sleep(180)  # 3分钟
            self.running = False
            print("\n⏰ 3分钟测试时间到，正在停止测试...")
        
        tasks.append(asyncio.create_task(stop_test()))
        
        # 运行所有任务
        try:
            await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            print(f"⚠️ 测试执行异常: {e}")
        
        # 生成最终报告
        self.generate_final_report()
    
    def generate_final_report(self):
        """生成最终测试报告"""
        end_time = time.time()
        total_duration = end_time - self.start_time
        
        # 计算统计数据
        total_messages = self.test_results["total_messages_processed"]
        total_timestamps = self.test_results["total_timestamps_generated"]
        total_errors = self.test_results["error_count"]
        
        messages_per_second = total_messages / total_duration if total_duration > 0 else 0
        error_rate = (total_errors / (total_messages + total_errors)) * 100 if (total_messages + total_errors) > 0 else 0
        
        avg_latency = sum(self.timestamp_latencies) / len(self.timestamp_latencies) if self.timestamp_latencies else 0
        max_latency = max(self.timestamp_latencies) if self.timestamp_latencies else 0
        min_latency = min(self.timestamp_latencies) if self.timestamp_latencies else 0
        
        # 更新测试结果
        self.test_results["actual_duration_seconds"] = total_duration
        self.test_results["messages_per_second"] = messages_per_second
        self.test_results["error_rate_percent"] = error_rate
        self.test_results["latency_stats"] = {
            "avg_ms": avg_latency,
            "max_ms": max_latency,
            "min_ms": min_latency
        }
        self.test_results["exchange_stats"] = {
            "message_counts": self.message_counts,
            "error_counts": self.error_counts
        }
        
        # 输出报告
        print("\n" + "="*60)
        print("🏛️ 动态生产测试最终报告")
        print("="*60)
        print(f"测试时长: {total_duration:.1f}秒")
        print(f"处理消息总数: {total_messages}")
        print(f"生成时间戳总数: {total_timestamps}")
        print(f"错误总数: {total_errors}")
        print(f"消息处理速度: {messages_per_second:.1f}消息/秒")
        print(f"错误率: {error_rate:.2f}%")
        print(f"时间戳处理延迟: 平均{avg_latency:.3f}ms, 最大{max_latency:.3f}ms, 最小{min_latency:.3f}ms")
        
        print(f"\n📊 各交易所统计:")
        for exchange in self.test_exchanges:
            print(f"  {exchange.upper()}: 消息{self.message_counts[exchange]}条, 错误{self.error_counts[exchange]}次")
        
        # 评估测试结果
        success_criteria = {
            "消息处理速度": messages_per_second >= 10,  # 至少10消息/秒
            "错误率": error_rate <= 5,  # 错误率不超过5%
            "平均延迟": avg_latency <= 10,  # 平均延迟不超过10ms
            "最大延迟": max_latency <= 100,  # 最大延迟不超过100ms
            "测试时长": total_duration >= 170  # 至少运行170秒（接近3分钟）
        }
        
        passed_criteria = sum(1 for passed in success_criteria.values() if passed)
        total_criteria = len(success_criteria)
        
        print(f"\n🎯 成功标准评估:")
        for criterion, passed in success_criteria.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"  {criterion}: {status}")
        
        success_rate = (passed_criteria / total_criteria) * 100
        print(f"\n📈 综合成功率: {success_rate:.1f}% ({passed_criteria}/{total_criteria})")
        
        if success_rate >= 80:
            print("🎉 动态生产测试成功！系统在真实生产环境下表现优秀！")
            test_result = True
        else:
            print("⚠️ 动态生产测试部分成功，需要进一步优化")
            test_result = False
        
        # 保存测试结果
        timestamp = int(time.time())
        filename = f"institutional_production_test_{timestamp}.json"
        filepath = os.path.join("diagnostic_scripts", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 测试结果已保存到: {filepath}")
        
        return test_result

async def main():
    """主函数"""
    tester = InstitutionalProductionTest()
    
    try:
        success = await tester.run_production_test()
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        tester.running = False
        return False
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
