#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金费率感知优化系统 - 核心模块
基于资金费率感知优化实现文档.md 100%实现

核心功能：
1. 资金费率感知平仓优化（策略1）
2. 资金费率结算前开仓控制（策略2） 
3. 动态开仓阈值策略（策略3）
4. 三交易所统一实现
5. 智能监控频率和缓存策略
6. 完善的错误处理和降级机制
"""

import time
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass, asdict
from utils.logger import get_logger


class FundingRateAwarenessState(Enum):
    """资金费率感知状态枚举"""
    NORMAL = "normal"           # 正常状态：按原系统动态阈值运行
    MONITORING = "monitoring"   # 监控状态：资金费率感知激活，开始监控
    FAVORABLE = "favorable"     # 有利状态：资金费率>0，延长持仓
    UNFAVORABLE = "unfavorable" # 不利状态：资金费率<0，加速平仓
    RECOVERY = "recovery"       # 恢复状态：等待恢复到正常状态


class FundingSettlementPhase(Enum):
    """资金费率结算阶段枚举"""
    FAR_FROM_SETTLEMENT = "far_from_settlement"         # 距结算>2小时
    APPROACHING_SETTLEMENT = "approaching_settlement"   # 距结算1-2小时  
    NEAR_SETTLEMENT = "near_settlement"                 # 距结算30分钟-1小时
    CLOSE_TO_SETTLEMENT = "close_to_settlement"         # 距结算<30分钟
    SETTLEMENT_WINDOW = "settlement_window"             # 结算窗口（结算前10分钟）


@dataclass
class FundingRateInfo:
    """资金费率信息数据类"""
    exchange: str
    symbol: str
    funding_rate: float
    timestamp: float
    settlement_time: float
    is_favorable: bool  # True: >0有利, False: <0不利
    time_to_settlement: float  # 距下次结算的秒数
    settlement_phase: FundingSettlementPhase
    

@dataclass
class FundingAwarenessDecision:
    """资金费率感知决策结果"""
    action: str  # "hold", "accelerate_close", "reset_threshold", "block_opening"
    reason: str
    current_state: FundingRateAwarenessState
    funding_info: FundingRateInfo
    threshold_adjustment: Optional[float] = None  # 阈值调整值
    monitoring_frequency: Optional[int] = None    # 监控频率(秒)
    

class FundingRateAwareness:
    """
    资金费率感知优化系统
    
    实现三大核心策略：
    1. 资金费率感知平仓
    2. 资金费率结算前开仓控制  
    3. 动态开仓阈值策略
    """
    
    def __init__(self, config: Dict = None):
        """初始化资金费率感知系统"""
        self.logger = get_logger(self.__class__.__name__)
        self.config = config or {}
        
        # 功能开关配置
        self.enabled = self._get_config_bool("FUNDING_RATE_AWARENESS_ENABLED", True)
        self.closing_optimization_enabled = self._get_config_bool("FUNDING_CLOSING_OPTIMIZATION_ENABLED", True)
        self.opening_control_enabled = self._get_config_bool("FUNDING_OPENING_CONTROL_ENABLED", True)
        self.dynamic_threshold_enabled = self._get_config_bool("FUNDING_DYNAMIC_THRESHOLD_ENABLED", True)
        
        # 状态管理
        self.position_states: Dict[str, FundingRateAwarenessState] = {}  # 每个持仓的状态
        self.position_reset_counts: Dict[str, int] = {}  # 每个持仓的重置次数
        
        # 缓存管理
        self.funding_rate_cache: Dict[str, FundingRateInfo] = {}
        self.cache_timestamps: Dict[str, float] = {}
        
        # 配置参数
        self.max_reset_count = self._get_config_int("FUNDING_MAX_RESET_COUNT", 3)
        self.settlement_monitoring_window = self._get_config_int("FUNDING_SETTLEMENT_MONITORING_WINDOW", 600)  # 10分钟
        self.forced_recovery_timeout = self._get_config_int("FUNDING_FORCED_RECOVERY_TIMEOUT", 600)  # 10分钟
        
        # 动态开仓阈值配置
        self.opening_thresholds = {
            FundingSettlementPhase.FAR_FROM_SETTLEMENT: self._get_config_float("FUNDING_THRESHOLD_FAR", 0.006),      # 0.6%
            FundingSettlementPhase.APPROACHING_SETTLEMENT: self._get_config_float("FUNDING_THRESHOLD_APPROACHING", 0.007),  # 0.7%
            FundingSettlementPhase.NEAR_SETTLEMENT: self._get_config_float("FUNDING_THRESHOLD_NEAR", 0.008),        # 0.8%
            FundingSettlementPhase.CLOSE_TO_SETTLEMENT: self._get_config_float("FUNDING_THRESHOLD_CLOSE", 0.010),   # 1.0%
            FundingSettlementPhase.SETTLEMENT_WINDOW: self._get_config_float("FUNDING_THRESHOLD_SETTLEMENT", 0.015)  # 1.5%
        }
        
        # 监控频率配置（秒）
        self.monitoring_frequencies = {
            FundingSettlementPhase.FAR_FROM_SETTLEMENT: 900,      # 15分钟
            FundingSettlementPhase.APPROACHING_SETTLEMENT: 300,   # 5分钟
            FundingSettlementPhase.NEAR_SETTLEMENT: 60,           # 1分钟
            FundingSettlementPhase.CLOSE_TO_SETTLEMENT: 30,       # 30秒
            FundingSettlementPhase.SETTLEMENT_WINDOW: 5           # 5秒（超高频）
        }
        
        # 缓存时长配置（秒）
        self.cache_durations = {
            FundingSettlementPhase.FAR_FROM_SETTLEMENT: 900,      # 15分钟
            FundingSettlementPhase.APPROACHING_SETTLEMENT: 300,   # 5分钟
            FundingSettlementPhase.NEAR_SETTLEMENT: 60,           # 1分钟
            FundingSettlementPhase.CLOSE_TO_SETTLEMENT: 15,       # 15秒
            FundingSettlementPhase.SETTLEMENT_WINDOW: 5           # 5秒
        }
        
        self.logger.info("✅ 资金费率感知系统初始化完成")
        self.logger.info(f"   总开关: {'启用' if self.enabled else '禁用'}")
        self.logger.info(f"   平仓优化: {'启用' if self.closing_optimization_enabled else '禁用'}")
        self.logger.info(f"   开仓控制: {'启用' if self.opening_control_enabled else '禁用'}")
        self.logger.info(f"   动态阈值: {'启用' if self.dynamic_threshold_enabled else '禁用'}")
        
    def _get_config_bool(self, key: str, default: bool) -> bool:
        """获取布尔配置值"""
        import os
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
        
    def _get_config_int(self, key: str, default: int) -> int:
        """获取整数配置值"""
        import os
        try:
            return int(os.getenv(key, str(default)))
        except ValueError:
            return default
            
    def _get_config_float(self, key: str, default: float) -> float:
        """获取浮点配置值"""
        import os
        try:
            return float(os.getenv(key, str(default)))
        except ValueError:
            return default
            
    def get_funding_settlement_times(self) -> List[str]:
        """获取资金费率结算时间（UTC）
        
        三大交易所统一结算时间：00:00, 08:00, 16:00 UTC
        """
        return ["00:00", "08:00", "16:00"]
        
    def calculate_time_to_next_settlement(self, current_time: float = None) -> Tuple[float, str]:
        """计算到下次结算的时间
        
        Returns:
            Tuple[float, str]: (距下次结算的秒数, 下次结算时间字符串)
        """
        if current_time is None:
            current_time = time.time()
            
        current_dt = datetime.fromtimestamp(current_time, tz=timezone.utc)
        current_hour = current_dt.hour
        
        # 找到下一个结算时间
        settlement_hours = [0, 8, 16]
        next_settlement_hour = None
        
        for hour in settlement_hours:
            if hour > current_hour:
                next_settlement_hour = hour
                break
                
        if next_settlement_hour is None:
            # 下一个结算时间是明天的00:00
            next_settlement_hour = 0
            next_day = current_dt.replace(hour=0, minute=0, second=0, microsecond=0) + \
                      timedelta(days=1)
        else:
            # 今天的结算时间
            next_day = current_dt.replace(hour=next_settlement_hour, minute=0, second=0, microsecond=0)
            
        time_to_settlement = (next_day.timestamp() - current_time)
        settlement_time_str = next_day.strftime("%H:%M UTC")
        
        return time_to_settlement, settlement_time_str
        
    def determine_settlement_phase(self, time_to_settlement: float) -> FundingSettlementPhase:
        """确定当前所处的结算阶段"""
        if time_to_settlement > 7200:  # >2小时
            return FundingSettlementPhase.FAR_FROM_SETTLEMENT
        elif time_to_settlement > 3600:  # 1-2小时
            return FundingSettlementPhase.APPROACHING_SETTLEMENT
        elif time_to_settlement > 1800:  # 30分钟-1小时
            return FundingSettlementPhase.NEAR_SETTLEMENT
        elif time_to_settlement > 600:   # 10-30分钟
            return FundingSettlementPhase.CLOSE_TO_SETTLEMENT
        else:  # <10分钟
            return FundingSettlementPhase.SETTLEMENT_WINDOW
            
    def should_enable_monitoring(self, time_to_settlement: float) -> bool:
        """判断是否应该启用资金费率监控
        
        策略：结算前10分钟开始监控
        """
        return time_to_settlement <= self.settlement_monitoring_window
        
    def get_cache_duration(self, settlement_phase: FundingSettlementPhase) -> int:
        """获取缓存时长"""
        return self.cache_durations.get(settlement_phase, 300)
        
    def get_monitoring_frequency(self, settlement_phase: FundingSettlementPhase) -> int:
        """获取监控频率"""
        return self.monitoring_frequencies.get(settlement_phase, 60)
        
    def get_opening_threshold(self, settlement_phase: FundingSettlementPhase) -> float:
        """获取开仓阈值"""
        return self.opening_thresholds.get(settlement_phase, 0.006)
        
    def is_funding_rate_cached(self, cache_key: str, settlement_phase: FundingSettlementPhase) -> bool:
        """检查资金费率是否已缓存且有效"""
        if cache_key not in self.funding_rate_cache:
            return False
            
        cache_time = self.cache_timestamps.get(cache_key, 0)
        cache_duration = self.get_cache_duration(settlement_phase)
        
        return (time.time() - cache_time) < cache_duration
        
    def cache_funding_rate(self, cache_key: str, funding_info: FundingRateInfo):
        """缓存资金费率信息"""
        self.funding_rate_cache[cache_key] = funding_info
        self.cache_timestamps[cache_key] = time.time()
        
    def get_cached_funding_rate(self, cache_key: str) -> Optional[FundingRateInfo]:
        """获取缓存的资金费率信息"""
        return self.funding_rate_cache.get(cache_key)
        
    def create_funding_rate_info(self, exchange: str, symbol: str, funding_rate: float,
                                timestamp: float = None) -> FundingRateInfo:
        """创建资金费率信息对象"""
        if timestamp is None:
            timestamp = time.time()
            
        time_to_settlement, _ = self.calculate_time_to_next_settlement(timestamp)
        settlement_phase = self.determine_settlement_phase(time_to_settlement)
        
        return FundingRateInfo(
            exchange=exchange,
            symbol=symbol,
            funding_rate=round(funding_rate, 6),  # 6位精度
            timestamp=timestamp,
            settlement_time=timestamp + time_to_settlement,
            is_favorable=funding_rate > 0,  # >0有利，<0不利
            time_to_settlement=time_to_settlement,
            settlement_phase=settlement_phase
        )
        
    def get_position_key(self, exchange: str, symbol: str) -> str:
        """生成持仓唯一标识"""
        return f"{exchange}_{symbol}"
        
    def get_position_state(self, exchange: str, symbol: str) -> FundingRateAwarenessState:
        """获取持仓的资金费率感知状态"""
        position_key = self.get_position_key(exchange, symbol)
        return self.position_states.get(position_key, FundingRateAwarenessState.NORMAL)
        
    def set_position_state(self, exchange: str, symbol: str, state: FundingRateAwarenessState):
        """设置持仓的资金费率感知状态"""
        position_key = self.get_position_key(exchange, symbol)
        old_state = self.position_states.get(position_key, FundingRateAwarenessState.NORMAL)
        self.position_states[position_key] = state
        
        self.logger.info(f"📊 [状态变更] {position_key}: {old_state.value} → {state.value}")
        
    def increment_reset_count(self, exchange: str, symbol: str) -> int:
        """增加持仓重置次数"""
        position_key = self.get_position_key(exchange, symbol)
        count = self.position_reset_counts.get(position_key, 0) + 1
        self.position_reset_counts[position_key] = count
        return count
        
    def get_reset_count(self, exchange: str, symbol: str) -> int:
        """获取持仓重置次数"""
        position_key = self.get_position_key(exchange, symbol)
        return self.position_reset_counts.get(position_key, 0)
        
    def can_reset_threshold(self, exchange: str, symbol: str) -> bool:
        """检查是否可以重置阈值"""
        return self.get_reset_count(exchange, symbol) < self.max_reset_count
        
    def should_block_opening(self, funding_info: FundingRateInfo) -> Tuple[bool, str]:
        """判断是否应该阻止开仓（策略2）"""
        if not self.enabled or not self.opening_control_enabled:
            return False, "开仓控制未启用"
            
        phase = funding_info.settlement_phase
        
        if phase == FundingSettlementPhase.SETTLEMENT_WINDOW:
            return True, "结算窗口期，阻止开仓"
        elif phase == FundingSettlementPhase.CLOSE_TO_SETTLEMENT:
            if not funding_info.is_favorable:
                return True, "临近结算且资金费率不利，阻止开仓"
                
        return False, "允许开仓"
        
    def get_dynamic_opening_threshold(self, funding_info: FundingRateInfo) -> float:
        """获取动态开仓阈值（策略3）"""
        if not self.enabled or not self.dynamic_threshold_enabled:
            return self.opening_thresholds[FundingSettlementPhase.FAR_FROM_SETTLEMENT]  # 默认0.6%
            
        return self.get_opening_threshold(funding_info.settlement_phase)
        
    def make_closing_decision(self, exchange: str, symbol: str, current_spread: float,
                            funding_info: FundingRateInfo, start_time: float) -> FundingAwarenessDecision:
        """制作平仓决策（策略1）"""
        if not self.enabled or not self.closing_optimization_enabled:
            return FundingAwarenessDecision(
                action="hold",
                reason="资金费率感知未启用，使用原系统逻辑",
                current_state=FundingRateAwarenessState.NORMAL,
                funding_info=funding_info
            )
            
        current_state = self.get_position_state(exchange, symbol)
        
        # 检查是否在监控窗口内
        if not self.should_enable_monitoring(funding_info.time_to_settlement):
            # 距离结算时间较远，继续正常状态
            if current_state != FundingRateAwarenessState.NORMAL:
                self.set_position_state(exchange, symbol, FundingRateAwarenessState.NORMAL)
                return FundingAwarenessDecision(
                    action="hold",
                    reason="距离结算时间较远，恢复正常状态",
                    current_state=FundingRateAwarenessState.NORMAL,
                    funding_info=funding_info
                )
            return FundingAwarenessDecision(
                action="hold",
                reason="距离结算时间较远，正常状态",
                current_state=FundingRateAwarenessState.NORMAL,
                funding_info=funding_info
            )
            
        # 进入监控窗口
        if current_state == FundingRateAwarenessState.NORMAL:
            self.set_position_state(exchange, symbol, FundingRateAwarenessState.MONITORING)
            current_state = FundingRateAwarenessState.MONITORING
            
        # 根据资金费率做决策
        if funding_info.is_favorable:  # 资金费率>0，有利
            if current_state in [FundingRateAwarenessState.MONITORING, FundingRateAwarenessState.UNFAVORABLE]:
                # 从监控或不利状态转为有利状态
                if self.can_reset_threshold(exchange, symbol):
                    self.set_position_state(exchange, symbol, FundingRateAwarenessState.FAVORABLE)
                    self.increment_reset_count(exchange, symbol)
                    
                    return FundingAwarenessDecision(
                        action="reset_threshold",
                        reason=f"资金费率有利({funding_info.funding_rate*100:.4f}%)，重置阈值到-0.5%",
                        current_state=FundingRateAwarenessState.FAVORABLE,
                        funding_info=funding_info,
                        threshold_adjustment=-0.005,  # 重置到-0.5%
                        monitoring_frequency=self.get_monitoring_frequency(funding_info.settlement_phase)
                    )
                else:
                    return FundingAwarenessDecision(
                        action="hold",
                        reason=f"资金费率有利但已达到最大重置次数({self.max_reset_count})",
                        current_state=current_state,
                        funding_info=funding_info
                    )
            elif current_state == FundingRateAwarenessState.FAVORABLE:
                # 继续保持有利状态
                return FundingAwarenessDecision(
                    action="hold",
                    reason=f"继续享受有利资金费率({funding_info.funding_rate*100:.4f}%)",
                    current_state=FundingRateAwarenessState.FAVORABLE,
                    funding_info=funding_info,
                    monitoring_frequency=self.get_monitoring_frequency(funding_info.settlement_phase)
                )
        else:  # 资金费率<0，不利
            if current_state in [FundingRateAwarenessState.MONITORING, FundingRateAwarenessState.FAVORABLE]:
                # 转为不利状态，加速平仓
                self.set_position_state(exchange, symbol, FundingRateAwarenessState.UNFAVORABLE)
                
                return FundingAwarenessDecision(
                    action="accelerate_close",
                    reason=f"资金费率不利({funding_info.funding_rate*100:.4f}%)，加速平仓",
                    current_state=FundingRateAwarenessState.UNFAVORABLE,
                    funding_info=funding_info,
                    threshold_adjustment=-0.0002,  # 加速到-0.02%
                    monitoring_frequency=self.get_monitoring_frequency(funding_info.settlement_phase)
                )
            elif current_state == FundingRateAwarenessState.UNFAVORABLE:
                # 继续加速平仓状态
                return FundingAwarenessDecision(
                    action="accelerate_close",
                    reason=f"继续避免不利资金费率({funding_info.funding_rate*100:.4f}%)",
                    current_state=FundingRateAwarenessState.UNFAVORABLE,
                    funding_info=funding_info,
                    threshold_adjustment=-0.0002,
                    monitoring_frequency=self.get_monitoring_frequency(funding_info.settlement_phase)
                )
                
        # 默认保持当前状态
        return FundingAwarenessDecision(
            action="hold",
            reason="保持当前状态",
            current_state=current_state,
            funding_info=funding_info
        )
        
    def handle_funding_rate_reversal(self, exchange: str, symbol: str, old_funding_info: FundingRateInfo,
                                   new_funding_info: FundingRateInfo) -> Optional[FundingAwarenessDecision]:
        """处理资金费率反转情况"""
        if old_funding_info.is_favorable == new_funding_info.is_favorable:
            return None  # 没有反转
            
        position_key = self.get_position_key(exchange, symbol)
        
        if old_funding_info.is_favorable and not new_funding_info.is_favorable:
            # 有利→不利：立即执行不利策略
            self.logger.warning(f"⚠️ [资金费率反转] {position_key}: 有利→不利 ({old_funding_info.funding_rate*100:.4f}% → {new_funding_info.funding_rate*100:.4f}%)")
            self.set_position_state(exchange, symbol, FundingRateAwarenessState.UNFAVORABLE)
            
            return FundingAwarenessDecision(
                action="accelerate_close",
                reason=f"资金费率反转为不利，立即加速平仓",
                current_state=FundingRateAwarenessState.UNFAVORABLE,
                funding_info=new_funding_info,
                threshold_adjustment=-0.0002
            )
            
        elif not old_funding_info.is_favorable and new_funding_info.is_favorable:
            # 不利→有利：重新执行有利策略
            self.logger.info(f"✅ [资金费率反转] {position_key}: 不利→有利 ({old_funding_info.funding_rate*100:.4f}% → {new_funding_info.funding_rate*100:.4f}%)")
            
            if self.can_reset_threshold(exchange, symbol):
                self.set_position_state(exchange, symbol, FundingRateAwarenessState.FAVORABLE)
                self.increment_reset_count(exchange, symbol)
                
                return FundingAwarenessDecision(
                    action="reset_threshold",
                    reason=f"资金费率反转为有利，重置阈值",
                    current_state=FundingRateAwarenessState.FAVORABLE,
                    funding_info=new_funding_info,
                    threshold_adjustment=-0.005
                )
                
        return None
        
    def should_force_recovery(self, exchange: str, symbol: str, start_time: float) -> bool:
        """检查是否应该强制恢复正常状态"""
        elapsed_time = time.time() - start_time
        return elapsed_time > self.forced_recovery_timeout
        
    def force_recovery(self, exchange: str, symbol: str, reason: str = "强制恢复超时"):
        """强制恢复到正常状态"""
        position_key = self.get_position_key(exchange, symbol)
        old_state = self.get_position_state(exchange, symbol)
        
        self.set_position_state(exchange, symbol, FundingRateAwarenessState.NORMAL)
        self.logger.warning(f"🔄 [强制恢复] {position_key}: {old_state.value} → NORMAL ({reason})")
        
    def cleanup_position(self, exchange: str, symbol: str):
        """清理持仓相关的状态和计数"""
        position_key = self.get_position_key(exchange, symbol)
        
        if position_key in self.position_states:
            del self.position_states[position_key]
        if position_key in self.position_reset_counts:
            del self.position_reset_counts[position_key]
            
        self.logger.info(f"🧹 [清理持仓状态] {position_key}")
        
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态信息"""
        return {
            "enabled": self.enabled,
            "closing_optimization_enabled": self.closing_optimization_enabled,
            "opening_control_enabled": self.opening_control_enabled,
            "dynamic_threshold_enabled": self.dynamic_threshold_enabled,
            "active_positions": len(self.position_states),
            "position_states": {k: v.value for k, v in self.position_states.items()},
            "reset_counts": dict(self.position_reset_counts),
            "cached_funding_rates": len(self.funding_rate_cache),
            "max_reset_count": self.max_reset_count,
            "settlement_monitoring_window": self.settlement_monitoring_window,
            "forced_recovery_timeout": self.forced_recovery_timeout
        }


# 全局单例
_funding_rate_awareness_instance = None

def get_funding_rate_awareness(config: Dict = None) -> FundingRateAwareness:
    """获取资金费率感知系统单例"""
    global _funding_rate_awareness_instance
    if _funding_rate_awareness_instance is None:
        _funding_rate_awareness_instance = FundingRateAwareness(config)
    return _funding_rate_awareness_instance