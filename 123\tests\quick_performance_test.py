#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 核心性能快速验证 - 确保1秒以内执行目标
"""

import asyncio
import time
import sys
import os

# 设置项目路径
project_root = '/root/myproject/123/77 修复了开仓差价变化错误的问题/123'
sys.path.insert(0, project_root)
os.chdir(project_root)

def test_timestamp_fix():
    """验证时间戳修复是否正确"""
    print("🔍 测试1: 验证时间戳修复...")
    
    try:
        # 导入时间戳处理函数
        from websocket.unified_timestamp_processor import calculate_data_age
        
        current_time = time.time()
        timestamp_ms = int(current_time * 1000)
        
        # 测试正确的时间戳处理
        age_seconds = calculate_data_age(timestamp_ms, current_time)
        
        if abs(age_seconds) < 0.1:  # 应该接近0秒
            print(f"✅ 时间戳处理正确: {age_seconds:.3f}秒")
            return True
        else:
            print(f"❌ 时间戳处理错误: {age_seconds:.3f}秒 (应该接近0)")
            return False
            
    except Exception as e:
        print(f"❌ 时间戳测试异常: {e}")
        return False

def test_unified_modules():
    """验证统一模块可用性"""
    print("🔍 测试2: 验证统一模块...")
    
    try:
        # 测试核心统一模块导入
        from core.unified_opening_manager import get_opening_manager
        from core.unified_closing_manager import get_closing_manager
        from core.unified_order_spread_calculator import get_order_spread_calculator
        
        opening_manager = get_opening_manager()
        closing_manager = get_closing_manager()
        spread_calculator = get_order_spread_calculator()
        
        if all([opening_manager, closing_manager, spread_calculator]):
            print("✅ 统一模块导入成功")
            return True
        else:
            print("❌ 统一模块导入失败")
            return False
            
    except Exception as e:
        print(f"❌ 统一模块测试异常: {e}")
        return False

async def test_parallel_execution():
    """验证并行执行能力"""
    print("🔍 测试3: 验证并行执行...")
    
    try:
        async def mock_task(delay_ms, name):
            await asyncio.sleep(delay_ms / 1000.0)
            return f"{name}_done"
        
        start_time = time.time()
        results = await asyncio.gather(
            mock_task(50, "spot"),
            mock_task(50, "futures"),
            mock_task(30, "leverage"),
            return_exceptions=True
        )
        execution_time = (time.time() - start_time) * 1000
        
        # 并行执行应该接近最长任务时间(50ms)，而不是累加时间(130ms)
        if execution_time < 80:  # 允许30ms误差
            print(f"✅ 并行执行正常: {execution_time:.1f}ms")
            return True
        else:
            print(f"❌ 并行执行异常: {execution_time:.1f}ms (应该<80ms)")
            return False
            
    except Exception as e:
        print(f"❌ 并行执行测试异常: {e}")
        return False

def test_data_freshness():
    """验证数据新鲜度阈值"""
    print("🔍 测试4: 验证数据新鲜度阈值...")
    
    try:
        threshold_ms = 1000
        current_time = time.time() * 1000
        
        # 测试新鲜数据（500ms内）
        fresh_data = current_time - 500
        fresh_age = current_time - fresh_data
        fresh_valid = fresh_age <= threshold_ms
        
        # 测试过期数据（1500ms前）
        stale_data = current_time - 1500
        stale_age = current_time - stale_data
        stale_invalid = stale_age > threshold_ms
        
        if fresh_valid and stale_invalid:
            print(f"✅ 数据新鲜度检查正确: 新鲜={fresh_valid}, 过期={stale_invalid}")
            return True
        else:
            print(f"❌ 数据新鲜度检查错误: 新鲜={fresh_valid}, 过期={stale_invalid}")
            return False
            
    except Exception as e:
        print(f"❌ 数据新鲜度测试异常: {e}")
        return False

async def test_execution_engine_performance():
    """验证执行引擎核心性能"""
    print("🔍 测试5: 验证执行引擎性能...")
    
    try:
        # 模拟完整的执行流程
        start_time = time.time()
        
        # 模拟各阶段耗时
        await asyncio.sleep(0.01)  # 数据获取 10ms
        await asyncio.sleep(0.005) # 价差计算 5ms
        await asyncio.sleep(0.003) # 参数准备 3ms
        
        # 并行执行（现货+期货）
        await asyncio.gather(
            asyncio.sleep(0.02),  # 现货 20ms
            asyncio.sleep(0.025), # 期货 25ms
        )
        
        await asyncio.sleep(0.002) # 结果处理 2ms
        
        total_time = (time.time() - start_time) * 1000
        
        if total_time < 100:  # 目标100ms内完成
            print(f"✅ 执行引擎性能良好: {total_time:.1f}ms")
            return True
        else:
            print(f"❌ 执行引擎性能不达标: {total_time:.1f}ms")
            return False
            
    except Exception as e:
        print(f"❌ 执行引擎性能测试异常: {e}")
        return False

async def run_performance_stress_test():
    """运行30秒性能压力测试"""
    print("🔍 测试6: 运行30秒性能压力测试...")
    
    test_duration = 30
    start_time = time.time()
    end_time = start_time + test_duration
    
    test_count = 0
    success_count = 0
    total_execution_time = 0
    
    print(f"开始{test_duration}秒压力测试...")
    
    while time.time() < end_time:
        test_count += 1
        execution_start = time.time()
        
        try:
            # 模拟完整套利流程
            await asyncio.gather(
                asyncio.sleep(0.01),  # 数据获取
                asyncio.sleep(0.005), # 计算
            )
            
            # 并行执行
            await asyncio.gather(
                asyncio.sleep(0.02),  # 现货
                asyncio.sleep(0.025), # 期货
            )
            
            execution_time = (time.time() - execution_start) * 1000
            total_execution_time += execution_time
            success_count += 1
            
            # 每100次输出状态
            if test_count % 100 == 0:
                avg_time = total_execution_time / success_count
                remaining = end_time - time.time()
                print(f"  进度: {test_count}次, 平均: {avg_time:.1f}ms, 剩余: {remaining:.0f}s")
            
            await asyncio.sleep(0.01)  # 控制频率
            
        except Exception as e:
            print(f"  第{test_count}次测试异常: {e}")
    
    if success_count > 0:
        avg_execution_time = total_execution_time / success_count
        success_rate = success_count / test_count * 100
        
        print(f"✅ 压力测试完成:")
        print(f"  总次数: {test_count}")
        print(f"  成功: {success_count} ({success_rate:.1f}%)")
        print(f"  平均耗时: {avg_execution_time:.1f}ms")
        
        # 性能目标：平均执行时间<100ms
        return avg_execution_time < 100
    
    return False

async def main():
    """主测试流程"""
    print("🚀 开始核心性能快速验证...")
    print("=" * 60)
    
    # 同步测试
    tests = [
        ("时间戳修复", test_timestamp_fix),
        ("统一模块", test_unified_modules),
        ("数据新鲜度", test_data_freshness),
    ]
    
    sync_results = []
    for name, test_func in tests:
        try:
            result = test_func()
            sync_results.append(result)
            print()
        except Exception as e:
            print(f"❌ {name}测试异常: {e}")
            sync_results.append(False)
            print()
    
    # 异步测试
    async_tests = [
        ("并行执行", test_parallel_execution),
        ("执行引擎性能", test_execution_engine_performance),
        ("压力测试", run_performance_stress_test),
    ]
    
    async_results = []
    for name, test_func in async_tests:
        try:
            result = await test_func()
            async_results.append(result)
            print()
        except Exception as e:
            print(f"❌ {name}测试异常: {e}")
            async_results.append(False)
            print()
    
    # 汇总结果
    all_results = sync_results + async_results
    passed_count = sum(all_results)
    total_count = len(all_results)
    success_rate = passed_count / total_count * 100
    
    print("=" * 60)
    print("🔥 核心性能验证结果:")
    print(f"📊 通过: {passed_count}/{total_count} ({success_rate:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！系统已准备好实现1秒以内执行目标！")
        return True
    else:
        print("⚠️  存在问题需要修复")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)