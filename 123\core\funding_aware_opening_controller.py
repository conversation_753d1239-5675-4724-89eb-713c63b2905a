#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金费率感知开仓控制器
实现策略2和策略3：
- 策略2: 资金费率结算前开仓控制
- 策略3: 动态开仓阈值策略

核心功能：
1. 根据距离结算时间动态调整开仓阈值
2. 在不利资金费率临近结算时阻止开仓
3. 智能时间窗口管理
4. 风险评估和补偿定价
"""

import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from utils.logger import get_logger
from core.funding_rate_awareness import (
    FundingRateAwareness, 
    FundingSettlementPhase,
    get_funding_rate_awareness
)


@dataclass
class OpeningControlDecision:
    """开仓控制决策结果"""
    allow_opening: bool
    required_threshold: float  # 所需最低开仓阈值
    current_phase: FundingSettlementPhase
    time_to_settlement: float
    risk_premium: float  # 风险溢价
    decision_reason: str
    funding_rate: Optional[float] = None
    is_funding_favorable: Optional[bool] = None


class FundingAwareOpeningController:
    """
    资金费率感知开仓控制器
    
    实现两大策略：
    1. 结算前开仓控制：避免在不利时机开仓
    2. 动态开仓阈值：根据结算阶段调整阈值要求
    """
    
    def __init__(self, config: Dict = None):
        """初始化开仓控制器"""
        self.logger = get_logger(self.__class__.__name__)
        self.config = config or {}
        self.funding_awareness = get_funding_rate_awareness()
        
        # 功能开关
        self.enabled = self._get_config_bool("FUNDING_OPENING_CONTROL_ENABLED", True)
        self.dynamic_threshold_enabled = self._get_config_bool("FUNDING_DYNAMIC_THRESHOLD_ENABLED", True)
        self.strict_mode = self._get_config_bool("FUNDING_OPENING_STRICT_MODE", False)
        
        # 基础阈值配置 (与funding_rate_awareness.py保持一致)
        self.base_threshold = self._get_config_float("BASE_OPENING_THRESHOLD", 0.006)  # 0.6%基础阈值
        
        # 动态阈值配置
        self.phase_thresholds = {
            FundingSettlementPhase.FAR_FROM_SETTLEMENT: self._get_config_float("THRESHOLD_FAR", 0.006),      # 0.6%
            FundingSettlementPhase.APPROACHING_SETTLEMENT: self._get_config_float("THRESHOLD_APPROACHING", 0.007),  # 0.7%
            FundingSettlementPhase.NEAR_SETTLEMENT: self._get_config_float("THRESHOLD_NEAR", 0.008),        # 0.8%
            FundingSettlementPhase.CLOSE_TO_SETTLEMENT: self._get_config_float("THRESHOLD_CLOSE", 0.010),   # 1.0%
            FundingSettlementPhase.SETTLEMENT_WINDOW: self._get_config_float("THRESHOLD_SETTLEMENT", 0.015)  # 1.5%
        }
        
        # 风险评估配置
        self.risk_premiums = {
            FundingSettlementPhase.FAR_FROM_SETTLEMENT: 0.0,        # 无风险溢价
            FundingSettlementPhase.APPROACHING_SETTLEMENT: 0.001,   # 0.1%溢价
            FundingSettlementPhase.NEAR_SETTLEMENT: 0.002,          # 0.2%溢价
            FundingSettlementPhase.CLOSE_TO_SETTLEMENT: 0.004,      # 0.4%溢价
            FundingSettlementPhase.SETTLEMENT_WINDOW: 0.009         # 0.9%溢价(基本阻止开仓)
        }
        
        # 阻止开仓的时间窗口
        self.blocking_phases = {
            FundingSettlementPhase.SETTLEMENT_WINDOW: True,  # 结算窗口期完全阻止
        }
        
        # 有条件阻止开仓的配置
        self.conditional_blocking = {
            FundingSettlementPhase.CLOSE_TO_SETTLEMENT: {
                "block_if_unfavorable": True,  # 资金费率不利时阻止
                "allow_if_favorable": True     # 资金费率有利时允许(但阈值较高)
            }
        }
        
        self.logger.info("✅ 资金费率感知开仓控制器初始化完成")
        self.logger.info(f"   总开关: {'启用' if self.enabled else '禁用'}")
        self.logger.info(f"   动态阈值: {'启用' if self.dynamic_threshold_enabled else '禁用'}")
        self.logger.info(f"   严格模式: {'启用' if self.strict_mode else '禁用'}")
        self.logger.info(f"   基础阈值: {self.base_threshold*100:.2f}%")
        
    def _get_config_bool(self, key: str, default: bool) -> bool:
        """获取布尔配置值"""
        import os
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
        
    def _get_config_float(self, key: str, default: float) -> float:
        """获取浮点配置值"""
        import os
        try:
            return float(os.getenv(key, str(default)))
        except ValueError:
            return default
            
    def get_required_threshold_for_phase(self, phase: FundingSettlementPhase) -> float:
        """获取指定阶段的所需开仓阈值"""
        if not self.dynamic_threshold_enabled:
            return self.base_threshold
            
        return self.phase_thresholds.get(phase, self.base_threshold)
        
    def get_risk_premium_for_phase(self, phase: FundingSettlementPhase) -> float:
        """获取指定阶段的风险溢价"""
        return self.risk_premiums.get(phase, 0.0)
        
    def should_block_opening_for_phase(self, phase: FundingSettlementPhase, 
                                     funding_favorable: Optional[bool] = None) -> Tuple[bool, str]:
        """判断指定阶段是否应该阻止开仓"""
        # 完全阻止的阶段
        if phase in self.blocking_phases and self.blocking_phases[phase]:
            return True, f"结算窗口期({phase.value})，完全阻止开仓"
            
        # 有条件阻止的阶段
        if phase in self.conditional_blocking:
            conditions = self.conditional_blocking[phase]
            
            if funding_favorable is None:
                # 资金费率信息不可用，谨慎处理
                if self.strict_mode:
                    return True, f"{phase.value}期间资金费率信息不可用，严格模式阻止开仓"
                else:
                    return False, f"{phase.value}期间资金费率信息不可用，宽松模式允许开仓"
                    
            if not funding_favorable and conditions.get("block_if_unfavorable", False):
                return True, f"{phase.value}期间资金费率不利，阻止开仓"
                
        return False, "允许开仓"
        
    async def evaluate_opening_opportunity(self, exchange: str, symbol: str, 
                                         current_spread: float) -> OpeningControlDecision:
        """
        评估开仓机会
        
        综合考虑：
        1. 距离结算时间
        2. 资金费率有利/不利情况
        3. 当前价差是否满足动态阈值要求
        4. 风险评估和溢价要求
        
        Args:
            exchange: 交易所名称
            symbol: 交易对符号
            current_spread: 当前价差
            
        Returns:
            OpeningControlDecision: 开仓控制决策
        """
        if not self.enabled:
            # 功能未启用，使用基础阈值
            return OpeningControlDecision(
                allow_opening=abs(current_spread) >= self.base_threshold,
                required_threshold=self.base_threshold,
                current_phase=FundingSettlementPhase.FAR_FROM_SETTLEMENT,
                time_to_settlement=float('inf'),
                risk_premium=0.0,
                decision_reason="资金费率感知未启用，使用基础阈值",
                funding_rate=None,
                is_funding_favorable=None
            )
            
        try:
            # 获取资金费率信息
            from core.unified_funding_rate_provider import get_funding_rate_provider
            funding_provider = get_funding_rate_provider()
            
            funding_response = await funding_provider.get_funding_rate(exchange, symbol, use_cache=True)
            
            # 计算时间相关信息
            time_to_settlement, _ = self.funding_awareness.calculate_time_to_next_settlement()
            current_phase = self.funding_awareness.determine_settlement_phase(time_to_settlement)
            
            # 获取阶段相关配置
            required_threshold = self.get_required_threshold_for_phase(current_phase)
            risk_premium = self.get_risk_premium_for_phase(current_phase)
            
            # 资金费率信息
            funding_rate = None
            is_funding_favorable = None
            funding_reason = ""
            
            if funding_response.success:
                funding_rate = funding_response.funding_rate
                is_funding_favorable = funding_rate > 0
                funding_reason = f"资金费率{funding_rate*10000:.2f}bp({'有利' if is_funding_favorable else '不利'})"
            else:
                funding_reason = f"资金费率获取失败: {funding_response.error_message}"
                
            # 检查是否应该阻止开仓
            should_block, block_reason = self.should_block_opening_for_phase(current_phase, is_funding_favorable)
            
            if should_block:
                return OpeningControlDecision(
                    allow_opening=False,
                    required_threshold=required_threshold,
                    current_phase=current_phase,
                    time_to_settlement=time_to_settlement,
                    risk_premium=risk_premium,
                    decision_reason=f"阻止开仓: {block_reason} | {funding_reason}",
                    funding_rate=funding_rate,
                    is_funding_favorable=is_funding_favorable
                )
                
            # 检查价差是否满足动态阈值要求
            abs_spread = abs(current_spread)
            meets_threshold = abs_spread >= required_threshold
            
            # 构建决策理由
            if meets_threshold:
                decision_reason = f"允许开仓: 价差{abs_spread*100:.3f}% >= 阈值{required_threshold*100:.2f}% | {current_phase.value} | {funding_reason}"
            else:
                decision_reason = f"价差不足: {abs_spread*100:.3f}% < 阈值{required_threshold*100:.2f}% | {current_phase.value} | {funding_reason}"
                
            return OpeningControlDecision(
                allow_opening=meets_threshold,
                required_threshold=required_threshold,
                current_phase=current_phase,
                time_to_settlement=time_to_settlement,
                risk_premium=risk_premium,
                decision_reason=decision_reason,
                funding_rate=funding_rate,
                is_funding_favorable=is_funding_favorable
            )
            
        except Exception as e:
            self.logger.error(f"❌ 开仓机会评估异常: {exchange}_{symbol} - {e}")
            
            # 异常情况，降级到基础逻辑
            meets_base_threshold = abs(current_spread) >= self.base_threshold
            
            return OpeningControlDecision(
                allow_opening=meets_base_threshold,
                required_threshold=self.base_threshold,
                current_phase=FundingSettlementPhase.FAR_FROM_SETTLEMENT,
                time_to_settlement=0.0,
                risk_premium=0.0,
                decision_reason=f"评估异常，降级到基础逻辑: {str(e)}",
                funding_rate=None,
                is_funding_favorable=None
            )
            
    async def get_current_opening_requirements(self, exchange: str, symbol: str) -> Dict[str, Any]:
        """获取当前开仓要求信息"""
        try:
            # 计算时间信息
            time_to_settlement, settlement_time_str = self.funding_awareness.calculate_time_to_next_settlement()
            current_phase = self.funding_awareness.determine_settlement_phase(time_to_settlement)
            
            # 获取配置
            required_threshold = self.get_required_threshold_for_phase(current_phase)
            risk_premium = self.get_risk_premium_for_phase(current_phase)
            
            # 获取资金费率信息
            from core.unified_funding_rate_provider import get_funding_rate_provider
            funding_provider = get_funding_rate_provider()
            funding_response = await funding_provider.get_funding_rate(exchange, symbol, use_cache=True)
            
            # 检查阻止条件
            funding_favorable = None
            if funding_response.success:
                funding_favorable = funding_response.funding_rate > 0
                
            should_block, block_reason = self.should_block_opening_for_phase(current_phase, funding_favorable)
            
            return {
                "enabled": self.enabled,
                "dynamic_threshold_enabled": self.dynamic_threshold_enabled,
                "current_phase": current_phase.value,
                "time_to_settlement": time_to_settlement,
                "next_settlement": settlement_time_str,
                "required_threshold": required_threshold,
                "required_threshold_percent": required_threshold * 100,
                "base_threshold": self.base_threshold,
                "base_threshold_percent": self.base_threshold * 100,
                "risk_premium": risk_premium,
                "risk_premium_percent": risk_premium * 100,
                "should_block_opening": should_block,
                "block_reason": block_reason if should_block else "不阻止",
                "funding_rate": funding_response.funding_rate if funding_response.success else None,
                "funding_rate_bp": funding_response.funding_rate * 10000 if funding_response.success else None,
                "funding_favorable": funding_favorable,
                "funding_success": funding_response.success,
                "funding_error": funding_response.error_message if not funding_response.success else None
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取开仓要求信息异常: {exchange}_{symbol} - {e}")
            return {
                "enabled": self.enabled,
                "error": str(e),
                "base_threshold": self.base_threshold,
                "base_threshold_percent": self.base_threshold * 100
            }
            
    def get_threshold_adjustment_ratio(self, base_spread: float, phase: FundingSettlementPhase) -> float:
        """计算阈值调整比率
        
        Returns:
            float: 调整比率，例如1.33表示阈值提高33%
        """
        if not self.dynamic_threshold_enabled:
            return 1.0
            
        required_threshold = self.get_required_threshold_for_phase(phase)
        return required_threshold / self.base_threshold if self.base_threshold > 0 else 1.0
        
    def get_all_phase_thresholds(self) -> Dict[str, float]:
        """获取所有阶段的阈值配置"""
        return {
            phase.value: {
                "threshold": self.phase_thresholds.get(phase, self.base_threshold),
                "threshold_percent": self.phase_thresholds.get(phase, self.base_threshold) * 100,
                "risk_premium": self.risk_premiums.get(phase, 0.0),
                "risk_premium_percent": self.risk_premiums.get(phase, 0.0) * 100,
                "blocks_opening": phase in self.blocking_phases and self.blocking_phases[phase]
            }
            for phase in FundingSettlementPhase
        }
        
    async def validate_opening_with_funding_awareness(self, exchange: str, symbol: str, 
                                                    current_spread: float, 
                                                    min_required_spread: float = None) -> Tuple[bool, str, float]:
        """
        资金费率感知开仓验证
        
        Args:
            exchange: 交易所
            symbol: 交易对
            current_spread: 当前价差
            min_required_spread: 最小要求价差(可选，用于覆盖系统计算)
            
        Returns:
            Tuple[bool, str, float]: (是否允许开仓, 原因, 实际使用的阈值)
        """
        decision = await self.evaluate_opening_opportunity(exchange, symbol, current_spread)
        
        # 如果指定了最小要求价差，使用两者中的较大值
        final_threshold = decision.required_threshold
        if min_required_spread is not None:
            final_threshold = max(final_threshold, min_required_spread)
            
        # 重新检查是否满足最终阈值
        if abs(current_spread) < final_threshold:
            reason = f"价差{abs(current_spread)*100:.3f}%不足，需要{final_threshold*100:.3f}% | {decision.decision_reason}"
            return False, reason, final_threshold
            
        return decision.allow_opening, decision.decision_reason, final_threshold


# 全局单例
_opening_controller_instance = None

def get_funding_aware_opening_controller(config: Dict = None) -> FundingAwareOpeningController:
    """获取资金费率感知开仓控制器单例"""
    global _opening_controller_instance
    if _opening_controller_instance is None:
        _opening_controller_instance = FundingAwareOpeningController(config)
    return _opening_controller_instance