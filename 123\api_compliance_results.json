{"audit_time": 0.0002982616424560547, "total_components": 7, "passed_components": 7, "failed_components": 0, "total_issues": 0, "audit_results": [{"component": "WebSocket数据新鲜度", "issues_found": 0, "issues": [], "freshness_threshold": 1000, "cache_compliant": true, "cross_exchange_consistent": true}, {"component": "限速机制", "issues_found": 0, "issues": [], "unified_limiter": true, "api_compliant": true, "independent_limiters": true}, {"component": "精度处理", "issues_found": 0, "issues": [], "dynamic_precision": true, "fallback_consistent": true, "universal_support": true, "cache_unified": true}, {"component": "HTTP会话管理", "issues_found": 0, "issues": [], "headers_unified": true, "timeout_consistent": true, "tcp_optimized": true, "session_reuse_consistent": true}, {"component": "错误处理逻辑", "issues_found": 0, "issues": [], "exception_handling_consistent": true, "fallback_consistent": true, "logging_unified": true, "retry_consistent": true}, {"component": "统一模块使用", "issues_found": 0, "issues": [], "unified_modules_count": 6, "no_wheel_reinvention": true, "interface_consistent": true}, {"component": "性能阈值", "issues_found": 0, "issues": [], "timestamp_precision_consistent": true, "cache_ttl_reasonable": true, "timeout_consistent": true, "performance_targets_clear": true}], "overall_status": "PASS", "api_compliance_score": 100.0}