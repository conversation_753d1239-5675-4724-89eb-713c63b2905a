#!/usr/bin/env python3
"""
🏛️ 机构级别基础核心测试：模块单元功能验证
验证瞬时时间戳标记系统的核心模块功能

测试覆盖：
1. 参数输入输出验证
2. 边界检查
3. 错误处理
4. 修复点本身100%稳定性
"""

import sys
import os
import time
import asyncio
import json
from typing import Dict, Any, List

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

class InstitutionalBasicCoreTest:
    """机构级别基础核心测试器"""
    
    def __init__(self):
        self.test_results = {
            "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_type": "基础核心测试",
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": []
        }
    
    def run_test(self, test_name: str, test_func, *args, **kwargs):
        """运行单个测试"""
        self.test_results["total_tests"] += 1
        
        try:
            result = test_func(*args, **kwargs)
            if result:
                self.test_results["passed_tests"] += 1
                status = "PASS"
                print(f"✅ {test_name}: PASS")
            else:
                self.test_results["failed_tests"] += 1
                status = "FAIL"
                print(f"❌ {test_name}: FAIL")
                
            self.test_results["test_details"].append({
                "name": test_name,
                "status": status,
                "result": result
            })
            
        except Exception as e:
            self.test_results["failed_tests"] += 1
            status = "ERROR"
            print(f"💥 {test_name}: ERROR - {e}")
            
            self.test_results["test_details"].append({
                "name": test_name,
                "status": status,
                "error": str(e)
            })
    
    def test_timestamp_processor_initialization(self) -> bool:
        """测试时间戳处理器初始化"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            # 测试三个交易所的初始化
            for exchange in ["gate", "okx", "bybit"]:
                processor = get_timestamp_processor(exchange)
                
                # 验证基本属性
                if not hasattr(processor, 'exchange_name'):
                    return False
                if processor.exchange_name.lower() != exchange:
                    return False
                if not hasattr(processor, 'get_synced_timestamp'):
                    return False
                if not hasattr(processor, '_get_network_delay_compensation'):
                    return False
            
            return True
            
        except Exception as e:
            print(f"初始化测试异常: {e}")
            return False
    
    def test_websocket_receive_timestamp_priority(self) -> bool:
        """测试WebSocket接收时间戳优先级"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            current_time = int(time.time() * 1000)
            
            for exchange in ["gate", "okx", "bybit"]:
                processor = get_timestamp_processor(exchange)
                
                # 测试数据：WebSocket接收时间戳 vs 历史时间戳
                test_data = {
                    'receive_timestamp_ms': current_time,
                    't': current_time - 5000,  # Gate.io历史时间戳
                    'ts': current_time - 5000  # OKX/Bybit历史时间戳
                }
                
                result = processor.get_synced_timestamp(test_data)
                
                # 验证使用了WebSocket接收时间戳（差异应该很小）
                time_diff = abs(result - current_time)
                if time_diff > 200:  # 超过200ms认为失败
                    print(f"{exchange}时间戳优先级测试失败: 差异{time_diff}ms")
                    return False
            
            return True
            
        except Exception as e:
            print(f"时间戳优先级测试异常: {e}")
            return False
    
    def test_network_delay_compensation(self) -> bool:
        """测试网络延迟补偿功能"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            expected_delays = {
                "gate": 50,
                "okx": 30,
                "bybit": 40
            }
            
            for exchange, expected_delay in expected_delays.items():
                processor = get_timestamp_processor(exchange)
                actual_delay = processor._get_network_delay_compensation()
                
                # 验证延迟补偿值
                if actual_delay != expected_delay:
                    print(f"{exchange}网络延迟补偿错误: 期望{expected_delay}ms, 实际{actual_delay}ms")
                    return False
            
            return True
            
        except Exception as e:
            print(f"网络延迟补偿测试异常: {e}")
            return False
    
    def test_boundary_conditions(self) -> bool:
        """测试边界条件"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            processor = get_timestamp_processor("gate")
            
            # 测试1：空数据
            result1 = processor.get_synced_timestamp(None)
            if not isinstance(result1, int) or result1 <= 0:
                return False
            
            # 测试2：空字典
            result2 = processor.get_synced_timestamp({})
            if not isinstance(result2, int) or result2 <= 0:
                return False
            
            # 测试3：无效时间戳
            result3 = processor.get_synced_timestamp({"receive_timestamp_ms": -1})
            if not isinstance(result3, int) or result3 <= 0:
                return False
            
            # 测试4：超大时间戳
            future_time = int(time.time() * 1000) + 86400000  # 未来1天
            result4 = processor.get_synced_timestamp({"receive_timestamp_ms": future_time})
            if not isinstance(result4, int):
                return False
            
            return True
            
        except Exception as e:
            print(f"边界条件测试异常: {e}")
            return False
    
    def test_error_handling(self) -> bool:
        """测试错误处理"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            processor = get_timestamp_processor("gate")
            
            # 测试1：无效数据类型
            result1 = processor.get_synced_timestamp("invalid_string")
            if not isinstance(result1, int):
                return False
            
            # 测试2：循环引用数据
            circular_data = {}
            circular_data["self"] = circular_data
            result2 = processor.get_synced_timestamp(circular_data)
            if not isinstance(result2, int):
                return False
            
            # 测试3：超大数据结构
            large_data = {"data": "x" * 1000000, "receive_timestamp_ms": int(time.time() * 1000)}
            result3 = processor.get_synced_timestamp(large_data)
            if not isinstance(result3, int):
                return False
            
            return True
            
        except Exception as e:
            print(f"错误处理测试异常: {e}")
            return False
    
    def test_thread_safety(self) -> bool:
        """测试线程安全性"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            import threading
            import concurrent.futures
            
            processor = get_timestamp_processor("gate")
            current_time = int(time.time() * 1000)
            
            def worker():
                test_data = {"receive_timestamp_ms": current_time}
                return processor.get_synced_timestamp(test_data)
            
            # 并发测试
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(worker) for _ in range(100)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            # 验证所有结果都是有效的时间戳
            for result in results:
                if not isinstance(result, int) or result <= 0:
                    return False
                # 验证时间戳在合理范围内
                if abs(result - current_time) > 1000:  # 1秒容差
                    return False
            
            return True
            
        except Exception as e:
            print(f"线程安全测试异常: {e}")
            return False
    
    def test_memory_usage(self) -> bool:
        """测试内存使用"""
        try:
            import psutil
            import gc
            
            # 获取初始内存使用
            process = psutil.Process()
            initial_memory = process.memory_info().rss
            
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            # 大量创建和使用时间戳处理器
            for i in range(1000):
                for exchange in ["gate", "okx", "bybit"]:
                    processor = get_timestamp_processor(exchange)
                    current_time = int(time.time() * 1000)
                    test_data = {"receive_timestamp_ms": current_time}
                    result = processor.get_synced_timestamp(test_data)
            
            # 强制垃圾回收
            gc.collect()
            
            # 检查内存使用
            final_memory = process.memory_info().rss
            memory_increase = final_memory - initial_memory
            
            # 内存增长不应超过50MB
            if memory_increase > 50 * 1024 * 1024:
                print(f"内存使用过多: 增长{memory_increase / 1024 / 1024:.1f}MB")
                return False
            
            return True
            
        except ImportError:
            print("psutil未安装，跳过内存测试")
            return True
        except Exception as e:
            print(f"内存使用测试异常: {e}")
            return False
    
    def test_performance_benchmarks(self) -> bool:
        """测试性能基准"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            processor = get_timestamp_processor("gate")
            current_time = int(time.time() * 1000)
            test_data = {"receive_timestamp_ms": current_time}
            
            # 性能测试：1000次调用
            start_time = time.time()
            for _ in range(1000):
                result = processor.get_synced_timestamp(test_data)
            end_time = time.time()
            
            total_time = end_time - start_time
            avg_time_ms = (total_time / 1000) * 1000
            
            # 平均每次调用不应超过1ms
            if avg_time_ms > 1.0:
                print(f"性能不达标: 平均{avg_time_ms:.3f}ms/次")
                return False
            
            print(f"性能测试通过: 平均{avg_time_ms:.3f}ms/次")
            return True
            
        except Exception as e:
            print(f"性能测试异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有基础核心测试"""
        print("🏛️ 开始机构级别基础核心测试")
        print("="*60)
        
        # 运行所有测试
        self.run_test("时间戳处理器初始化", self.test_timestamp_processor_initialization)
        self.run_test("WebSocket接收时间戳优先级", self.test_websocket_receive_timestamp_priority)
        self.run_test("网络延迟补偿功能", self.test_network_delay_compensation)
        self.run_test("边界条件处理", self.test_boundary_conditions)
        self.run_test("错误处理机制", self.test_error_handling)
        self.run_test("线程安全性", self.test_thread_safety)
        self.run_test("内存使用控制", self.test_memory_usage)
        self.run_test("性能基准测试", self.test_performance_benchmarks)
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        success_rate = (self.test_results["passed_tests"] / self.test_results["total_tests"]) * 100
        
        print("\n" + "="*60)
        print("🏛️ 基础核心测试报告")
        print("="*60)
        print(f"总测试数: {self.test_results['total_tests']}")
        print(f"通过测试: {self.test_results['passed_tests']}")
        print(f"失败测试: {self.test_results['failed_tests']}")
        print(f"成功率: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("🎉 基础核心测试100%通过！修复点本身100%稳定！")
        else:
            print("⚠️ 存在测试失败，需要进一步修复")
            
        # 保存测试结果
        timestamp = int(time.time())
        filename = f"institutional_basic_core_test_{timestamp}.json"
        filepath = os.path.join("diagnostic_scripts", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 测试结果已保存到: {filepath}")
        
        return success_rate == 100

def main():
    """主函数"""
    tester = InstitutionalBasicCoreTest()
    success = tester.run_all_tests()
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
