#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 统一缓存监控系统

按照【期现套利系统修复指令】的要求，这是唯一的缓存监控实现：
- 统一监控所有缓存系统的性能
- 支持5大缓存系统的统计
- 无重复链路，统一入口

支持的缓存类型：
1. balance_cache - 余额缓存（ArbitrageEngine）
2. margin_cache - 保证金缓存（MarginCalculator）
3. trading_rules_cache - 交易规则缓存（TradingRulesPreloader）
4. hedge_quality_cache - 对冲质量缓存（TradingRulesPreloader）
5. precision_cache - 精度缓存（TradingRulesPreloader）

注意：orderbook_cache已删除，统一使用OpportunityScanner.market_data
"""

import time
import logging
from typing import Dict, Any
from datetime import datetime

class CacheMonitor:
    """5大缓存系统监控器"""
    
    def __init__(self):
        # 创建专门的缓存日志
        self.cache_logger = self._setup_cache_logger()
        
        # 缓存状态统计
        self.cache_stats = {
            "balance_cache": {"hits": 0, "misses": 0, "updates": 0, "last_update": None},
            "margin_cache": {"hits": 0, "misses": 0, "updates": 0, "last_update": None},
            "trading_rules_cache": {"hits": 0, "misses": 0, "updates": 0, "last_update": None},
            "hedge_quality_cache": {"hits": 0, "misses": 0, "updates": 0, "last_update": None},
            "precision_cache": {"hits": 0, "misses": 0, "updates": 0, "last_update": None}
        }

        # 启动监控
        self.cache_logger.info("🚀 5大缓存系统监控器启动")
        self.cache_logger.info("=" * 80)
        
    def _setup_cache_logger(self) -> logging.Logger:
        """设置缓存专用日志 - 🔥 修复：使用统一日志系统"""
        # 🔥 关键修复：使用统一日志系统，不创建独立的处理器
        logger = logging.getLogger("CacheMonitor")
        logger.setLevel(logging.INFO)

        # 🔥 确保日志传播到统一日志系统
        logger.propagate = True

        # 🔥 不再创建独立的处理器，使用统一日志系统的处理器
        # 这样CacheMonitor的日志会被正确过滤到cache_monitor.log文件

        return logger
    
    def log_balance_cache_hit(self, exchange: str, cache_key: str, value: float):
        """记录余额缓存命中"""
        self.cache_stats["balance_cache"]["hits"] += 1
        self.cache_stats["balance_cache"]["last_update"] = time.time()
        
        self.cache_logger.info(f"💰 [余额缓存] 命中: {exchange} {cache_key} = ${value:.2f}")
    
    def log_balance_cache_miss(self, exchange: str, cache_key: str):
        """记录余额缓存未命中"""
        self.cache_stats["balance_cache"]["misses"] += 1
        
        self.cache_logger.info(f"💰 [余额缓存] 未命中: {exchange} {cache_key} - 需要API获取")
    
    def log_balance_cache_update(self, exchange: str, cache_key: str, old_value: float, new_value: float):
        """记录余额缓存更新"""
        self.cache_stats["balance_cache"]["updates"] += 1
        self.cache_stats["balance_cache"]["last_update"] = time.time()
        
        change = new_value - old_value
        change_str = f"+${change:.2f}" if change >= 0 else f"-${abs(change):.2f}"
        
        self.cache_logger.info(f"💰 [余额缓存] 更新: {exchange} {cache_key} ${old_value:.2f} -> ${new_value:.2f} ({change_str})")
    
    def log_margin_cache_hit(self, exchange: str, symbol: str, contract_info: Dict[str, Any]):
        """记录保证金缓存命中"""
        self.cache_stats["margin_cache"]["hits"] += 1
        self.cache_stats["margin_cache"]["last_update"] = time.time()
        
        margin_ratio = contract_info.get("margin_ratio", 0)
        self.cache_logger.info(f"📊 [保证金缓存] 命中: {exchange} {symbol} 保证金比例={margin_ratio}")
    
    def log_margin_cache_miss(self, exchange: str, symbol: str):
        """记录保证金缓存未命中"""
        self.cache_stats["margin_cache"]["misses"] += 1
        
        self.cache_logger.info(f"📊 [保证金缓存] 未命中: {exchange} {symbol} - 需要API获取")
    
    def log_trading_rules_cache_hit(self, exchange: str, symbol: str, market_type: str, rule: Any):
        """记录交易规则缓存命中"""
        self.cache_stats["trading_rules_cache"]["hits"] += 1
        self.cache_stats["trading_rules_cache"]["last_update"] = time.time()
        
        step_size = getattr(rule, 'step_size', 'N/A')
        price_step = getattr(rule, 'price_step', 'N/A')
        
        self.cache_logger.info(f"📋 [交易规则缓存] 命中: {exchange} {symbol} {market_type} 步长={step_size} 价格步长={price_step}")
    
    def log_trading_rules_cache_miss(self, exchange: str, symbol: str, market_type: str):
        """记录交易规则缓存未命中"""
        self.cache_stats["trading_rules_cache"]["misses"] += 1
        
        self.cache_logger.info(f"📋 [交易规则缓存] 未命中: {exchange} {symbol} {market_type} - 需要API获取")
    
    def log_orderbook_cache_hit(self, exchange: str, symbol: str, market_type: str, orderbook: Dict[str, Any]):
        """记录订单簿缓存命中"""
        self.cache_stats["orderbook_cache"]["hits"] += 1
        self.cache_stats["orderbook_cache"]["last_update"] = time.time()
        
        asks_count = len(orderbook.get("asks", []))
        bids_count = len(orderbook.get("bids", []))
        
        self.cache_logger.info(f"📖 [订单簿缓存] 命中: {exchange} {symbol} {market_type} asks={asks_count} bids={bids_count}")
    
    def log_orderbook_cache_miss(self, exchange: str, symbol: str, market_type: str):
        """记录订单簿缓存未命中"""
        self.cache_stats["orderbook_cache"]["misses"] += 1
        
        self.cache_logger.info(f"📖 [订单簿缓存] 未命中: {exchange} {symbol} {market_type} - 需要API获取")
    
    def log_orderbook_api_call(self, exchange: str, symbol: str, market_type: str, duration_ms: float):
        """记录订单簿API调用"""
        self.cache_stats["orderbook_cache"]["updates"] += 1
        
        self.cache_logger.info(f"📖 [订单簿API] 调用: {exchange} {symbol} {market_type} 耗时={duration_ms:.1f}ms")
    
    def log_hedge_quality_cache_hit(self, spot_exchange: str, futures_exchange: str, symbol: str, hedge_quality: Dict[str, Any]):
        """记录对冲质量缓存命中"""
        self.cache_stats["hedge_quality_cache"]["hits"] += 1
        self.cache_stats["hedge_quality_cache"]["last_update"] = time.time()
        
        hedge_ratio = hedge_quality.get("hedge_ratio", 0)
        is_good = hedge_quality.get("is_good_hedge", False)
        
        self.cache_logger.info(f"⚖️ [对冲质量缓存] 命中: {spot_exchange}+{futures_exchange} {symbol} 比例={hedge_ratio:.4f} {'✅良好' if is_good else '❌不佳'}")
    
    def log_hedge_quality_cache_miss(self, spot_exchange: str, futures_exchange: str, symbol: str):
        """记录对冲质量缓存未命中"""
        self.cache_stats["hedge_quality_cache"]["misses"] += 1
        
        self.cache_logger.info(f"⚖️ [对冲质量缓存] 未命中: {spot_exchange}+{futures_exchange} {symbol} - 需要计算")
    
    def log_hedge_quality_calculation(self, spot_exchange: str, futures_exchange: str, symbol: str, 
                                    spot_amount: float, futures_amount: float, hedge_ratio: float):
        """记录对冲质量计算"""
        self.cache_stats["hedge_quality_cache"]["updates"] += 1
        
        self.cache_logger.info(f"⚖️ [对冲质量计算] {spot_exchange}+{futures_exchange} {symbol} 现货={spot_amount:.6f} 期货={futures_amount:.6f} 比例={hedge_ratio:.4f}")
    
    def log_precision_cache_hit(self, exchange: str, symbol: str, market_type: str, formatted_amount: str):
        """记录精度缓存命中"""
        self.cache_stats["precision_cache"]["hits"] += 1
        self.cache_stats["precision_cache"]["last_update"] = time.time()
        
        self.cache_logger.info(f"🎯 [精度缓存] 命中: {exchange} {symbol} {market_type} 格式化={formatted_amount}")
    
    def log_precision_cache_miss(self, exchange: str, symbol: str, market_type: str):
        """记录精度缓存未命中"""
        self.cache_stats["precision_cache"]["misses"] += 1
        
        self.cache_logger.info(f"🎯 [精度缓存] 未命中: {exchange} {symbol} {market_type} - 需要规则处理")
    
    def log_cache_cleanup(self, cache_name: str, cleaned_count: int, total_count: int):
        """记录缓存清理"""
        self.cache_logger.info(f"🧹 [缓存清理] {cache_name}: 清理{cleaned_count}个过期项，剩余{total_count - cleaned_count}个")
    
    def log_cache_status_summary(self):
        """记录缓存状态总结"""
        self.cache_logger.info("=" * 80)
        self.cache_logger.info("📊 6大缓存系统状态总结")
        self.cache_logger.info("=" * 80)
        
        for cache_name, stats in self.cache_stats.items():
            hits = stats["hits"]
            misses = stats["misses"]
            updates = stats["updates"]
            total_requests = hits + misses
            hit_rate = (hits / total_requests * 100) if total_requests > 0 else 0
            
            last_update = stats["last_update"]
            last_update_str = datetime.fromtimestamp(last_update).strftime("%H:%M:%S") if last_update else "无"
            
            cache_display_name = {
                "balance_cache": "💰 余额缓存",
                "margin_cache": "📊 保证金缓存",
                "trading_rules_cache": "📋 交易规则缓存",
                "hedge_quality_cache": "⚖️ 对冲质量缓存",
                "precision_cache": "🎯 精度缓存"
            }.get(cache_name, cache_name)
            
            self.cache_logger.info(f"{cache_display_name}: 命中{hits} 未命中{misses} 更新{updates} 命中率{hit_rate:.1f}% 最后更新{last_update_str}")
        
        self.cache_logger.info("=" * 80)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self.cache_stats.copy()

# 全局实例
_cache_monitor = None

def get_cache_monitor() -> CacheMonitor:
    """获取缓存监控器实例"""
    global _cache_monitor
    if _cache_monitor is None:
        _cache_monitor = CacheMonitor()
    return _cache_monitor

# 便捷函数
def log_balance_hit(exchange: str, cache_key: str, value: float):
    """记录余额缓存命中"""
    get_cache_monitor().log_balance_cache_hit(exchange, cache_key, value)

def log_balance_miss(exchange: str, cache_key: str):
    """记录余额缓存未命中"""
    get_cache_monitor().log_balance_cache_miss(exchange, cache_key)

def log_balance_update(exchange: str, cache_key: str, old_value: float, new_value: float):
    """记录余额缓存更新"""
    get_cache_monitor().log_balance_cache_update(exchange, cache_key, old_value, new_value)

def log_balance_api(exchange: str, account_type: str, balance_data: Dict[str, Any], duration: float):
    """记录余额API调用"""
    # 提取USDT余额信息
    usdt_balance = balance_data.get('USDT', {})
    if isinstance(usdt_balance, dict):
        available = usdt_balance.get('available', 0)
        get_cache_monitor().cache_logger.info(f"🔍 [API调用] 余额接口: {exchange}_{account_type} | 真实API请求完成，耗时{duration:.1f}ms，USDT可用{available:.2f}")
    else:
        get_cache_monitor().cache_logger.info(f"🔍 [API调用] 余额接口: {exchange}_{account_type} | 真实API请求完成，耗时{duration:.1f}ms，USDT{usdt_balance:.2f}")

def log_margin_hit(exchange: str, symbol: str, contract_info: Dict[str, Any]):
    """记录保证金缓存命中"""
    get_cache_monitor().log_margin_cache_hit(exchange, symbol, contract_info)

def log_margin_miss(exchange: str, symbol: str):
    """记录保证金缓存未命中"""
    get_cache_monitor().log_margin_cache_miss(exchange, symbol)

def log_trading_rules_hit(exchange: str, symbol: str, market_type: str, rule: Any):
    """记录交易规则缓存命中"""
    get_cache_monitor().log_trading_rules_cache_hit(exchange, symbol, market_type, rule)

def log_trading_rules_miss(exchange: str, symbol: str, market_type: str):
    """记录交易规则缓存未命中"""
    get_cache_monitor().log_trading_rules_cache_miss(exchange, symbol, market_type)

def log_orderbook_hit(exchange: str, symbol: str, market_type: str, orderbook: Dict[str, Any]):
    """记录订单簿缓存命中"""
    get_cache_monitor().log_orderbook_cache_hit(exchange, symbol, market_type, orderbook)

def log_orderbook_miss(exchange: str, symbol: str, market_type: str):
    """记录订单簿缓存未命中"""
    get_cache_monitor().log_orderbook_cache_miss(exchange, symbol, market_type)

def log_orderbook_api(exchange: str, symbol: str, market_type: str, duration_ms: float):
    """记录订单簿API调用"""
    get_cache_monitor().log_orderbook_api_call(exchange, symbol, market_type, duration_ms)

def log_hedge_hit(spot_exchange: str, futures_exchange: str, symbol: str, hedge_quality: Dict[str, Any]):
    """记录对冲质量缓存命中"""
    get_cache_monitor().log_hedge_quality_cache_hit(spot_exchange, futures_exchange, symbol, hedge_quality)

def log_hedge_miss(spot_exchange: str, futures_exchange: str, symbol: str):
    """记录对冲质量缓存未命中"""
    get_cache_monitor().log_hedge_quality_cache_miss(spot_exchange, futures_exchange, symbol)

def log_hedge_calc(spot_exchange: str, futures_exchange: str, symbol: str, 
                  spot_amount: float, futures_amount: float, hedge_ratio: float):
    """记录对冲质量计算"""
    get_cache_monitor().log_hedge_quality_calculation(spot_exchange, futures_exchange, symbol, 
                                                     spot_amount, futures_amount, hedge_ratio)

def log_precision_hit(exchange: str, symbol: str, market_type: str, formatted_amount: str):
    """记录精度缓存命中"""
    get_cache_monitor().log_precision_cache_hit(exchange, symbol, market_type, formatted_amount)

def log_precision_miss(exchange: str, symbol: str, market_type: str):
    """记录精度缓存未命中"""
    get_cache_monitor().log_precision_cache_miss(exchange, symbol, market_type)

def log_cache_summary():
    """记录缓存状态总结"""
    get_cache_monitor().log_cache_status_summary()

def log_cleanup(cache_name: str, cleaned_count: int, total_count: int):
    """记录缓存清理"""
    get_cache_monitor().log_cache_cleanup(cache_name, cleaned_count, total_count)
