#!/usr/bin/env python3
"""
动态数据流测试 - 真实WebSocket连接测试
验证数据堆积、数据阻塞、数据流断裂问题是否真正解决

禁止任何模拟数据！使用真实WebSocket连接进行测试
"""

import os
import sys
import time
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入真实的WebSocket客户端
from websocket.gate_ws import GateWebSocketClient
from websocket.okx_ws import OKXWebSocketClient
from websocket.bybit_ws import BybitWebSocketClient
from websocket.enhanced_blocking_tracker import get_blocking_tracker

class RealTimeDataFlowMonitor:
    """实时数据流监控器 - 检测数据堆积、阻塞、断裂问题"""
    
    def __init__(self):
        self.test_start_time = time.time()
        self.test_results = {
            "test_start": datetime.now().isoformat(),
            "exchanges": {},
            "data_flow_analysis": {},
            "blocking_detection": {},
            "final_verdict": {}
        }
        
        # 数据流监控
        self.message_timestamps = defaultdict(deque)  # 每个交易所的消息时间戳队列
        self.message_counts = defaultdict(int)
        self.last_message_time = defaultdict(float)
        self.data_gaps = defaultdict(list)  # 数据间隔记录
        
        # 阻塞检测器
        self.blocking_tracker = get_blocking_tracker()
        
        # 设置日志
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置测试专用日志"""
        logger = logging.getLogger("dynamic_data_flow_test")
        logger.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s [TEST] %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        
        if not logger.handlers:
            logger.addHandler(console_handler)
        
        return logger
    
    async def test_gate_websocket_flow(self, duration_seconds: int = 60):
        """测试Gate.io WebSocket数据流"""
        self.logger.info("🔍 开始测试Gate.io WebSocket数据流...")

        gate_client = None
        try:
            gate_client = GateWebSocketClient("spot")

            # 记录开始时间
            start_time = time.time()
            self.test_results["exchanges"]["gate"] = {
                "start_time": start_time,
                "messages_received": 0,
                "data_gaps": [],
                "max_gap_ms": 0,
                "avg_gap_ms": 0,
                "connection_stable": True,
                "blocking_events": []
            }

            # 设置测试符号
            test_symbols = ["BTC_USDT", "ETH_USDT"]
            gate_client.set_symbols(test_symbols)

            # 启动WebSocket连接
            connection_success = await gate_client.start_connection()

            if not connection_success:
                self.logger.error("Gate.io WebSocket连接失败")
                self.test_results["exchanges"]["gate"]["connection_stable"] = False
                return

            # 监控数据流
            last_message_time = time.time()
            message_count = 0
            gaps = []

            while time.time() - start_time < duration_seconds:
                try:
                    # 等待消息 - 使用正确的消息队列属性
                    if hasattr(gate_client, '_message_queue') and gate_client._message_queue:
                        message = await asyncio.wait_for(
                            gate_client._message_queue.get(),
                            timeout=5.0
                        )
                    else:
                        # 如果没有消息队列，直接等待
                        await asyncio.sleep(1.0)
                        continue
                    
                    current_time = time.time()
                    message_count += 1
                    
                    # 计算数据间隔
                    if message_count > 1:
                        gap_ms = (current_time - last_message_time) * 1000
                        gaps.append(gap_ms)
                        
                        # 检测异常间隔（>5秒表示可能的数据堆积）
                        if gap_ms > 5000:
                            self.logger.warning(f"Gate.io 数据间隔异常: {gap_ms:.1f}ms")
                            self.test_results["exchanges"]["gate"]["data_gaps"].append({
                                "timestamp": current_time,
                                "gap_ms": gap_ms,
                                "message_count": message_count
                            })
                    
                    last_message_time = current_time
                    
                    # 记录消息时间戳用于新鲜度分析
                    self.message_timestamps["gate"].append(current_time)
                    if len(self.message_timestamps["gate"]) > 100:
                        self.message_timestamps["gate"].popleft()
                    
                    # 每10秒报告一次状态
                    if message_count % 50 == 0:
                        self.logger.info(f"Gate.io: 已接收 {message_count} 条消息")
                
                except asyncio.TimeoutError:
                    self.logger.warning("Gate.io: 5秒内未收到消息，可能存在数据流问题")
                    current_time = time.time()
                    gap_ms = (current_time - last_message_time) * 1000
                    if gap_ms > 5000:
                        self.test_results["exchanges"]["gate"]["data_gaps"].append({
                            "timestamp": current_time,
                            "gap_ms": gap_ms,
                            "timeout": True
                        })
                    continue
                
                except Exception as e:
                    self.logger.error(f"Gate.io 消息处理异常: {e}")
                    self.test_results["exchanges"]["gate"]["connection_stable"] = False
                    break
            
            # 统计结果
            self.test_results["exchanges"]["gate"]["messages_received"] = message_count
            self.test_results["exchanges"]["gate"]["max_gap_ms"] = max(gaps) if gaps else 0
            self.test_results["exchanges"]["gate"]["avg_gap_ms"] = sum(gaps) / len(gaps) if gaps else 0
            self.test_results["exchanges"]["gate"]["total_gaps"] = len(self.test_results["exchanges"]["gate"]["data_gaps"])
            
            self.logger.info(f"Gate.io 测试完成: {message_count}条消息, 最大间隔{max(gaps) if gaps else 0:.1f}ms")
            
        except Exception as e:
            self.logger.error(f"Gate.io WebSocket测试失败: {e}")
            self.test_results["exchanges"]["gate"]["error"] = str(e)
            self.test_results["exchanges"]["gate"]["connection_stable"] = False
        
        finally:
            if gate_client:
                try:
                    await gate_client.close()
                except:
                    pass
    
    async def test_okx_websocket_flow(self, duration_seconds: int = 60):
        """测试OKX WebSocket数据流"""
        self.logger.info("🔍 开始测试OKX WebSocket数据流...")

        okx_client = None
        try:
            okx_client = OKXWebSocketClient("spot")

            # 记录开始时间
            start_time = time.time()
            self.test_results["exchanges"]["okx"] = {
                "start_time": start_time,
                "messages_received": 0,
                "data_gaps": [],
                "max_gap_ms": 0,
                "avg_gap_ms": 0,
                "connection_stable": True,
                "blocking_events": []
            }

            # 设置测试符号
            test_symbols = ["BTC-USDT", "ETH-USDT"]
            okx_client.set_symbols(test_symbols)

            # 启动WebSocket连接
            connection_success = await okx_client.start_connection()

            if not connection_success:
                self.logger.error("OKX WebSocket连接失败")
                self.test_results["exchanges"]["okx"]["connection_stable"] = False
                return

            # 监控数据流
            last_message_time = time.time()
            message_count = 0
            gaps = []

            while time.time() - start_time < duration_seconds:
                try:
                    # 等待消息 - 使用正确的消息队列属性
                    if hasattr(okx_client, '_message_queue') and okx_client._message_queue:
                        message = await asyncio.wait_for(
                            okx_client._message_queue.get(),
                            timeout=5.0
                        )
                    else:
                        # 如果没有消息队列，直接等待
                        await asyncio.sleep(1.0)
                        continue
                    
                    current_time = time.time()
                    message_count += 1
                    
                    # 计算数据间隔
                    if message_count > 1:
                        gap_ms = (current_time - last_message_time) * 1000
                        gaps.append(gap_ms)
                        
                        # 检测异常间隔
                        if gap_ms > 5000:
                            self.logger.warning(f"OKX 数据间隔异常: {gap_ms:.1f}ms")
                            self.test_results["exchanges"]["okx"]["data_gaps"].append({
                                "timestamp": current_time,
                                "gap_ms": gap_ms,
                                "message_count": message_count
                            })
                    
                    last_message_time = current_time
                    
                    # 记录消息时间戳
                    self.message_timestamps["okx"].append(current_time)
                    if len(self.message_timestamps["okx"]) > 100:
                        self.message_timestamps["okx"].popleft()
                    
                    # 每10秒报告一次状态
                    if message_count % 50 == 0:
                        self.logger.info(f"OKX: 已接收 {message_count} 条消息")
                
                except asyncio.TimeoutError:
                    self.logger.warning("OKX: 5秒内未收到消息，可能存在数据流问题")
                    current_time = time.time()
                    gap_ms = (current_time - last_message_time) * 1000
                    if gap_ms > 5000:
                        self.test_results["exchanges"]["okx"]["data_gaps"].append({
                            "timestamp": current_time,
                            "gap_ms": gap_ms,
                            "timeout": True
                        })
                    continue
                
                except Exception as e:
                    self.logger.error(f"OKX 消息处理异常: {e}")
                    self.test_results["exchanges"]["okx"]["connection_stable"] = False
                    break
            
            # 统计结果
            self.test_results["exchanges"]["okx"]["messages_received"] = message_count
            self.test_results["exchanges"]["okx"]["max_gap_ms"] = max(gaps) if gaps else 0
            self.test_results["exchanges"]["okx"]["avg_gap_ms"] = sum(gaps) / len(gaps) if gaps else 0
            self.test_results["exchanges"]["okx"]["total_gaps"] = len(self.test_results["exchanges"]["okx"]["data_gaps"])
            
            self.logger.info(f"OKX 测试完成: {message_count}条消息, 最大间隔{max(gaps) if gaps else 0:.1f}ms")
            
        except Exception as e:
            self.logger.error(f"OKX WebSocket测试失败: {e}")
            self.test_results["exchanges"]["okx"]["error"] = str(e)
            self.test_results["exchanges"]["okx"]["connection_stable"] = False
        
        finally:
            if okx_client:
                try:
                    await okx_client.close()
                except:
                    pass
    
    def analyze_data_flow_health(self):
        """分析数据流健康状况"""
        self.logger.info("📊 分析数据流健康状况...")
        
        analysis = {
            "overall_health": "UNKNOWN",
            "exchanges_analysis": {},
            "critical_issues": [],
            "warnings": [],
            "recommendations": []
        }
        
        healthy_exchanges = 0
        total_exchanges = 0
        
        for exchange, data in self.test_results["exchanges"].items():
            if not data:
                continue
                
            total_exchanges += 1
            exchange_analysis = {
                "status": "HEALTHY",
                "issues": [],
                "metrics": {}
            }
            
            # 检查连接稳定性
            if not data.get("connection_stable", True):
                exchange_analysis["status"] = "UNSTABLE"
                exchange_analysis["issues"].append("连接不稳定")
            
            # 检查消息接收量
            messages = data.get("messages_received", 0)
            if messages < 10:  # 60秒内少于10条消息异常
                exchange_analysis["status"] = "CRITICAL"
                exchange_analysis["issues"].append(f"消息量过低: {messages}条/60秒")
                analysis["critical_issues"].append(f"{exchange}: 消息量过低")
            
            # 检查数据间隔
            max_gap = data.get("max_gap_ms", 0)
            if max_gap > 10000:  # 超过10秒的间隔
                exchange_analysis["status"] = "CRITICAL"
                exchange_analysis["issues"].append(f"数据间隔过大: {max_gap:.1f}ms")
                analysis["critical_issues"].append(f"{exchange}: 数据堆积问题")
            elif max_gap > 5000:  # 超过5秒的间隔
                exchange_analysis["issues"].append(f"数据间隔较大: {max_gap:.1f}ms")
                analysis["warnings"].append(f"{exchange}: 可能存在轻微数据延迟")
            
            # 检查数据间隔数量
            gap_count = data.get("total_gaps", 0)
            if gap_count > 5:  # 超过5次异常间隔
                exchange_analysis["status"] = "CRITICAL"
                exchange_analysis["issues"].append(f"异常间隔次数过多: {gap_count}次")
                analysis["critical_issues"].append(f"{exchange}: 数据流不稳定")
            
            # 记录指标
            exchange_analysis["metrics"] = {
                "messages_per_minute": messages,
                "max_gap_ms": max_gap,
                "avg_gap_ms": data.get("avg_gap_ms", 0),
                "abnormal_gaps": gap_count
            }
            
            if exchange_analysis["status"] == "HEALTHY":
                healthy_exchanges += 1
            
            analysis["exchanges_analysis"][exchange] = exchange_analysis
        
        # 总体健康状况
        if healthy_exchanges == total_exchanges and total_exchanges > 0:
            analysis["overall_health"] = "HEALTHY"
        elif healthy_exchanges > 0:
            analysis["overall_health"] = "PARTIAL"
        else:
            analysis["overall_health"] = "CRITICAL"
        
        # 生成建议
        if analysis["critical_issues"]:
            analysis["recommendations"].append("立即检查WebSocket连接和消息处理逻辑")
            analysis["recommendations"].append("检查是否存在阻塞检测冲突")
        
        if analysis["warnings"]:
            analysis["recommendations"].append("监控数据流稳定性，考虑优化网络连接")
        
        self.test_results["data_flow_analysis"] = analysis
        return analysis
    
    async def run_dynamic_test(self, test_duration: int = 60):
        """运行动态测试"""
        self.logger.info(f"🚀 开始动态数据流测试 (持续{test_duration}秒)...")
        self.logger.info("=" * 60)
        
        # 并发测试多个交易所
        tasks = [
            self.test_gate_websocket_flow(test_duration),
            self.test_okx_websocket_flow(test_duration)
        ]
        
        try:
            await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            self.logger.error(f"动态测试执行异常: {e}")
        
        # 分析结果
        analysis = self.analyze_data_flow_health()
        
        # 生成最终判断
        self._generate_final_verdict(analysis)
        
        # 保存结果
        self._save_test_results()
        
        # 输出摘要
        self._print_test_summary()
        
        return self.test_results
    
    def _generate_final_verdict(self, analysis):
        """生成最终判断"""
        verdict = {
            "data_stacking_resolved": True,
            "data_blocking_resolved": True,
            "data_flow_break_resolved": True,
            "overall_success": True,
            "confidence_level": "HIGH",
            "evidence": []
        }
        
        # 检查数据堆积问题
        for exchange, data in analysis["exchanges_analysis"].items():
            max_gap = data["metrics"].get("max_gap_ms", 0)
            if max_gap > 10000:  # 超过10秒表示数据堆积
                verdict["data_stacking_resolved"] = False
                verdict["evidence"].append(f"{exchange}: 数据堆积 {max_gap:.1f}ms")
        
        # 检查数据阻塞问题
        critical_issues = analysis.get("critical_issues", [])
        if any("消息量过低" in issue for issue in critical_issues):
            verdict["data_blocking_resolved"] = False
            verdict["evidence"].append("检测到消息量异常低，可能存在数据阻塞")
        
        # 检查数据流断裂问题
        for exchange, data in self.test_results["exchanges"].items():
            if not data.get("connection_stable", True):
                verdict["data_flow_break_resolved"] = False
                verdict["evidence"].append(f"{exchange}: 连接不稳定，数据流断裂")
        
        # 总体判断
        verdict["overall_success"] = (
            verdict["data_stacking_resolved"] and 
            verdict["data_blocking_resolved"] and 
            verdict["data_flow_break_resolved"]
        )
        
        # 置信度
        if not verdict["overall_success"]:
            verdict["confidence_level"] = "HIGH"  # 问题明确
        elif analysis["overall_health"] == "HEALTHY":
            verdict["confidence_level"] = "HIGH"
        else:
            verdict["confidence_level"] = "MEDIUM"
        
        self.test_results["final_verdict"] = verdict
    
    def _print_test_summary(self):
        """打印测试摘要"""
        print("\n🎯 动态数据流测试摘要")
        print("=" * 60)
        
        # 交易所状态
        for exchange, data in self.test_results["exchanges"].items():
            if not data:
                continue
            status = "✅" if data.get("connection_stable", True) else "❌"
            messages = data.get("messages_received", 0)
            max_gap = data.get("max_gap_ms", 0)
            print(f"{status} {exchange.upper()}: {messages}条消息, 最大间隔{max_gap:.1f}ms")
        
        # 最终判断
        verdict = self.test_results["final_verdict"]
        print(f"\n🔍 最终判断:")
        print(f"   数据堆积问题: {'✅ 已解决' if verdict['data_stacking_resolved'] else '❌ 未解决'}")
        print(f"   数据阻塞问题: {'✅ 已解决' if verdict['data_blocking_resolved'] else '❌ 未解决'}")
        print(f"   数据流断裂问题: {'✅ 已解决' if verdict['data_flow_break_resolved'] else '❌ 未解决'}")
        print(f"   总体成功: {'✅ 是' if verdict['overall_success'] else '❌ 否'}")
        print(f"   置信度: {verdict['confidence_level']}")
        
        if verdict["evidence"]:
            print(f"\n⚠️ 发现的问题:")
            for evidence in verdict["evidence"]:
                print(f"   - {evidence}")
    
    def _save_test_results(self):
        """保存测试结果"""
        timestamp = int(time.time())
        filename = f"dynamic_data_flow_test_{timestamp}.json"
        filepath = os.path.join(os.path.dirname(__file__), filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            self.logger.info(f"📄 测试结果已保存: {filename}")
        except Exception as e:
            self.logger.error(f"❌ 保存测试结果失败: {e}")

async def main():
    """主函数"""
    monitor = RealTimeDataFlowMonitor()
    
    print("🚀 启动动态数据流测试...")
    print("📋 测试目标:")
    print("   1. 验证数据堆积问题是否解决")
    print("   2. 验证数据阻塞问题是否解决") 
    print("   3. 验证数据流断裂问题是否解决")
    print("   4. 使用真实WebSocket连接，禁止模拟数据")
    print("=" * 60)
    
    # 运行60秒动态测试
    results = await monitor.run_dynamic_test(60)
    
    # 输出最终结论
    verdict = results["final_verdict"]
    print("\n🎯 最终结论")
    print("=" * 60)
    
    if verdict["overall_success"]:
        print("✅ 动态测试成功！数据堆积、数据阻塞、数据流断裂问题已解决")
    else:
        print("❌ 动态测试发现问题！需要进一步修复")
        for evidence in verdict["evidence"]:
            print(f"   ❌ {evidence}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
