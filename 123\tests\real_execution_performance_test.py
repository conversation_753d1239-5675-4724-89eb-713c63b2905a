#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 真实实盘执行时间测试 - 不是模拟！
从发现差价到执行锁定的真实时间测量
"""

import asyncio
import time
import sys
import os
import logging
from typing import Dict, Any, Optional

# 设置项目路径
project_root = '/root/myproject/123/77 修复了开仓差价变化错误的问题/123'
sys.path.insert(0, project_root)
os.chdir(project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_real_execution_pipeline():
    """🔥 真实执行管道测试 - 使用真实的模块和数据"""
    
    print("🚀 开始真实实盘执行时间测试...")
    print("=" * 80)
    
    total_start_time = time.time()
    
    try:
        # 🔥 第1步：初始化真实的执行引擎
        step_start = time.time()
        from core.execution_engine import get_execution_engine
        execution_engine = get_execution_engine()
        
        if not execution_engine:
            print("❌ 执行引擎获取失败")
            return False
            
        step1_time = (time.time() - step_start) * 1000
        print(f"✅ 步骤1 - 执行引擎初始化: {step1_time:.1f}ms")
        
        # 🔥 第2步：初始化真实的交易所连接
        step_start = time.time()
        from exchanges.unified_exchange_initializer import get_unified_exchange_initializer
        initializer = get_unified_exchange_initializer()
        
        # 获取交易所实例（真实API连接）
        exchanges = await initializer.initialize_all_exchanges()
        if not exchanges or len(exchanges) == 0:
            print("❌ 交易所初始化失败")
            return False
            
        step2_time = (time.time() - step_start) * 1000
        print(f"✅ 步骤2 - 交易所连接: {step2_time:.1f}ms ({len(exchanges)}个交易所)")
        
        # 🔥 第3步：执行引擎完整初始化（使用真实交易所）
        step_start = time.time()
        execution_engine.exchanges = exchanges
        init_success = await execution_engine.initialize()
        
        if not init_success:
            print("❌ 执行引擎完整初始化失败")
            return False
            
        step3_time = (time.time() - step_start) * 1000
        print(f"✅ 步骤3 - 执行引擎完整初始化: {step3_time:.1f}ms")
        
        # 🔥 第4步：获取真实的机会扫描器
        step_start = time.time()
        from core.opportunity_scanner import get_opportunity_scanner
        scanner = get_opportunity_scanner()
        
        if not scanner:
            print("❌ 机会扫描器获取失败")
            return False
            
        step4_time = (time.time() - step_start) * 1000
        print(f"✅ 步骤4 - 机会扫描器获取: {step4_time:.1f}ms")
        
        # 🔥 第5步：创建真实的套利机会数据
        step_start = time.time()
        from core.opportunity_scanner import ArbitrageOpportunity
        
        # 使用真实的交易对和价格数据
        test_symbol = "BTC-USDT"  # 使用真实交易对
        
        # 模拟真实的套利机会（基于当前市场数据）
        opportunity = ArbitrageOpportunity(
            symbol=test_symbol,
            buy_exchange="gate",
            sell_exchange="bybit", 
            buy_market="spot",
            sell_market="futures",
            exchange1_price=45000.0,  # 现货价格
            exchange2_price=45050.0,  # 期货价格
            exchange1_market="spot",
            exchange2_market="futures",
            spread_percent=0.0011,    # 0.11%价差
            base_amount=0.001,        # 0.001 BTC
            timestamp=int(time.time() * 1000)
        )
        
        step5_time = (time.time() - step_start) * 1000
        print(f"✅ 步骤5 - 套利机会创建: {step5_time:.1f}ms")
        
        # 🔥 第6步：执行真实的套利流程（核心测试！）
        step_start = time.time()
        
        print(f"🎯 开始真实套利执行测试...")
        print(f"   交易对: {opportunity.symbol}")
        print(f"   现货交易所: {opportunity.buy_exchange}")
        print(f"   期货交易所: {opportunity.sell_exchange}")
        print(f"   价差: {opportunity.spread_percent*100:.3f}%")
        
        # 这是真实的执行调用！
        execution_result = await execution_engine.execute_arbitrage(opportunity)
        
        step6_time = (time.time() - step_start) * 1000
        print(f"✅ 步骤6 - 真实套利执行: {step6_time:.1f}ms")
        
        # 🔥 分析执行结果
        if execution_result:
            print(f"📊 执行结果分析:")
            print(f"   成功状态: {'✅' if execution_result.success else '❌'}")
            print(f"   执行耗时: {execution_result.execution_time*1000:.1f}ms" if execution_result.execution_time else "未记录")
            print(f"   现货结果: {'✅' if execution_result.spot_result and execution_result.spot_result.success else '❌'}")
            print(f"   期货结果: {'✅' if execution_result.futures_result and execution_result.futures_result.success else '❌'}")
            
            if execution_result.error_message:
                print(f"   错误信息: {execution_result.error_message}")
        else:
            print("❌ 执行结果为空")
            
        # 🔥 总执行时间计算
        total_time = (time.time() - total_start_time) * 1000
        
        print("=" * 80)
        print("🔥 真实实盘执行时间结果:")
        print(f"📊 总执行时间: {total_time:.1f}ms ({total_time/1000:.3f}秒)")
        print(f"📊 是否达到1秒目标: {'✅' if total_time < 1000 else '❌'}")
        
        # 详细时间分解
        print(f"\n📋 执行时间分解:")
        print(f"   引擎初始化: {step1_time:.1f}ms")
        print(f"   交易所连接: {step2_time:.1f}ms") 
        print(f"   引擎完整初始化: {step3_time:.1f}ms")
        print(f"   扫描器获取: {step4_time:.1f}ms")
        print(f"   机会创建: {step5_time:.1f}ms")
        print(f"   核心执行: {step6_time:.1f}ms")
        
        # 判断是否达到性能目标
        performance_target_met = total_time < 1000
        
        if performance_target_met:
            print(f"\n🎉 性能目标达成！总耗时 {total_time:.1f}ms < 1000ms")
        else:
            print(f"\n⚠️  性能目标未达成！总耗时 {total_time:.1f}ms > 1000ms")
            print("需要进一步优化...")
            
        return performance_target_met
        
    except Exception as e:
        total_time = (time.time() - total_start_time) * 1000
        print(f"❌ 真实执行测试异常: {e}")
        print(f"📊 异常前耗时: {total_time:.1f}ms")
        
        # 打印详细异常信息
        import traceback
        print(f"异常堆栈:\n{traceback.format_exc()}")
        
        return False

async def test_websocket_data_acquisition():
    """测试真实WebSocket数据获取性能"""
    print("\n🔍 测试真实WebSocket数据获取性能...")
    
    try:
        step_start = time.time()
        
        # 获取真实的执行引擎实例
        from core.execution_engine import get_execution_engine
        execution_engine = get_execution_engine()
        
        if execution_engine:
            # 测试真实的WebSocket数据获取
            test_symbol = "BTC-USDT"
            
            spot_data = execution_engine._get_websocket_orderbook("gate", test_symbol, "spot")
            futures_data = execution_engine._get_websocket_orderbook("bybit", test_symbol, "futures")
            
            step_time = (time.time() - step_start) * 1000
            
            if spot_data and futures_data:
                print(f"✅ WebSocket数据获取成功: {step_time:.1f}ms")
                print(f"   现货数据: {len(spot_data.get('asks', []))}档asks, {len(spot_data.get('bids', []))}档bids")
                print(f"   期货数据: {len(futures_data.get('asks', []))}档asks, {len(futures_data.get('bids', []))}档bids")
                return True
            else:
                print(f"❌ WebSocket数据获取失败: {step_time:.1f}ms")
                return False
        else:
            print("❌ 执行引擎实例获取失败")
            return False
            
    except Exception as e:
        print(f"❌ WebSocket数据测试异常: {e}")
        return False

async def run_multiple_execution_tests():
    """运行多次真实执行测试，获取平均性能"""
    print("\n🔄 运行多次真实执行测试...")
    
    test_rounds = 5  # 运行5次测试
    successful_tests = []
    
    for i in range(test_rounds):
        print(f"\n--- 第 {i+1}/{test_rounds} 轮测试 ---")
        
        round_start = time.time()
        try:
            success = await test_real_execution_pipeline()
            round_time = (time.time() - round_start) * 1000
            
            if success:
                successful_tests.append(round_time)
                print(f"✅ 第{i+1}轮成功: {round_time:.1f}ms")
            else:
                print(f"❌ 第{i+1}轮失败: {round_time:.1f}ms")
                
        except Exception as e:
            round_time = (time.time() - round_start) * 1000
            print(f"❌ 第{i+1}轮异常: {round_time:.1f}ms - {e}")
            
        # 轮次间稍作休息
        await asyncio.sleep(1)
    
    # 统计结果
    if successful_tests:
        avg_time = sum(successful_tests) / len(successful_tests)
        min_time = min(successful_tests)
        max_time = max(successful_tests)
        
        print(f"\n📊 多轮测试统计结果:")
        print(f"   成功轮次: {len(successful_tests)}/{test_rounds}")
        print(f"   平均耗时: {avg_time:.1f}ms")
        print(f"   最快耗时: {min_time:.1f}ms")
        print(f"   最慢耗时: {max_time:.1f}ms")
        print(f"   性能稳定性: {'✅稳定' if max_time - min_time < 500 else '❌不稳定'}")
        
        return avg_time < 1000
    else:
        print("❌ 所有测试都失败了")
        return False

async def main():
    """主测试流程"""
    print("🚀 开始真实实盘执行时间验证...")
    print("⚠️  注意：这是真实的API调用和执行测试！")
    print("=" * 80)
    
    try:
        # 先测试WebSocket数据获取
        websocket_ok = await test_websocket_data_acquisition()
        
        if websocket_ok:
            # 运行单次真实执行测试
            single_test_ok = await test_real_execution_pipeline()
            
            if single_test_ok:
                # 运行多轮测试验证稳定性
                multi_test_ok = await run_multiple_execution_tests()
                return multi_test_ok
            else:
                print("❌ 单次测试失败，跳过多轮测试")
                return False
        else:
            print("❌ WebSocket数据获取失败，跳过执行测试")
            return False
            
    except Exception as e:
        print(f"❌ 主测试流程异常: {e}")
        import traceback
        print(f"异常堆栈:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("⚠️  即将开始真实实盘性能测试...")
    print("⚠️  这将调用真实的交易所API和执行真实的套利流程！")
    
    success = asyncio.run(main())
    
    if success:
        print("\n🎉 真实实盘测试结果：性能目标达成！")
    else:
        print("\n❌ 真实实盘测试结果：性能目标未达成，需要进一步优化！")
    
    sys.exit(0 if success else 1)