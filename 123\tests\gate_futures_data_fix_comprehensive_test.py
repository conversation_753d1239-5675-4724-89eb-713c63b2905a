#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Gate期货数据格式修复 - 机构级别综合测试
三段进阶验证机制：基础核心 → 系统级联 → 动态生产
确保100%完美修复，零错误容忍
"""

import sys
import os
import asyncio
import json
import time
import logging
from typing import Dict, List, Any, Optional
from decimal import Decimal
from datetime import datetime
import unittest
from unittest.mock import patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'websocket'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'core'))

# 配置测试日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)


class GateFuturesDataFixComprehensiveTest:
    """Gate期货数据格式修复 - 机构级别综合测试"""
    
    def __init__(self):
        self.test_results = {
            "basic_core_tests": {"total": 0, "passed": 0, "failed": 0, "errors": []},
            "system_cascade_tests": {"total": 0, "passed": 0, "failed": 0, "errors": []},
            "dynamic_production_tests": {"total": 0, "passed": 0, "failed": 0, "errors": []},
            "overall_success": False,
            "test_start_time": None,
            "test_end_time": None
        }
        self.logger = logger
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """执行所有三段测试"""
        self.test_results["test_start_time"] = datetime.now().isoformat()
        self.logger.info("🚀 开始Gate期货数据格式修复 - 机构级别综合测试")
        
        try:
            # ① 基础核心测试
            await self.run_basic_core_tests()
            
            # ② 复杂系统级联测试  
            await self.run_system_cascade_tests()
            
            # ③ 动态生产测试 (1-3分钟)
            await self.run_dynamic_production_tests()
            
            # 计算总体结果
            self._calculate_overall_results()
            
        except Exception as e:
            self.logger.error(f"❌ 测试执行异常: {e}")
            self.test_results["overall_success"] = False
        finally:
            self.test_results["test_end_time"] = datetime.now().isoformat()
            
        return self.test_results
    
    async def run_basic_core_tests(self):
        """① 基础核心测试：模块单元功能验证"""
        self.logger.info("🧪 开始基础核心测试...")
        
        tests = [
            self.test_gate_websocket_import,
            self.test_unified_formatter_gate_specs,
            self.test_gate_data_format_handling,
            self.test_gate_futures_spot_difference,
            self.test_orderbook_validation_logic,
            self.test_error_handling_mechanisms
        ]
        
        for test_func in tests:
            await self._run_single_test(test_func, "basic_core_tests")
    
    async def run_system_cascade_tests(self):
        """② 复杂系统级联测试：模块间交互验证"""
        self.logger.info("🔗 开始系统级联测试...")
        
        tests = [
            self.test_websocket_to_formatter_integration,
            self.test_three_exchange_consistency,
            self.test_market_data_flow_cascade,
            self.test_timestamp_sync_integration,
            self.test_multi_symbol_processing,
            self.test_concurrent_orderbook_updates
        ]
        
        for test_func in tests:
            await self._run_single_test(test_func, "system_cascade_tests")
    
    async def run_dynamic_production_tests(self):
        """③ 动态生产测试：真实环境模拟 (1-3分钟)"""
        self.logger.info("🚀 开始动态生产测试 (预计2分钟)...")
        
        tests = [
            self.test_real_gate_websocket_connection,
            self.test_continuous_data_processing,
            self.test_arbitrage_combinations_availability,
            self.test_performance_under_load,
            self.test_error_recovery_scenarios
        ]
        
        for test_func in tests:
            await self._run_single_test(test_func, "dynamic_production_tests")
    
    async def _run_single_test(self, test_func, category: str):
        """运行单个测试"""
        test_name = test_func.__name__
        self.test_results[category]["total"] += 1
        
        try:
            self.logger.info(f"  🔸 {test_name}")
            result = await test_func()
            if result:
                self.test_results[category]["passed"] += 1
                self.logger.info(f"    ✅ {test_name} - 通过")
            else:
                self.test_results[category]["failed"] += 1
                self.test_results[category]["errors"].append(f"{test_name}: 测试失败")
                self.logger.error(f"    ❌ {test_name} - 失败")
        except Exception as e:
            self.test_results[category]["failed"] += 1
            self.test_results[category]["errors"].append(f"{test_name}: {str(e)}")
            self.logger.error(f"    💥 {test_name} - 异常: {e}")
    
    # =================== 基础核心测试 ===================
    
    async def test_gate_websocket_import(self) -> bool:
        """测试Gate WebSocket模块导入"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            from websocket.unified_data_formatter import get_orderbook_formatter
            
            # 验证关键方法存在
            client = GateWebSocketClient("futures")
            assert hasattr(client, '_handle_orderbook')
            assert hasattr(client, '_orderbook_formatter')
            
            return True
        except Exception as e:
            self.logger.error(f"Gate WebSocket导入失败: {e}")
            return False
    
    async def test_unified_formatter_gate_specs(self) -> bool:
        """测试统一格式化器Gate.io规范配置"""
        try:
            from websocket.unified_data_formatter import get_orderbook_formatter
            
            formatter = get_orderbook_formatter()
            gate_spec = formatter.api_format_specs.get("gate", {})
            
            # 验证Gate.io API规范配置
            assert gate_spec.get("price_field") == "p", "Gate.io价格字段应为'p'"
            assert gate_spec.get("volume_field") == "s", "Gate.io数量字段应为's'"
            assert gate_spec.get("data_wrapper") == "result", "Gate.io数据包装器应为'result'"
            
            return True
        except Exception as e:
            self.logger.error(f"统一格式化器配置错误: {e}")
            return False
    
    async def test_gate_data_format_handling(self) -> bool:
        """测试Gate期货数据格式处理"""
        try:
            from websocket.unified_data_formatter import get_orderbook_formatter
            
            formatter = get_orderbook_formatter()
            
            # 模拟Gate期货字典格式数据
            futures_asks = [
                {"p": "50000.5", "s": "1.234"},
                {"p": "50001.0", "s": "0.567"}
            ]
            futures_bids = [
                {"p": "49999.5", "s": "2.345"},
                {"p": "49998.0", "s": "1.890"}
            ]
            
            # 模拟Gate现货数组格式数据  
            spot_asks = [
                ["50000.5", "1.234"],
                ["50001.0", "0.567"]
            ]
            spot_bids = [
                ["49999.5", "2.345"],
                ["49998.0", "1.890"]
            ]
            
            # 测试期货数据处理
            futures_result = formatter.format_orderbook_data(
                asks=futures_asks,
                bids=futures_bids,
                symbol="BTC-USDT",
                exchange="gate",
                market_type="futures"
            )
            
            # 测试现货数据处理
            spot_result = formatter.format_orderbook_data(
                asks=spot_asks,
                bids=spot_bids,
                symbol="BTC-USDT", 
                exchange="gate",
                market_type="spot"
            )
            
            # 验证结果
            assert not futures_result.get("incomplete_orderbook", True), "期货数据不应不完整"
            assert not spot_result.get("incomplete_orderbook", True), "现货数据不应不完整"
            assert len(futures_result.get("asks", [])) > 0, "期货应有asks数据"
            assert len(futures_result.get("bids", [])) > 0, "期货应有bids数据"
            
            return True
        except Exception as e:
            self.logger.error(f"Gate数据格式处理测试失败: {e}")
            return False
    
    async def test_gate_futures_spot_difference(self) -> bool:
        """测试Gate期货与现货数据格式差异处理"""
        try:
            # 确保修复能正确区分处理期货字典格式和现货数组格式
            from websocket.unified_data_formatter import UnifiedOrderbookFormatter
            
            formatter = UnifiedOrderbookFormatter()
            
            # Gate期货数据格式 {'p': price, 's': size}
            futures_data = [{"p": "50000", "s": "1.0"}]
            # Gate现货数据格式 [price, size]  
            spot_data = [["50000", "1.0"]]
            
            # 两种格式都应该被正确处理
            futures_formatted = formatter._format_price_levels_api_standard(futures_data, "asks", "gate")
            spot_formatted = formatter._format_price_levels_api_standard(spot_data, "asks", "gate")
            
            assert len(futures_formatted) == 1, "期货数据应被处理"
            assert len(spot_formatted) == 1, "现货数据应被处理"
            assert futures_formatted[0][0] == 50000.0, "期货价格应正确"
            assert spot_formatted[0][0] == 50000.0, "现货价格应正确"
            
            return True
        except Exception as e:
            self.logger.error(f"Gate期货现货差异处理测试失败: {e}")
            return False
    
    async def test_orderbook_validation_logic(self) -> bool:
        """测试订单簿验证逻辑"""
        try:
            from websocket.orderbook_validator import get_orderbook_validator
            
            validator = get_orderbook_validator()
            
            # 测试有效数据
            valid_data = {
                "asks": [{"p": "50000", "s": "1.0"}],
                "bids": [{"p": "49999", "s": "1.0"}]
            }
            
            result = validator.validate_orderbook_data(
                valid_data, exchange="gate", symbol="BTC_USDT", market_type="futures"
            )
            
            assert result.is_valid, "有效数据应通过验证"
            
            # 测试无效数据
            invalid_data = {"asks": [], "bids": []}
            invalid_result = validator.validate_orderbook_data(
                invalid_data, exchange="gate", symbol="BTC_USDT", market_type="futures" 
            )
            
            assert not invalid_result.is_valid, "无效数据应不通过验证"
            
            return True
        except Exception as e:
            self.logger.error(f"订单簿验证逻辑测试失败: {e}")
            return False
    
    async def test_error_handling_mechanisms(self) -> bool:
        """测试错误处理机制"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("futures")
            
            # 测试空数据处理
            test_data = {}
            # 这不应该抛出异常，而应该优雅处理
            try:
                await client._handle_orderbook(data=test_data)
                # 如果没有抛出异常，说明错误处理机制工作正常
                return True
            except Exception as e:
                # 如果抛出异常，检查是否是预期的优雅错误处理
                if "数据不完整" in str(e) or "validation" in str(e).lower():
                    return True
                else:
                    raise e
            
        except Exception as e:
            self.logger.error(f"错误处理机制测试失败: {e}")
            return False
    
    # =================== 系统级联测试 ===================
    
    async def test_websocket_to_formatter_integration(self) -> bool:
        """测试WebSocket到格式化器的集成"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("futures")
            
            # 模拟Gate期货WebSocket数据
            mock_data = {
                "result": {
                    "currency_pair": "BTC_USDT",
                    "asks": [{"p": "50000", "s": "1.0"}],
                    "bids": [{"p": "49999", "s": "1.0"}]
                }
            }
            
            # 验证数据能被正确处理
            # 这里我们检查处理不会失败
            try:
                await client._handle_orderbook(data=mock_data)
                return True
            except Exception as e:
                self.logger.error(f"WebSocket集成处理失败: {e}")
                return False
            
        except Exception as e:
            self.logger.error(f"WebSocket到格式化器集成测试失败: {e}")
            return False
    
    async def test_three_exchange_consistency(self) -> bool:
        """测试三交易所一致性"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            from websocket.bybit_ws import BybitWebSocketClient
            from websocket.okx_ws import OKXWebSocketClient
            
            # 验证三个交易所的_handle_orderbook方法签名一致
            gate_client = GateWebSocketClient("futures")
            bybit_client = BybitWebSocketClient("futures") 
            okx_client = OKXWebSocketClient("futures")
            
            # 检查方法签名
            import inspect
            gate_sig = inspect.signature(gate_client._handle_orderbook)
            bybit_sig = inspect.signature(bybit_client._handle_orderbook)
            okx_sig = inspect.signature(okx_client._handle_orderbook)
            
            assert str(gate_sig) == str(bybit_sig), "Gate和Bybit方法签名应一致"
            assert str(bybit_sig) == str(okx_sig), "Bybit和OKX方法签名应一致"
            
            return True
        except Exception as e:
            self.logger.error(f"三交易所一致性测试失败: {e}")
            return False
    
    async def test_market_data_flow_cascade(self) -> bool:
        """测试市场数据流级联"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("futures")
            received_data = []
            
            # 模拟market_data事件接收
            def mock_emit(event_type, data):
                if event_type == "market_data":
                    received_data.append(data)
            
            client.emit = mock_emit
            
            # 发送测试数据
            test_data = {
                "result": {
                    "currency_pair": "BTC_USDT",
                    "asks": [{"p": "50000", "s": "1.0"}],
                    "bids": [{"p": "49999", "s": "1.0"}]
                }
            }
            
            await client._handle_orderbook(data=test_data)
            
            # 验证数据被正确传递
            assert len(received_data) > 0, "应该接收到market_data事件"
            
            market_data = received_data[0]
            assert market_data.get("exchange") == "gate", "交易所应为gate"
            assert market_data.get("data_type") == "orderbook", "数据类型应为orderbook"
            
            return True
        except Exception as e:
            self.logger.error(f"市场数据流级联测试失败: {e}")
            return False
    
    async def test_timestamp_sync_integration(self) -> bool:
        """测试时间戳同步集成"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            processor = get_timestamp_processor("gate")
            
            # 测试时间戳处理
            current_time = int(time.time() * 1000)
            test_data = {"timestamp": current_time}
            
            synced_timestamp = processor.get_synced_timestamp(test_data)
            
            # 验证时间戳在合理范围内（1秒差异）
            time_diff = abs(synced_timestamp - current_time)
            assert time_diff < 1000, f"时间戳同步差异过大: {time_diff}ms"
            
            return True
        except Exception as e:
            self.logger.error(f"时间戳同步集成测试失败: {e}")
            return False
    
    async def test_multi_symbol_processing(self) -> bool:
        """测试多交易对处理"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("futures")
            test_symbols = ["BTC_USDT", "ETH_USDT", "ADA_USDT"]
            
            client.set_symbols(test_symbols)
            
            # 验证交易对设置成功
            assert len(client.symbols) > 0, "应该有交易对被设置"
            
            # 测试每个交易对的数据处理
            for symbol in test_symbols[:2]:  # 测试前2个
                test_data = {
                    "result": {
                        "currency_pair": symbol,
                        "asks": [{"p": "1000", "s": "1.0"}],
                        "bids": [{"p": "999", "s": "1.0"}]
                    }
                }
                
                # 不应该抛出异常
                await client._handle_orderbook(data=test_data)
            
            return True
        except Exception as e:
            self.logger.error(f"多交易对处理测试失败: {e}")
            return False
    
    async def test_concurrent_orderbook_updates(self) -> bool:
        """测试并发订单簿更新"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("futures")
            
            # 模拟并发更新
            tasks = []
            for i in range(5):
                test_data = {
                    "result": {
                        "currency_pair": "BTC_USDT",
                        "asks": [{"p": f"{50000+i}", "s": "1.0"}],
                        "bids": [{"p": f"{49999-i}", "s": "1.0"}]
                    }
                }
                tasks.append(client._handle_orderbook(data=test_data))
            
            # 并发执行
            await asyncio.gather(*tasks, return_exceptions=True)
            
            return True
        except Exception as e:
            self.logger.error(f"并发订单簿更新测试失败: {e}")
            return False
    
    # =================== 动态生产测试 ===================
    
    async def test_real_gate_websocket_connection(self) -> bool:
        """测试真实Gate WebSocket连接 (30秒)"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            self.logger.info("    📡 测试真实Gate WebSocket连接...")
            
            client = GateWebSocketClient("futures")
            client.set_symbols(["BTC_USDT"])
            
            received_data = []
            
            def on_market_data(data):
                received_data.append(data)
                self.logger.info(f"    📊 接收到Gate期货数据: {data.get('symbol', 'N/A')}")
            
            client.register_callback("market_data", on_market_data)
            
            # 运行30秒
            start_time = time.time()
            timeout = 30.0
            
            try:
                # 启动WebSocket
                task = asyncio.create_task(client.run())
                
                # 等待数据或超时
                while time.time() - start_time < timeout:
                    await asyncio.sleep(1)
                    if received_data:
                        break
                
                client.running = False
                await asyncio.sleep(1)  # 给客户端时间清理
                
                # 验证是否接收到数据
                success = len(received_data) > 0
                self.logger.info(f"    📈 接收到 {len(received_data)} 条Gate期货数据")
                return success
                
            except asyncio.TimeoutError:
                self.logger.warning("    ⏰ Gate WebSocket连接超时")
                return False
            except Exception as e:
                self.logger.error(f"    💥 Gate WebSocket连接异常: {e}")
                return False
            
        except Exception as e:
            self.logger.error(f"真实Gate WebSocket连接测试失败: {e}")
            return False
    
    async def test_continuous_data_processing(self) -> bool:
        """测试持续数据处理 (60秒)"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            self.logger.info("    🔄 测试60秒持续数据处理...")
            
            client = GateWebSocketClient("futures")
            client.set_symbols(["BTC_USDT", "ETH_USDT"])
            
            processed_count = 0
            error_count = 0
            
            def on_market_data(data):
                nonlocal processed_count
                processed_count += 1
                if processed_count % 10 == 0:
                    self.logger.info(f"    📊 已处理 {processed_count} 条数据")
            
            def on_error(error):
                nonlocal error_count
                error_count += 1
                self.logger.warning(f"    ⚠️ 处理错误: {error}")
            
            client.register_callback("market_data", on_market_data)
            
            # 运行60秒
            start_time = time.time()
            timeout = 60.0
            
            try:
                task = asyncio.create_task(client.run())
                
                while time.time() - start_time < timeout:
                    await asyncio.sleep(5)
                    # 检查处理情况
                    if processed_count > 50:  # 至少处理50条数据才算成功
                        break
                
                client.running = False
                await asyncio.sleep(2)  # 清理时间
                
                success = processed_count >= 10 and error_count < processed_count * 0.1  # 错误率<10%
                self.logger.info(f"    📈 处理数据: {processed_count} 条, 错误: {error_count} 条")
                return success
                
            except Exception as e:
                self.logger.error(f"    💥 持续处理异常: {e}")
                return False
            
        except Exception as e:
            self.logger.error(f"持续数据处理测试失败: {e}")
            return False
    
    async def test_arbitrage_combinations_availability(self) -> bool:
        """测试套利组合可用性 (检查组合[B]和[E])"""
        try:
            # 这里我们测试修复后Gate期货数据能否正确流入价格计算系统
            self.logger.info("    💰 测试套利组合[B]和[E]可用性...")
            
            from websocket.gate_ws import GateWebSocketClient
            from websocket.unified_data_formatter import get_orderbook_formatter
            
            client = GateWebSocketClient("futures")
            formatter = get_orderbook_formatter()
            
            # 模拟Gate期货数据
            gate_futures_data = {
                "result": {
                    "currency_pair": "BTC_USDT",
                    "asks": [{"p": "50000", "s": "1.0"}],
                    "bids": [{"p": "49999", "s": "1.0"}]
                }
            }
            
            # 检查数据处理流程
            processed_data = []
            
            def capture_data(event_type, data):
                if event_type == "market_data":
                    processed_data.append(data)
            
            client.emit = capture_data
            
            await client._handle_orderbook(data=gate_futures_data)
            
            # 验证数据格式正确，可用于套利计算
            if processed_data:
                data = processed_data[0]
                has_asks = len(data.get("asks", [])) > 0
                has_bids = len(data.get("bids", [])) > 0  
                correct_exchange = data.get("exchange") == "gate"
                correct_market_type = data.get("market_type") == "futures"
                
                success = has_asks and has_bids and correct_exchange and correct_market_type
                self.logger.info(f"    ✅ Gate期货数据格式验证: asks={has_asks}, bids={has_bids}, exchange={correct_exchange}")
                return success
            else:
                self.logger.error("    ❌ 未接收到任何处理后的数据")
                return False
            
        except Exception as e:
            self.logger.error(f"套利组合可用性测试失败: {e}")
            return False
    
    async def test_performance_under_load(self) -> bool:
        """测试负载下的性能 (高频数据处理)"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            self.logger.info("    ⚡ 测试高频负载下的性能...")
            
            client = GateWebSocketClient("futures")
            
            # 模拟高频数据
            processed_count = 0
            start_time = time.time()
            
            def count_processed(event_type, data):
                nonlocal processed_count
                processed_count += 1
            
            client.emit = count_processed
            
            # 发送100条高频数据
            for i in range(100):
                test_data = {
                    "result": {
                        "currency_pair": "BTC_USDT",
                        "asks": [{"p": f"{50000+i}", "s": "1.0"}],
                        "bids": [{"p": f"{49999-i}", "s": "1.0"}]
                    }
                }
                await client._handle_orderbook(data=test_data)
            
            processing_time = time.time() - start_time
            avg_latency = (processing_time / 100) * 1000  # ms per message
            
            # 要求平均延迟 < 10ms
            success = avg_latency < 10.0 and processed_count >= 95  # 至少处理95%
            self.logger.info(f"    📊 处理100条数据: {processing_time:.2f}s, 平均延迟: {avg_latency:.2f}ms")
            
            return success
        except Exception as e:
            self.logger.error(f"性能负载测试失败: {e}")
            return False
    
    async def test_error_recovery_scenarios(self) -> bool:
        """测试错误恢复场景"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            self.logger.info("    🛡️ 测试错误恢复场景...")
            
            client = GateWebSocketClient("futures")
            
            # 测试各种错误数据
            error_scenarios = [
                {},  # 空数据
                {"result": {}},  # 空result
                {"result": {"currency_pair": "BTC_USDT"}},  # 缺少asks/bids
                {"result": {"asks": [], "bids": []}},  # 空asks/bids
                {"result": {"asks": [{"invalid": "data"}], "bids": [{"p": "50000", "s": "1.0"}]}},  # 无效数据格式
            ]
            
            error_count = 0
            processed_count = 0
            
            def track_processing(event_type, data):
                nonlocal processed_count
                processed_count += 1
            
            client.emit = track_processing
            
            # 测试每种错误场景
            for i, scenario in enumerate(error_scenarios):
                try:
                    await client._handle_orderbook(data=scenario)
                    self.logger.info(f"    ✅ 错误场景 {i+1} 优雅处理")
                except Exception as e:
                    error_count += 1
                    self.logger.warning(f"    ⚠️ 错误场景 {i+1} 异常: {e}")
            
            # 系统应该优雅处理错误，不崩溃
            success = error_count == 0  # 不应该有未处理的异常
            self.logger.info(f"    🛡️ 错误恢复测试: {len(error_scenarios)}个场景, {error_count}个异常")
            
            return success
        except Exception as e:
            self.logger.error(f"错误恢复场景测试失败: {e}")
            return False
    
    def _calculate_overall_results(self):
        """计算总体测试结果"""
        total_tests = (
            self.test_results["basic_core_tests"]["total"] +
            self.test_results["system_cascade_tests"]["total"] +
            self.test_results["dynamic_production_tests"]["total"]
        )
        
        total_passed = (
            self.test_results["basic_core_tests"]["passed"] +
            self.test_results["system_cascade_tests"]["passed"] +
            self.test_results["dynamic_production_tests"]["passed"]
        )
        
        # 要求100%通过才算成功
        self.test_results["overall_success"] = (total_passed == total_tests and total_tests > 0)
        self.test_results["coverage_percentage"] = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # 记录汇总
        self.logger.info("=" * 60)
        self.logger.info("📋 测试结果汇总:")
        self.logger.info(f"  基础核心测试: {self.test_results['basic_core_tests']['passed']}/{self.test_results['basic_core_tests']['total']}")
        self.logger.info(f"  系统级联测试: {self.test_results['system_cascade_tests']['passed']}/{self.test_results['system_cascade_tests']['total']}")
        self.logger.info(f"  动态生产测试: {self.test_results['dynamic_production_tests']['passed']}/{self.test_results['dynamic_production_tests']['total']}")
        self.logger.info(f"  整体成功率: {self.test_results['coverage_percentage']:.1f}%")
        self.logger.info(f"  最终结果: {'✅ 全部通过' if self.test_results['overall_success'] else '❌ 存在失败'}")
        self.logger.info("=" * 60)


async def main():
    """主测试函数"""
    tester = GateFuturesDataFixComprehensiveTest()
    results = await tester.run_all_tests()
    
    # 保存测试结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = f"gate_futures_fix_test_results_{timestamp}.json"
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 测试结果已保存至: {result_file}")
    
    # 返回退出码
    return 0 if results["overall_success"] else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)