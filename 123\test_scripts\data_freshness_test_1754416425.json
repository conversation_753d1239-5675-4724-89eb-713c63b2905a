{"timestamp": "2025-08-05T19:53:44.644777", "test_suite": "data_freshness_fix_verification", "tests": {"timestamp_processing": {"test_name": "timestamp_processing_performance", "total_data_points": 1000, "processed_count": 100, "total_time_ms": 0.6825923919677734, "avg_processing_time_ms": 0.000514984130859375, "max_processing_time_ms": 0.0064373016357421875, "min_processing_time_ms": 0.0002384185791015625, "throughput_per_second": 1465003.1435557108, "success": true}, "data_freshness": {"test_name": "data_freshness_threshold", "test_cases": [{"age_ms": 500, "description": "新鲜数据", "expected": true, "actual": true, "passed": true}, {"age_ms": 1000, "description": "临界数据", "expected": false, "actual": false, "passed": true}, {"age_ms": 2000, "description": "过期数据", "expected": false, "actual": false, "passed": true}, {"age_ms": 80000, "description": "严重过期数据", "expected": false, "actual": false, "passed": true}], "all_passed": true, "success": true}, "blocking_integration": {"test_name": "blocking_detection_integration", "normal_flow_messages": 10, "blocking_events_detected": 0, "integration_working": true, "success": true}, "memory_stability": {"test_name": "memory_usage_stability", "initial_memory_mb": 24.234375, "final_memory_mb": 24.234375, "memory_increase_mb": 0.0, "data_points_processed": 10000, "memory_stable": true, "success": true}, "concurrent_processing": {"test_name": "concurrent_processing", "total_data_points": 300, "total_processed": 270, "total_time_ms": 114.70699310302734, "concurrent_throughput": 2615.35928965156, "no_data_loss": false, "performance_good": true, "success": false}}, "summary": {"total_tests": 5, "passed_tests": 4, "failed_tests": 1, "success_rate": 80.0, "overall_success": false, "fix_effective": true}}