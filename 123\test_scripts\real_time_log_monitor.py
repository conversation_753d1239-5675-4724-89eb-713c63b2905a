#!/usr/bin/env python3
"""
实时日志监控器 - 检测数据堆积问题
通过监控实际运行的系统日志来验证修复效果

禁止任何模拟数据！监控真实系统日志
"""

import os
import sys
import time
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque
import glob

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class RealTimeLogMonitor:
    """实时日志监控器 - 检测数据新鲜度问题"""
    
    def __init__(self):
        self.monitor_start_time = time.time()
        self.results = {
            "monitor_start": datetime.now().isoformat(),
            "log_analysis": {},
            "data_staleness_events": [],
            "blocking_events": [],
            "performance_metrics": {},
            "final_verdict": {}
        }
        
        # 日志文件路径
        self.log_paths = [
            "logs/*.log",
            "logs/websocket_*.log", 
            "logs/performance_*.log",
            "logs/blocking_*.log"
        ]
        
        # 数据新鲜度问题模式
        self.staleness_patterns = [
            r"数据新鲜度检查失败.*timestamp_age_ms.*(\d+)",
            r"丢弃过期时间戳.*timestamp_age_ms.*(\d+)",
            r"数据过期.*(\d+).*秒",
            r"数据堆积.*(\d+).*ms"
        ]
        
        # 阻塞检测模式
        self.blocking_patterns = [
            r"数据流阻塞",
            r"WebSocket.*阻塞",
            r"消息队列.*积压",
            r"处理延迟.*(\d+).*ms"
        ]
        
        print("🔍 实时日志监控器启动")
        print(f"📂 监控日志路径: {self.log_paths}")
    
    def find_log_files(self) -> List[str]:
        """查找所有日志文件"""
        log_files = []
        for pattern in self.log_paths:
            files = glob.glob(pattern)
            log_files.extend(files)
        
        # 按修改时间排序，最新的在前
        log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        
        print(f"📄 找到 {len(log_files)} 个日志文件")
        for f in log_files[:5]:  # 只显示前5个
            mtime = datetime.fromtimestamp(os.path.getmtime(f))
            print(f"   {f} (修改时间: {mtime.strftime('%H:%M:%S')})")
        
        return log_files
    
    def analyze_log_file(self, file_path: str, max_lines: int = 1000) -> Dict[str, Any]:
        """分析单个日志文件"""
        analysis = {
            "file_path": file_path,
            "staleness_events": [],
            "blocking_events": [],
            "last_modified": None,
            "lines_analyzed": 0
        }
        
        try:
            # 获取文件修改时间
            analysis["last_modified"] = datetime.fromtimestamp(
                os.path.getmtime(file_path)
            ).isoformat()
            
            # 读取最新的日志行
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = deque(f, maxlen=max_lines)
            
            analysis["lines_analyzed"] = len(lines)
            
            # 分析每一行
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                
                # 检查数据新鲜度问题
                for pattern in self.staleness_patterns:
                    match = re.search(pattern, line, re.IGNORECASE)
                    if match:
                        age_ms = None
                        if match.groups():
                            try:
                                age_ms = int(match.group(1))
                            except:
                                pass
                        
                        analysis["staleness_events"].append({
                            "line_num": line_num,
                            "content": line,
                            "age_ms": age_ms,
                            "timestamp": self._extract_timestamp_from_line(line)
                        })
                
                # 检查阻塞事件
                for pattern in self.blocking_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        analysis["blocking_events"].append({
                            "line_num": line_num,
                            "content": line,
                            "timestamp": self._extract_timestamp_from_line(line)
                        })
        
        except Exception as e:
            analysis["error"] = str(e)
        
        return analysis
    
    def _extract_timestamp_from_line(self, line: str) -> Optional[str]:
        """从日志行中提取时间戳"""
        # 常见的时间戳格式
        timestamp_patterns = [
            r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})",
            r"(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})",
            r"(\d{2}:\d{2}:\d{2})"
        ]
        
        for pattern in timestamp_patterns:
            match = re.search(pattern, line)
            if match:
                return match.group(1)
        
        return None
    
    def monitor_real_time_logs(self, duration_seconds: int = 60):
        """实时监控日志文件"""
        print(f"🚀 开始实时监控 {duration_seconds} 秒...")
        
        start_time = time.time()
        log_files = self.find_log_files()
        
        if not log_files:
            print("❌ 未找到任何日志文件")
            return
        
        # 记录初始文件大小
        initial_sizes = {}
        for file_path in log_files:
            try:
                initial_sizes[file_path] = os.path.getsize(file_path)
            except:
                initial_sizes[file_path] = 0
        
        staleness_events_found = []
        blocking_events_found = []
        
        # 监控循环
        check_interval = 5  # 每5秒检查一次
        last_check_time = start_time
        
        while time.time() - start_time < duration_seconds:
            current_time = time.time()
            
            # 检查文件是否有更新
            for file_path in log_files:
                try:
                    current_size = os.path.getsize(file_path)
                    if current_size > initial_sizes.get(file_path, 0):
                        # 文件有更新，分析新内容
                        print(f"📝 检测到日志更新: {os.path.basename(file_path)}")
                        
                        analysis = self.analyze_log_file(file_path, max_lines=100)
                        
                        # 收集新的数据新鲜度事件
                        for event in analysis["staleness_events"]:
                            if event not in staleness_events_found:
                                staleness_events_found.append(event)
                                age_ms = event.get("age_ms", 0)
                                if age_ms and age_ms > 1000:  # 🔥 统一阈值修复：所有交易所使用1000ms阈值
                                    print(f"⚠️ 发现数据新鲜度问题: {age_ms}ms")
                        
                        # 收集新的阻塞事件
                        for event in analysis["blocking_events"]:
                            if event not in blocking_events_found:
                                blocking_events_found.append(event)
                                print(f"🚨 发现阻塞事件: {event['content'][:100]}...")
                        
                        initial_sizes[file_path] = current_size
                
                except Exception as e:
                    continue
            
            # 等待下次检查
            time.sleep(check_interval)
            
            # 显示进度
            elapsed = current_time - start_time
            remaining = duration_seconds - elapsed
            if int(elapsed) % 15 == 0 and elapsed > last_check_time + 10:  # 每15秒显示一次
                print(f"⏱️ 监控进度: {elapsed:.0f}s/{duration_seconds}s, 剩余: {remaining:.0f}s")
                print(f"   数据新鲜度事件: {len(staleness_events_found)}")
                print(f"   阻塞事件: {len(blocking_events_found)}")
                last_check_time = elapsed
        
        # 保存结果
        self.results["data_staleness_events"] = staleness_events_found
        self.results["blocking_events"] = blocking_events_found
        
        print(f"✅ 监控完成")
        print(f"📊 总计发现:")
        print(f"   数据新鲜度事件: {len(staleness_events_found)}")
        print(f"   阻塞事件: {len(blocking_events_found)}")
    
    def analyze_current_system_state(self):
        """分析当前系统状态"""
        print("🔍 分析当前系统状态...")
        
        log_files = self.find_log_files()
        all_analyses = []
        
        for file_path in log_files[:10]:  # 分析最新的10个日志文件
            analysis = self.analyze_log_file(file_path)
            all_analyses.append(analysis)
        
        # 统计结果
        total_staleness_events = 0
        total_blocking_events = 0
        recent_staleness_events = []  # 最近的数据新鲜度事件
        
        for analysis in all_analyses:
            total_staleness_events += len(analysis["staleness_events"])
            total_blocking_events += len(analysis["blocking_events"])
            
            # 收集最近的严重事件（>10秒的数据延迟）
            for event in analysis["staleness_events"]:
                age_ms = event.get("age_ms", 0)
                if age_ms and age_ms > 10000:  # 超过10秒
                    recent_staleness_events.append({
                        "file": os.path.basename(analysis["file_path"]),
                        "age_ms": age_ms,
                        "content": event["content"]
                    })
        
        self.results["log_analysis"] = {
            "files_analyzed": len(all_analyses),
            "total_staleness_events": total_staleness_events,
            "total_blocking_events": total_blocking_events,
            "recent_severe_events": recent_staleness_events
        }
        
        print(f"📊 系统状态分析:")
        print(f"   分析文件数: {len(all_analyses)}")
        print(f"   数据新鲜度事件: {total_staleness_events}")
        print(f"   阻塞事件: {total_blocking_events}")
        print(f"   严重延迟事件(>10s): {len(recent_staleness_events)}")
        
        if recent_staleness_events:
            print(f"⚠️ 发现严重延迟事件:")
            for event in recent_staleness_events[:3]:  # 只显示前3个
                print(f"   {event['age_ms']}ms - {event['content'][:80]}...")
    
    def generate_final_verdict(self):
        """生成最终判断"""
        print("🎯 生成最终判断...")
        
        # 分析数据新鲜度问题
        staleness_events = self.results.get("data_staleness_events", [])
        blocking_events = self.results.get("blocking_events", [])
        log_analysis = self.results.get("log_analysis", {})
        
        # 检查是否有严重的数据堆积问题
        severe_staleness = []
        for event in staleness_events:
            age_ms = event.get("age_ms", 0)
            if age_ms and age_ms > 30000:  # 超过30秒
                severe_staleness.append(event)
        
        # 检查历史日志中的严重事件
        historical_severe = log_analysis.get("recent_severe_events", [])
        
        verdict = {
            "data_stacking_resolved": len(severe_staleness) == 0 and len(historical_severe) == 0,
            "data_blocking_resolved": len(blocking_events) == 0,
            "system_stable": True,
            "confidence_level": "HIGH",
            "evidence": [],
            "recommendations": []
        }
        
        # 收集证据
        if severe_staleness:
            verdict["data_stacking_resolved"] = False
            verdict["evidence"].append(f"发现{len(severe_staleness)}个严重数据堆积事件(>30s)")
        
        if historical_severe:
            verdict["data_stacking_resolved"] = False
            verdict["evidence"].append(f"历史日志中发现{len(historical_severe)}个严重延迟事件(>10s)")
        
        if blocking_events:
            verdict["data_blocking_resolved"] = False
            verdict["evidence"].append(f"发现{len(blocking_events)}个数据阻塞事件")
        
        # 总体判断
        verdict["overall_success"] = (
            verdict["data_stacking_resolved"] and 
            verdict["data_blocking_resolved"]
        )
        
        # 生成建议
        if not verdict["overall_success"]:
            if not verdict["data_stacking_resolved"]:
                verdict["recommendations"].append("数据堆积问题仍然存在，需要进一步检查WebSocket消息处理")
            if not verdict["data_blocking_resolved"]:
                verdict["recommendations"].append("数据阻塞问题仍然存在，需要检查消息队列和处理逻辑")
        else:
            verdict["recommendations"].append("系统运行正常，数据堆积和阻塞问题已解决")
        
        self.results["final_verdict"] = verdict
        
        # 输出结果
        print(f"🎯 最终判断:")
        print(f"   数据堆积问题: {'✅ 已解决' if verdict['data_stacking_resolved'] else '❌ 未解决'}")
        print(f"   数据阻塞问题: {'✅ 已解决' if verdict['data_blocking_resolved'] else '❌ 未解决'}")
        print(f"   总体成功: {'✅ 是' if verdict['overall_success'] else '❌ 否'}")
        
        if verdict["evidence"]:
            print(f"⚠️ 发现的问题:")
            for evidence in verdict["evidence"]:
                print(f"   - {evidence}")
        
        if verdict["recommendations"]:
            print(f"💡 建议:")
            for rec in verdict["recommendations"]:
                print(f"   - {rec}")
    
    def save_results(self):
        """保存监控结果"""
        timestamp = int(time.time())
        filename = f"real_time_log_monitor_{timestamp}.json"
        filepath = os.path.join(os.path.dirname(__file__), filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            print(f"📄 监控结果已保存: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
    
    def run_monitor(self, duration_seconds: int = 60):
        """运行完整监控"""
        print("🚀 启动实时日志监控...")
        print("📋 监控目标:")
        print("   1. 检测数据新鲜度检查失败事件")
        print("   2. 检测数据堆积和阻塞事件")
        print("   3. 分析当前系统状态")
        print("   4. 验证修复效果")
        print("=" * 60)
        
        # 1. 分析当前系统状态
        self.analyze_current_system_state()
        
        # 2. 实时监控
        self.monitor_real_time_logs(duration_seconds)
        
        # 3. 生成最终判断
        self.generate_final_verdict()
        
        # 4. 保存结果
        self.save_results()
        
        return self.results

def main():
    """主函数"""
    monitor = RealTimeLogMonitor()
    
    print("🔍 实时日志监控器")
    print("📋 这是真实的动态测试，监控实际系统日志")
    print("🚫 禁止任何模拟数据")
    print("=" * 60)
    
    # 运行60秒监控
    results = monitor.run_monitor(60)
    
    # 输出最终结论
    verdict = results["final_verdict"]
    print("\n🎯 最终结论")
    print("=" * 60)
    
    if verdict["overall_success"]:
        print("✅ 实时监控成功！数据堆积和阻塞问题已解决")
        print("✅ 系统运行正常，修复效果良好")
    else:
        print("❌ 实时监控发现问题！修复效果不完全")
        for evidence in verdict["evidence"]:
            print(f"   ❌ {evidence}")
    
    return results

if __name__ == "__main__":
    main()
