#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 基础核心测试：优化方案验证测试
验证执行性能优化方案的核心功能实现
"""

import asyncio
import unittest
import time
import logging
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
import sys
import os

# 添加路径
sys.path.append('/root/myproject/123/81#####版本从79回档来的，从新重构了精度问题和修复gate期货缺失/123')

# 核心模块导入
from core.unified_preheating_manager import UnifiedPreheatingManager
from core.exchange_connection_preheater import ExchangeConnectionPreheater, ConnectionStatus
from core.compliance_monitor import ComplianceValidator
from core.price_cache_preheater import PriceCachePreheater
from core.execution_component_preheater import ExecutionComponentPreheater

logger = logging.getLogger(__name__)


class TestCoreOptimizationValidation(unittest.TestCase):
    """基础核心测试类"""
    
    def setUp(self):
        """测试设置"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
    def tearDown(self):
        """测试清理"""
        self.loop.close()
    
    def test_01_unified_preheating_manager_initialization(self):
        """测试1: 统一预热管理器初始化"""
        print("🧪 测试1: 统一预热管理器初始化")
        
        manager = UnifiedPreheatingManager()
        
        # 验证初始化
        self.assertIsInstance(manager, UnifiedPreheatingManager)
        self.assertIn("max_concurrent_operations", manager.preheat_config)
        self.assertEqual(manager.preheat_config["max_concurrent_operations"], 5)
        self.assertIn("preheat_timeout_seconds", manager.preheat_config)
        self.assertEqual(manager.preheat_config["preheat_timeout_seconds"], 300)
        
        # 验证性能指标初始化
        self.assertIn("total_preheat_time", manager.performance_metrics)
        self.assertEqual(manager.performance_metrics["successful_operations"], 0)
        
        print("✅ 测试1通过: 统一预热管理器正确初始化")
    
    def test_02_exchange_connection_preheater_status(self):
        """测试2: 交易所连接预热器状态管理"""
        print("🧪 测试2: 交易所连接预热器状态管理")
        
        preheater = ExchangeConnectionPreheater()
        
        # 验证初始化状态
        self.assertIsInstance(preheater, ExchangeConnectionPreheater)
        self.assertFalse(preheater.preheating_in_progress)
        self.assertIsNone(preheater.preheating_start_time)
        self.assertEqual(len(preheater.preheated_connections), 0)
        
        # 验证状态枚举
        self.assertIn("COLD", [status.value for status in ConnectionStatus])
        self.assertIn("HOT", [status.value for status in ConnectionStatus])
        self.assertIn("PREHEATING", [status.value for status in ConnectionStatus])
        self.assertIn("ERROR", [status.value for status in ConnectionStatus])
        
        print("✅ 测试2通过: 连接预热器状态正确")
    
    def test_03_compliance_validator_api_limits(self):
        """测试3: 合规验证器API限制配置"""
        print("🧪 测试3: 合规验证器API限制配置")
        
        validator = ComplianceValidator()
        
        # 验证API限制配置
        self.assertIn("gate", validator.api_limits)
        self.assertIn("bybit", validator.api_limits)
        self.assertIn("okx", validator.api_limits)
        
        # 验证Gate.io配置
        gate_config = validator.api_limits["gate"]
        self.assertIn("max_requests_per_second", gate_config)
        self.assertIn("max_connections", gate_config)
        self.assertIn("max_websocket_connections", gate_config)
        
        # 验证一致性要求配置
        self.assertEqual(validator.consistency_requirements["websocket_timeout_ms"], 1000)
        self.assertEqual(validator.consistency_requirements["data_freshness_ms"], 1000)
        self.assertEqual(validator.consistency_requirements["timestamp_tolerance_ms"], 1000)
        
        print("✅ 测试3通过: 合规验证器配置正确")
    
    def test_04_price_cache_preheater_initialization(self):
        """测试4: 价格缓存预热器初始化"""
        print("🧪 测试4: 价格缓存预热器初始化")
        
        preheater = PriceCachePreheater()
        
        # 验证初始化
        self.assertIsInstance(preheater, PriceCachePreheater)
        self.assertIn("price_models", preheater.cached_components)
        self.assertIn("execution_prices", preheater.cached_components)
        
        # 验证TTL配置
        self.assertGreater(preheater.default_cache_ttl_seconds, 0)
        self.assertLessEqual(preheater.default_cache_ttl_seconds, 3600)  # 不超过1小时
        
        print("✅ 测试4通过: 价格缓存预热器初始化正确")
    
    def test_05_execution_component_preheater_structure(self):
        """测试5: 执行组件预热器结构"""
        print("🧪 测试5: 执行组件预热器结构")
        
        preheater = ExecutionComponentPreheater()
        
        # 验证组件字典初始化
        self.assertIsInstance(preheater.preheated_components, dict)
        self.assertEqual(len(preheater.preheated_components), 0)  # 初始为空
        
        # 验证配置
        self.assertIn("max_preheat_symbols", preheater.preheat_config)
        self.assertIn("component_timeout_seconds", preheater.preheat_config)
        
        print("✅ 测试5通过: 执行组件预热器结构正确")
    
    async def test_06_async_preheat_configuration(self):
        """测试6: 异步预热配置验证"""
        print("🧪 测试6: 异步预热配置验证")
        
        manager = UnifiedPreheatingManager()
        
        # 模拟空交易所配置
        mock_exchanges = {}
        
        try:
            # 应该能处理空配置而不崩溃
            result = await manager.execute_full_preheat_cycle(mock_exchanges, symbols=["BTC-USDT"])
            
            # 验证返回结构
            self.assertIsInstance(result, dict)
            self.assertIn("total_time_ms", result)
            self.assertIn("operations_completed", result)
            
        except Exception as e:
            # 预期可能的异常，但应该是可控的
            self.assertIsInstance(e, (ValueError, TypeError, KeyError))
        
        print("✅ 测试6通过: 异步预热配置处理正确")
    
    def test_07_parameter_validation_edge_cases(self):
        """测试7: 参数验证边界情况"""
        print("🧪 测试7: 参数验证边界情况")
        
        # 测试空参数
        manager = UnifiedPreheatingManager()
        preheater = ExchangeConnectionPreheater()
        validator = ComplianceValidator()
        
        # 验证默认参数处理
        self.assertIsInstance(manager._get_default_symbols(), list)
        
        # 验证边界值
        self.assertGreaterEqual(validator.api_limits["gate"]["max_requests_per_second"], 1)
        self.assertLessEqual(validator.api_limits["gate"]["max_requests_per_second"], 1000)
        
        # 验证连接获取
        result = preheater.get_preheated_exchange("nonexistent")
        self.assertIsNone(result)
        
        print("✅ 测试7通过: 参数验证边界情况处理正确")
    
    def test_08_performance_metrics_calculation(self):
        """测试8: 性能指标计算"""
        print("🧪 测试8: 性能指标计算")
        
        manager = UnifiedPreheatingManager()
        
        # 模拟性能统计
        mock_stats = {
            "connection_stats": {"connection_pools_created": 3},
            "price_stats": {"execution_price_cached": 20},
            "leverage_stats": {"preheated_count": 15},
            "websocket_stats": {"ws_connections_established": 3}
        }
        
        # 计算性能改善
        improvement = manager._calculate_performance_improvement(mock_stats)
        
        # 验证计算结果
        self.assertIsInstance(improvement, (int, float))
        self.assertGreater(improvement, 1)  # 应该有性能改善
        
        print(f"✅ 测试8通过: 性能改善倍数 {improvement:.1f}x")
    
    def test_09_error_handling_mechanisms(self):
        """测试9: 错误处理机制"""
        print("🧪 测试9: 错误处理机制")
        
        preheater = ExchangeConnectionPreheater()
        
        # 测试错误标记
        preheater._mark_connection_error("test_exchange", "test error")
        
        # 验证错误状态
        if "test_exchange" in preheater.preheated_connections:
            connection = preheater.preheated_connections["test_exchange"]
            self.assertEqual(connection.status, ConnectionStatus.ERROR)
            self.assertEqual(connection.error_message, "test error")
        
        # 测试非法交易所处理
        result = preheater.get_preheated_exchange("invalid_exchange")
        self.assertIsNone(result)
        
        print("✅ 测试9通过: 错误处理机制正确")
    
    def test_10_consistency_validation(self):
        """测试10: 一致性验证"""
        print("🧪 测试10: 一致性验证")
        
        validator = ComplianceValidator()
        
        # 验证1000ms阈值一致性
        consistency_req = validator.consistency_requirements
        websocket_timeout = consistency_req["websocket_timeout_ms"]
        data_freshness = consistency_req["data_freshness_ms"]
        timestamp_tolerance = consistency_req["timestamp_tolerance_ms"]
        
        # 验证关键阈值都是1000ms
        self.assertEqual(websocket_timeout, 1000)
        self.assertEqual(data_freshness, 1000)
        self.assertEqual(timestamp_tolerance, 1000)
        
        # 验证三交易所连接数限制一致（除非特殊规则）
        gate_connections = validator.api_limits["gate"]["max_connections"]
        bybit_connections = validator.api_limits["bybit"]["max_connections"]
        okx_connections = validator.api_limits["okx"]["max_connections"]
        
        self.assertEqual(gate_connections, bybit_connections)
        self.assertEqual(bybit_connections, okx_connections)
        
        print("✅ 测试10通过: 一致性验证正确")


def run_basic_core_tests():
    """运行基础核心测试"""
    print("=" * 80)
    print("🧪 开始执行基础核心测试（测试①）")
    print("=" * 80)
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试方法
    test_methods = [
        'test_01_unified_preheating_manager_initialization',
        'test_02_exchange_connection_preheater_status', 
        'test_03_compliance_validator_api_limits',
        'test_04_price_cache_preheater_initialization',
        'test_05_execution_component_preheater_structure',
        'test_07_parameter_validation_edge_cases',
        'test_08_performance_metrics_calculation',
        'test_09_error_handling_mechanisms',
        'test_10_consistency_validation'
    ]
    
    for method in test_methods:
        suite.addTest(TestCoreOptimizationValidation(method))
    
    # 异步测试单独运行
    async def run_async_tests():
        test_instance = TestCoreOptimizationValidation()
        test_instance.setUp()
        try:
            await test_instance.test_06_async_preheat_configuration()
        finally:
            test_instance.tearDown()
    
    # 运行同步测试
    runner = unittest.TextTestRunner(verbosity=2)
    sync_result = runner.run(suite)
    
    # 运行异步测试
    try:
        asyncio.run(run_async_tests())
        async_success = True
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        async_success = False
    
    # 统计结果
    total_tests = sync_result.testsRun + (1 if async_success else 0)
    failed_tests = len(sync_result.failures) + len(sync_result.errors) + (0 if async_success else 1)
    passed_tests = total_tests - failed_tests
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print("=" * 80)
    print("🧪 基础核心测试结果汇总")
    print("=" * 80)
    print(f"总测试数: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {failed_tests}")
    print(f"成功率: {success_rate:.1f}%")
    print("=" * 80)
    
    if success_rate >= 80:
        print("✅ 基础核心测试通过（成功率≥80%）")
        return True
    else:
        print("❌ 基础核心测试失败（成功率<80%）")
        return False


if __name__ == "__main__":
    result = run_basic_core_tests()
    exit(0 if result else 1)