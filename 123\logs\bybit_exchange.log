2025-08-11 10:22:41.351 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:41.352 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:41.352 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:41.354 [INFO] [BybitExchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:41.354 [INFO] [exchanges.bybit_exchange] 🚀 初始化Bybit交易所（统一账户模式）...
2025-08-11 10:22:41.410 [WARNING] [exchanges.bybit_exchange] 获取Bybit服务器时间失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'，使用本地时间
2025-08-11 10:22:41.412 [INFO] [exchanges.bybit_exchange] ✅ Bybit连接成功，服务器时间: *************
2025-08-11 10:22:41.412 [INFO] [exchanges.bybit_exchange] ✅ Bybit使用统一账户模式
2025-08-11 10:22:41.412 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-08-11 10:22:41.452 [ERROR] [exchanges.bybit_exchange] 获取Bybit余额失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:41.454 [INFO] [exchanges.bybit_exchange] ✅ Bybit初始余额: 0.00 USDT
2025-08-11 10:22:41.454 [INFO] [exchanges.bybit_exchange] ✅ Bybit统一账户模式已激活
2025-08-11 10:22:41.454 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所初始化完成
2025-08-11 10:22:42.464 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-08-11 10:22:42.510 [ERROR] [exchanges.bybit_exchange] 获取Bybit余额失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.837 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:42.837 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:42.837 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:42.881 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:42.883 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:42.883 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:42.883 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:42.944 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.345 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:43.345 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:43.345 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:43.393 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.395 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:43.396 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:43.396 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:43.440 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.730 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:43.730 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:43.730 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:43.825 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:43.831 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:43.831 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:43.831 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:43.915 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.122 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:44.122 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:44.122 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.165 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.168 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:44.168 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:44.168 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.211 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.430 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:44.430 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:44.430 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.473 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.476 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:44.476 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:44.476 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.521 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.741 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:44.742 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:44.742 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.807 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:44.815 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:44.815 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:44.815 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:44.888 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.135 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:45.135 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:45.135 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.202 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.205 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:45.205 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:45.205 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.249 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.540 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:45.540 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:45.540 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.588 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.590 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:45.590 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:45.590 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.634 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.832 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:45.832 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:45.832 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.884 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:45.886 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:45.886 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:45.886 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:45.931 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.145 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:46.146 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:46.146 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.191 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.196 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:46.196 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:46.196 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.250 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.494 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:46.494 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:46.494 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.542 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.544 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:46.545 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:46.545 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.611 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.811 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:46.811 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:46.811 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.865 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:46.868 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:46.868 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:46.868 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:46.920 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.125 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:47.125 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:47.125 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:47.176 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.181 [INFO] [exchanges.bybit_exchange] ✅ Bybit API限速使用统一配置: 50次/秒
2025-08-11 10:22:47.181 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 50/秒
2025-08-11 10:22:47.181 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-11 10:22:47.246 [ERROR] [exchanges.bybit_exchange] 获取Bybit交易对信息失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.526 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ADA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.756 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 DOGE-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:47.975 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SOL-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.111 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 AVAX-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.277 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 DOT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.425 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 BNB-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.575 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 AI16Z-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.710 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 BONK-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:48.864 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 PENGU-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.027 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ERA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.180 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 PUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.397 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 TRUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.698 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 POPCAT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:49.967 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ADA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.154 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 DOGE-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.312 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SOL-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.476 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 AVAX-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.646 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 DOT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:50.864 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 BNB-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.010 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 AI16Z-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.177 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 BONK-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.340 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 PENGU-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.496 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ERA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.635 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 PUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.806 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 TRUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:51.986 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 POPCAT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.205 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ADA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.361 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 DOGE-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.506 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SOL-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.675 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 AVAX-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.815 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 DOT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:52.955 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 BNB-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.212 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 AI16Z-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.427 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 BONK-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.586 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 PENGU-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.743 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ERA-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:53.884 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 PUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:54.048 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 TRUMP-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:22:54.244 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 POPCAT-USDT: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
2025-08-11 10:23:13.272 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-08-11 10:23:13.334 [ERROR] [exchanges.bybit_exchange] 获取Bybit余额失败: TCPConnector.__init__() got an unexpected keyword argument 'tcp_nodelay'
