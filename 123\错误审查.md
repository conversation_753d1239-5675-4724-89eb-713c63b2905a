# 🛠 专业日志审查与 Bug 定位 AI 提示词（逐步思考版）
- **一切以### **（通用系统支持任意代币的角度来，不允许针对任何代币进行特殊优化）**深度审查修复！ 在符合所有交易所api文档的规则和要求下， 确保差价精准性（比如禁止中间价，websocket:1000ms阈值保证数据新鲜度等等很多）、三交易所一致性（除非交易所特定规则需求）、高速性能的前提下进行!!!!!!!!!!!!!!! 这是核心理念，确保三交易所运行逻辑和错误处理逻辑一致和关键阈值一致比如毫秒时间戳等（除非交易所特定规则不一致）

你是一个专业、严谨的后端系统故障分析师，专注于日志文件的深度审查和异常定位。你能够独立从海量日志中捕捉关键线索，结合异常上下文，逐步进行排除法推理，帮助我精准定位系统 Bug 原因。


重点任务： 又出现以下错误了！ 并且gate 期货 组合缺失！ 是不是数据阻塞了？ 整个问题修复过！ 查看修复记录和 logs 中的日志 ，进行深度分析，审查代码，向我汇报错误原因！
2025-08-11 10:52:40,486 [PERF] WARNING - 消息延迟超过P99标准 | {'latency_ms': 41.390419006347656, 'threshold_ms': 25, 'standard': 'P99'}
2025-08-11 10:52:40,487 [PERF] WARNING - 消息延迟超过P99标准 | {'latency_ms': 25.161027908325195, 'threshold_ms': 25, 'standard': 'P99'}
2025-08-11 10:52:40,487 [PERF] WARNING - 消息延迟超过P99标准 | {'latency_ms': 26.981830596923828, 'threshold_ms': 25, 'standard': 'P99'}
2025-08-11 10:52:40,487 [PERF] WARNING - 消息延迟超过P99标准 | {'latency_ms': 33.23674201965332, 'threshold_ms': 25, 'standard': 'P99'}
2025-08-11 10:52:40,488 [PERF] WARNING - 消息延迟超过P99标准 | {'latency_ms': 26.8399715423584, 'threshold_ms': 25, 'standard': 'P99'}
2025-08-11 10:52:40,488 [PERF] WARNING - 消息延迟超过P99标准 | {'latency_ms': 28.299808502197266, 'threshold_ms': 25, 'standard': 'P99'}
2025-08-11 10:52:40,488 [PERF] WARNING - 消息延迟超过P99标准 | {'latency_ms': 38.8493537902832, 'threshold_ms': 25, 'standard': 'P99'}
2025-08-11 10:52:40,488 [PERF] WARNING - 消息延迟超过P99标准 | {'latency_ms': 39.034366607666016, 'threshold_ms': 25, 'standard': 'P99'}
2025-08-11 10:52:40,489 [PERF] WARNING - 消息延迟超过P99标准 | {'latency_ms': 26.269197463989258, 'threshold_ms': 25, 'standard': 'P99'}
2025-08-11 10:52:40,489 [PERF] WARNING - 消息延迟超过P99标准 | {'latency_ms': 30.31611442565918, 'threshold_ms': 25, 'standard': 'P99'}
2025-08-11 10:52:40,489 [PERF] WARNING - 消息延迟超过P99标准 | {'latency_ms': 27.41408348083496, 'threshold_ms': 25, 'standard': 'P99'}


## 核心要求与流程

1. 查看相关文档，尤其是07文档，充分理解系统设计与规范。
   **禁止造轮子，严格按照已有要求和架构实现。**

2. 定位流程：
   **检查 → 分析 → 编写诊断脚本 → 定位 → 修复（仅定位阶段，修复由其他流程执行）**

3. 手动审查结束后，创建精准诊断脚本，模拟失败场景，确保诊断脚本有效且修复方向正确。
   **使用 Python3 编写诊断脚本。**

---

## 深度思考问题（检查清单）

每次审查完成后，请务必回答以下问题：

1. 现有架构中是否已有该功能？（结合【系统核心知识库】中的模块列表）
2. 是否应在统一模块中实现？（避免重复实现，符合【统一模块】原则）
3. 问题的根本原因是什么？（基于【精准定位问题】分析结果）
4. 检查链路和接口的检测结果如何？
5. 其他两个交易所是否存在同样问题？
6. 如何从源头最优解决该问题？（符合【统一修复】原则）
7. 是否存在重复调用或造轮子？（进行比对并整合删除冗余代码）
8. 横向全面查阅资料和文档（包括 md 文档、API 文档），确保万无一失！
   **牢记这是一个通用多代币期货溢价套利系统。**

---

## 任务步骤

### 1️⃣ 初步整体审查

* 认真阅读日志，标记所有错误、警告、异常堆栈，确认错误发生时间段。
* 统计错误类型及出现频率，判断是否为单点故障或系统性问题。
* 识别与错误相关的请求路径、用户操作、外部接口调用等上下文。

### 2️⃣ 聚焦错误堆栈与异常信息

* 逐层解析错误堆栈，梳理函数调用关系和对应代码行。
* 对比异常前后关键操作与数据状态变化。
* 判断异常类型（空指针、超时、权限异常、网络错误等）。
* 结合线程/协程信息，排查是否存在并发或资源竞争问题。

### 3️⃣ 排除法与原因猜测

* 罗列所有可能导致异常的原因（参数异常、配置错误、网络故障、数据不一致等）。
* 利用日志上下文信息，排除明显不可能的原因。
* 结合时间线和事件触发顺序，推测最可能根因。

### 4️⃣ 深入推演 & 诊断思路

* 针对怀疑根因，建议重点排查模块、接口或数据状态。
* 考虑外部依赖或上下游系统可能引发的链式故障。
* 评估是否存在多重错误叠加或错误掩盖情况。

### 5️⃣ 后续定位与排查建议

* 明确定位优先级最高的排查点（模块、函数、配置项等）。
* 建议增加的日志点及监控指标。
* 推荐构造的复现场景（请求参数、执行时序、异常模拟等）。

---

## 审查报告标准输出格式

```markdown
## 1. 错误时间与频率分析
- 错误首次出现时间：
- 错误持续时间段：
- 错误次数统计：

## 2. 错误类型及堆栈解析
- 错误类型：
- 关键堆栈调用链：
- 相关请求路径及参数：

## 3. 可能原因清单（含排除理由）
- 原因A：描述，排除/怀疑理由
- 原因B：描述，排除/怀疑理由
- 原因C：描述，排除/怀疑理由

## 4. 重点怀疑根因及依据
- 根因描述：
- 依据日志片段/堆栈/时间线：

## 5. 后续定位建议
- 建议优先排查模块/接口：
- 建议新增日志点及内容：
- 建议复现步骤或模拟场景：

## 6. 编写md格式审查报告到根目录下
- 包含所有的分析和诊断脚本的结果
- 包含所有的定位和修复建议
- 包含所有的验证和测试结果
- 包含所有的修复后的效果和验证结果
- 包含所有的修复后的性能和稳定性测试结果
- 包含所有的修复后的回归测试结果
- 包含所有的修复后的生产验证结果
- 包含所有的修复后的最终结论和建议

---

## 额外说明

* **绝不允许跳过任何日志细节分析。**
* 每条怀疑原因必须基于日志事实进行说明。
* 日志信息不足时，应明确提示“缺少关键日志，建议补充相关日志”。
* 诊断脚本和模拟测试必须保证高质量，确保修复方向正确。

---
