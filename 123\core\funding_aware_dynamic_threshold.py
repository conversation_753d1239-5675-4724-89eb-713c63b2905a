#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金费率感知动态阈值扩展
扩展现有的DynamicConvergenceThreshold，增加资金费率感知功能

实现策略1：资金费率感知平仓系统
- 在现有动态阈值基础上，根据资金费率情况调整平仓策略
- 有利时：重置动态阈值到-0.5%，延长持仓赚取资金费率
- 不利时：加速平仓到-0.02%或当前阈值，避免支付成本
"""

import time
import math
from typing import Dict, Optional, Any, Tuple
from dataclasses import dataclass
from utils.logger import get_logger
from core.dynamic_convergence_threshold import DynamicConvergenceThreshold
from core.funding_rate_awareness import (
    FundingRateAwareness, 
    FundingRateAwarenessState,
    FundingAwarenessDecision,
    get_funding_rate_awareness
)


@dataclass
class FundingAwareThresholdDecision:
    """资金费率感知阈值决策结果"""
    should_close: bool
    threshold_used: float
    original_threshold: float
    funding_adjusted: bool
    funding_rate: Optional[float] = None
    funding_state: Optional[str] = None
    decision_reason: str = ""
    time_progress: float = 0.0
    reset_count: int = 0


class FundingAwareDynamicThreshold(DynamicConvergenceThreshold):
    """
    资金费率感知动态阈值
    
    扩展DynamicConvergenceThreshold，增加资金费率感知功能：
    1. 保持所有原有功能完全不变
    2. 新增资金费率感知平仓优化
    3. 支持阈值重置和加速平仓
    4. 完善的降级机制确保兼容性
    """
    
    def __init__(self, 
                 initial_threshold: float = 0.005,  # 初始阈值 0.5%
                 final_threshold: float = 0.0002,   # 最终阈值 0.02%
                 max_duration: float = 28800.0,     # 最大持续时间 8小时
                 decay_function: str = "exponential",
                 enable_funding_awareness: bool = True):
        """
        初始化资金费率感知动态阈值
        
        Args:
            initial_threshold: 初始阈值 (默认0.5%)
            final_threshold: 最终阈值 (默认0.02%)  
            max_duration: 最大持续时间 (默认8小时)
            decay_function: 衰减函数类型
            enable_funding_awareness: 是否启用资金费率感知
        """
        # 调用父类初始化
        super().__init__(initial_threshold, final_threshold, max_duration, decay_function)
        
        # 资金费率感知配置
        self.enable_funding_awareness = enable_funding_awareness
        self.funding_awareness = get_funding_rate_awareness() if enable_funding_awareness else None
        
        # 资金费率感知专用阈值
        self.favorable_reset_threshold = 0.005   # 有利时重置到-0.5%
        self.unfavorable_accelerate_threshold = 0.0002  # 不利时加速到-0.02%
        
        # 跟踪资金费率调整状态
        self.position_adjustments: Dict[str, Dict] = {}  # 每个持仓的调整状态
        
        self.logger.info("✅ 资金费率感知动态阈值初始化完成")
        self.logger.info(f"   资金费率感知: {'启用' if self.enable_funding_awareness else '禁用'}")
        if self.enable_funding_awareness:
            self.logger.info(f"   有利重置阈值: {self.favorable_reset_threshold*100:.2f}%")
            self.logger.info(f"   不利加速阈值: {self.unfavorable_accelerate_threshold*100:.2f}%")
    
    def _get_position_key(self, exchange: str, symbol: str) -> str:
        """生成持仓唯一标识"""
        return f"{exchange}_{symbol}"
    
    def _get_adjustment_info(self, exchange: str, symbol: str) -> Dict:
        """获取持仓的调整信息"""
        position_key = self._get_position_key(exchange, symbol)
        return self.position_adjustments.get(position_key, {
            "adjusted": False,
            "adjustment_type": None,  # "reset" or "accelerate"
            "original_threshold": None,
            "adjusted_threshold": None,
            "adjustment_time": None,
            "funding_rate": None
        })
    
    def _set_adjustment_info(self, exchange: str, symbol: str, adjustment_info: Dict):
        """设置持仓的调整信息"""
        position_key = self._get_position_key(exchange, symbol)
        self.position_adjustments[position_key] = adjustment_info
    
    def _clear_adjustment_info(self, exchange: str, symbol: str):
        """清除持仓的调整信息"""
        position_key = self._get_position_key(exchange, symbol)
        if position_key in self.position_adjustments:
            del self.position_adjustments[position_key]
    
    async def should_close_position_with_funding_awareness(
        self, 
        current_spread: float, 
        start_time: float,
        exchange: str,
        symbol: str,
        current_time: Optional[float] = None
    ) -> Tuple[bool, FundingAwareThresholdDecision]:
        """
        资金费率感知平仓判断
        
        这是主要的新增功能，集成资金费率感知到动态阈值系统
        
        Args:
            current_spread: 当前价差
            start_time: 开仓时间戳
            exchange: 交易所名称
            symbol: 交易对符号
            current_time: 当前时间戳
            
        Returns:
            Tuple[bool, FundingAwareThresholdDecision]: (是否平仓, 详细决策信息)
        """
        if current_time is None:
            current_time = time.time()
            
        # 获取原始动态阈值决策
        original_should_close, original_decision_info = self.should_close_position(
            current_spread, start_time, current_time
        )
        original_threshold = original_decision_info['current_threshold']
        
        # 如果资金费率感知未启用，直接返回原始决策
        if not self.enable_funding_awareness or not self.funding_awareness:
            return original_should_close, FundingAwareThresholdDecision(
                should_close=original_should_close,
                threshold_used=original_threshold,
                original_threshold=original_threshold,
                funding_adjusted=False,
                decision_reason=original_decision_info['decision_reason'],
                time_progress=original_decision_info['time_progress']
            )
        
        try:
            # 获取资金费率数据
            from core.unified_funding_rate_provider import get_funding_rate_provider
            funding_provider = get_funding_rate_provider()
            
            # 获取资金费率信息
            funding_response = await funding_provider.get_funding_rate(exchange, symbol, use_cache=True)
            
            if not funding_response.success:
                # 资金费率获取失败，降级到原始逻辑
                self.logger.warning(f"⚠️ 资金费率获取失败，降级到原始动态阈值: {exchange}_{symbol}")
                return original_should_close, FundingAwareThresholdDecision(
                    should_close=original_should_close,
                    threshold_used=original_threshold,
                    original_threshold=original_threshold,
                    funding_adjusted=False,
                    decision_reason=f"资金费率获取失败，使用原始逻辑: {original_decision_info['decision_reason']}",
                    time_progress=original_decision_info['time_progress']
                )
            
            # 创建资金费率信息对象
            funding_info = self.funding_awareness.create_funding_rate_info(
                exchange=exchange,
                symbol=symbol,
                funding_rate=funding_response.funding_rate,
                timestamp=funding_response.timestamp
            )
            
            # 制作资金费率感知决策
            funding_decision = self.funding_awareness.make_closing_decision(
                exchange, symbol, current_spread, funding_info, start_time
            )
            
            # 获取当前调整状态
            adjustment_info = self._get_adjustment_info(exchange, symbol)
            
            # 根据资金费率决策调整阈值
            final_threshold = original_threshold
            funding_adjusted = False
            threshold_adjustment_reason = ""
            
            if funding_decision.action == "reset_threshold":
                # 资金费率有利，重置阈值
                if funding_decision.threshold_adjustment:
                    final_threshold = abs(funding_decision.threshold_adjustment)  # 确保正值
                    funding_adjusted = True
                    threshold_adjustment_reason = "资金费率有利，重置阈值"
                    
                    # 记录调整信息
                    self._set_adjustment_info(exchange, symbol, {
                        "adjusted": True,
                        "adjustment_type": "reset",
                        "original_threshold": original_threshold,
                        "adjusted_threshold": final_threshold,
                        "adjustment_time": current_time,
                        "funding_rate": funding_info.funding_rate
                    })
                    
            elif funding_decision.action == "accelerate_close":
                # 资金费率不利，加速平仓
                if funding_decision.threshold_adjustment:
                    final_threshold = abs(funding_decision.threshold_adjustment)
                    funding_adjusted = True
                    threshold_adjustment_reason = "资金费率不利，加速平仓"
                    
                    # 记录调整信息
                    self._set_adjustment_info(exchange, symbol, {
                        "adjusted": True,
                        "adjustment_type": "accelerate", 
                        "original_threshold": original_threshold,
                        "adjusted_threshold": final_threshold,
                        "adjustment_time": current_time,
                        "funding_rate": funding_info.funding_rate
                    })
            
            # 使用调整后的阈值重新判断
            if funding_adjusted:
                # 重新计算是否应该平仓
                is_spot_premium = current_spread < 0
                meets_adjusted_threshold = abs(current_spread) >= final_threshold
                final_should_close = is_spot_premium and meets_adjusted_threshold
                
                decision_reason = f"{threshold_adjustment_reason} (原阈值:{original_threshold*100:.3f}% → 调整后:{final_threshold*100:.3f}%)"
            else:
                # 使用原始决策
                final_should_close = original_should_close
                decision_reason = original_decision_info['decision_reason']
            
            # 构建详细决策信息
            return final_should_close, FundingAwareThresholdDecision(
                should_close=final_should_close,
                threshold_used=final_threshold,
                original_threshold=original_threshold,
                funding_adjusted=funding_adjusted,
                funding_rate=funding_info.funding_rate,
                funding_state=funding_decision.current_state.value,
                decision_reason=decision_reason,
                time_progress=original_decision_info['time_progress'],
                reset_count=self.funding_awareness.get_reset_count(exchange, symbol)
            )
            
        except Exception as e:
            # 资金费率感知出现异常，降级到原始逻辑
            self.logger.error(f"❌ 资金费率感知异常，降级到原始动态阈值: {exchange}_{symbol} - {e}")
            return original_should_close, FundingAwareThresholdDecision(
                should_close=original_should_close,
                threshold_used=original_threshold,
                original_threshold=original_threshold,
                funding_adjusted=False,
                decision_reason=f"资金费率感知异常，使用原始逻辑: {original_decision_info['decision_reason']}",
                time_progress=original_decision_info['time_progress']
            )
    
    def cleanup_position_funding_state(self, exchange: str, symbol: str):
        """清理持仓的资金费率相关状态"""
        # 清理本类的调整信息
        self._clear_adjustment_info(exchange, symbol)
        
        # 清理资金费率感知系统的状态
        if self.funding_awareness:
            self.funding_awareness.cleanup_position(exchange, symbol)
            
        self.logger.info(f"🧹 [清理资金费率状态] {exchange}_{symbol}")
    
    def force_recovery_to_original_logic(self, exchange: str, symbol: str):
        """强制恢复到原始动态阈值逻辑"""
        # 清理调整状态
        self._clear_adjustment_info(exchange, symbol)
        
        # 强制恢复资金费率感知状态
        if self.funding_awareness:
            self.funding_awareness.force_recovery(exchange, symbol, "手动强制恢复")
            
        self.logger.info(f"🔄 [强制恢复] {exchange}_{symbol} 恢复到原始动态阈值逻辑")
    
    def get_funding_aware_status(self, exchange: str, symbol: str) -> Dict[str, Any]:
        """获取资金费率感知状态信息"""
        adjustment_info = self._get_adjustment_info(exchange, symbol)
        
        status = {
            "funding_awareness_enabled": self.enable_funding_awareness,
            "position_adjusted": adjustment_info["adjusted"],
            "adjustment_type": adjustment_info["adjustment_type"],
            "original_threshold": adjustment_info["original_threshold"],
            "adjusted_threshold": adjustment_info["adjusted_threshold"],
            "adjustment_time": adjustment_info["adjustment_time"],
            "funding_rate": adjustment_info["funding_rate"]
        }
        
        if self.funding_awareness:
            funding_state = self.funding_awareness.get_position_state(exchange, symbol)
            reset_count = self.funding_awareness.get_reset_count(exchange, symbol)
            status.update({
                "funding_state": funding_state.value,
                "reset_count": reset_count,
                "max_reset_count": self.funding_awareness.max_reset_count,
                "can_reset": self.funding_awareness.can_reset_threshold(exchange, symbol)
            })
        
        return status
    
    # 保持与父类完全兼容的接口
    def should_close_position(self, current_spread: float, start_time: float, 
                             current_time: Optional[float] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        保持与父类完全兼容的接口
        
        这个方法保持原有行为不变，新的资金费率感知功能通过
        should_close_position_with_funding_awareness 方法提供
        """
        return super().should_close_position(current_spread, start_time, current_time)


# 全局单例
_funding_aware_threshold_instance = None

def get_funding_aware_dynamic_threshold(
    initial_threshold: float = 0.005,
    final_threshold: float = 0.0002, 
    max_duration: float = 28800.0,
    decay_function: str = "exponential",
    enable_funding_awareness: bool = True
) -> FundingAwareDynamicThreshold:
    """获取资金费率感知动态阈值单例"""
    global _funding_aware_threshold_instance
    if _funding_aware_threshold_instance is None:
        _funding_aware_threshold_instance = FundingAwareDynamicThreshold(
            initial_threshold=initial_threshold,
            final_threshold=final_threshold,
            max_duration=max_duration,
            decay_function=decay_function,
            enable_funding_awareness=enable_funding_awareness
        )
    return _funding_aware_threshold_instance