#!/usr/bin/env python3
"""
WebSocket专用日志配置器
为WebSocket监控创建专用日志文件，支持性能监控、连接状态、错误恢复日志
"""

import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Optional

class WebSocketLogger:
    """WebSocket专用日志管理器"""
    
    def __init__(self, project_root: Optional[str] = None):
        """
        初始化WebSocket日志管理器
        
        Args:
            project_root: 项目根目录，如果为None则自动检测
        """
        if project_root is None:
            # 自动检测项目根目录
            current_file = Path(__file__)
            self.project_root = current_file.parent.parent
        else:
            self.project_root = Path(project_root)
        
        self.logs_dir = self.project_root / "logs"
        self.logs_dir.mkdir(exist_ok=True)
        
        # 获取当前日期用于日志文件名
        self.date_str = datetime.now().strftime("%Y%m%d")
        
        # 初始化各类日志器
        self._setup_loggers()
    
    def _setup_loggers(self):
        """设置各类专用日志器"""
        
        # 1. WebSocket性能监控日志
        self.performance_logger = self._create_logger(
            name="websocket_performance",
            filename=f"websocket_performance_{self.date_str}.log",
            format_str="%(asctime)s [PERF] %(levelname)s - %(message)s"
        )
        
        # 2. WebSocket连接状态日志
        self.connection_logger = self._create_logger(
            name="websocket_connection",
            filename=f"websocket_connection_{self.date_str}.log",
            format_str="%(asctime)s [CONN] %(levelname)s - %(message)s"
        )
        
        # 3. WebSocket错误恢复日志
        self.error_recovery_logger = self._create_logger(
            name="websocket_error_recovery",
            filename=f"websocket_error_recovery_{self.date_str}.log",
            format_str="%(asctime)s [RECOVERY] %(levelname)s - %(message)s"
        )
        
        # 4. WebSocket静默断流专用日志
        self.silent_disconnect_logger = self._create_logger(
            name="websocket_silent_disconnect",
            filename=f"websocket_silent_disconnect_{self.date_str}.log",
            format_str="%(asctime)s [SILENT] %(levelname)s - %(message)s"
        )
        
        # 5. WebSocket订阅失效专用日志
        self.subscription_failure_logger = self._create_logger(
            name="websocket_subscription_failure",
            filename=f"websocket_subscription_failure_{self.date_str}.log",
            format_str="%(asctime)s [SUB_FAIL] %(levelname)s - %(message)s"
        )
    
    def _create_logger(self, name: str, filename: str, format_str: str) -> logging.Logger:
        """
        创建专用日志器
        
        Args:
            name: 日志器名称
            filename: 日志文件名
            format_str: 日志格式字符串
            
        Returns:
            配置好的日志器
        """
        logger = logging.getLogger(name)
        logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 文件处理器 - 支持日志轮转
        file_path = self.logs_dir / filename
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器 - 只显示WARNING及以上级别
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        
        # 设置格式
        formatter = logging.Formatter(format_str)
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def log_performance(self, level: str, message: str, **kwargs):
        """记录性能监控日志"""
        # 🔥 修复：处理None或无效level参数
        if not level or not isinstance(level, str):
            level = "info"
        if not message:
            message = "Empty message"

        logger_method = getattr(self.performance_logger, level.lower(), self.performance_logger.info)
        if kwargs:
            message = f"{message} | {kwargs}"
        logger_method(message)
    
    def log_connection(self, level: str, message: str, exchange: str = None, **kwargs):
        """记录连接状态日志"""
        # 🔥 修复：处理None或无效level参数
        if not level or not isinstance(level, str):
            level = "info"
        if not message:
            message = "Empty message"

        if exchange:
            message = f"[{exchange}] {message}"
        logger_method = getattr(self.connection_logger, level.lower(), self.connection_logger.info)
        if kwargs:
            message = f"{message} | {kwargs}"
        logger_method(message)
    
    def log_error_recovery(self, level: str, message: str, exchange: str = None, error_type: str = None, **kwargs):
        """记录错误恢复日志"""
        # 🔥 修复：处理None或无效level参数
        if not level or not isinstance(level, str):
            level = "info"
        if not message:
            message = "Empty message"

        prefix_parts = []
        if exchange:
            prefix_parts.append(f"[{exchange}]")
        if error_type:
            prefix_parts.append(f"[{error_type}]")

        if prefix_parts:
            message = f"{' '.join(prefix_parts)} {message}"

        logger_method = getattr(self.error_recovery_logger, level.lower(), self.error_recovery_logger.info)
        if kwargs:
            message = f"{message} | {kwargs}"
        logger_method(message)
    
    def log_silent_disconnect(self, level: str, message: str, exchange: str = None, **kwargs):
        """记录静默断流日志"""
        # 🔥 修复：处理None或无效level参数
        if not level or not isinstance(level, str):
            level = "info"
        if not message:
            message = "Empty message"

        if exchange:
            message = f"[{exchange}] {message}"
        logger_method = getattr(self.silent_disconnect_logger, level.lower(), self.silent_disconnect_logger.info)
        if kwargs:
            message = f"{message} | {kwargs}"
        logger_method(message)
    
    def log_subscription_failure(self, level: str, message: str, exchange: str = None, symbol: str = None, **kwargs):
        """记录订阅失效日志"""
        # 🔥 修复：处理None或无效level参数
        if not level or not isinstance(level, str):
            level = "info"
        if not message:
            message = "Empty message"

        prefix_parts = []
        if exchange:
            prefix_parts.append(f"[{exchange}]")
        if symbol:
            prefix_parts.append(f"[{symbol}]")

        if prefix_parts:
            message = f"{' '.join(prefix_parts)} {message}"

        logger_method = getattr(self.subscription_failure_logger, level.lower(), self.subscription_failure_logger.info)
        if kwargs:
            message = f"{message} | {kwargs}"
        logger_method(message)
    
    def get_log_files_info(self) -> dict:
        """获取日志文件信息"""
        info = {
            "logs_directory": str(self.logs_dir),
            "date": self.date_str,
            "log_files": {}
        }
        
        log_files = [
            f"websocket_performance_{self.date_str}.log",
            f"websocket_connection_{self.date_str}.log",
            f"websocket_error_recovery_{self.date_str}.log",
            f"websocket_silent_disconnect_{self.date_str}.log",
            f"websocket_subscription_failure_{self.date_str}.log"
        ]
        
        for log_file in log_files:
            file_path = self.logs_dir / log_file
            info["log_files"][log_file] = {
                "exists": file_path.exists(),
                "path": str(file_path),
                "size_kb": file_path.stat().st_size / 1024 if file_path.exists() else 0
            }
        
        return info

# 全局WebSocket日志器实例
_websocket_logger = None

def get_websocket_logger() -> WebSocketLogger:
    """获取全局WebSocket日志器实例"""
    global _websocket_logger
    if _websocket_logger is None:
        _websocket_logger = WebSocketLogger()
    return _websocket_logger

def log_websocket_performance(level: str, message: str, **kwargs):
    """快捷方法：记录WebSocket性能日志"""
    get_websocket_logger().log_performance(level, message, **kwargs)

def log_websocket_connection(level: str, message: str, exchange: str = None, **kwargs):
    """快捷方法：记录WebSocket连接日志"""
    get_websocket_logger().log_connection(level, message, exchange, **kwargs)

def log_websocket_error_recovery(level: str, message: str, exchange: str = None, error_type: str = None, **kwargs):
    """快捷方法：记录WebSocket错误恢复日志"""
    get_websocket_logger().log_error_recovery(level, message, exchange, error_type, **kwargs)

def log_websocket_silent_disconnect(level: str, message: str, exchange: str = None, **kwargs):
    """快捷方法：记录WebSocket静默断流日志"""
    get_websocket_logger().log_silent_disconnect(level, message, exchange, **kwargs)

def log_websocket_subscription_failure(level: str, message: str, exchange: str = None, symbol: str = None, **kwargs):
    """快捷方法：记录WebSocket订阅失效日志"""
    get_websocket_logger().log_subscription_failure(level, message, exchange, symbol, **kwargs)

if __name__ == "__main__":
    # 测试日志器
    logger = get_websocket_logger()
    
    # 测试各类日志
    log_websocket_performance("info", "WebSocket性能监控测试", latency_ms=5.2, throughput=1500)
    log_websocket_connection("info", "WebSocket连接状态测试", exchange="Gate.io", status="connected")
    log_websocket_error_recovery("warning", "WebSocket错误恢复测试", exchange="Bybit", error_type="connection_lost")
    log_websocket_silent_disconnect("error", "WebSocket静默断流检测", exchange="OKX", silent_duration=30)
    log_websocket_subscription_failure("error", "WebSocket订阅失效检测", exchange="Gate.io", symbol="BTC/USDT")
    
    # 输出日志文件信息
    import json
    print(json.dumps(logger.get_log_files_info(), indent=2, ensure_ascii=False))
