#!/usr/bin/env python3
"""
🔥 WebSocket时间戳问题精确诊断脚本
专门诊断Gate和OKX的WebSocket消息中时间戳字段的实时性问题

诊断目标：
1. 验证Gate.io的't'字段和OKX的'ts'字段是否为过期时间戳
2. 检查队列延迟对时间戳老化的影响
3. 测试瞬时时间戳标记的必要性
4. 分析网络延迟对时间戳准确性的影响
"""

import asyncio
import time
import json
import websockets
import statistics
from typing import Dict, List, Optional, Tuple
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class WebSocketTimestampDiagnostic:
    """WebSocket时间戳问题诊断器"""
    
    def __init__(self):
        self.results = {
            "gate": {"messages": [], "timestamp_ages": [], "queue_delays": []},
            "okx": {"messages": [], "timestamp_ages": [], "queue_delays": []},
            "analysis": {}
        }
        
        # 测试配置
        self.test_duration = 30  # 30秒测试
        self.sample_size = 50    # 采样50条消息
        
        # WebSocket配置
        self.endpoints = {
            "gate": "wss://api.gateio.ws/ws/v4/",
            "okx": "wss://ws.okx.com:8443/ws/v5/public"
        }
        
        # 订阅配置
        self.subscriptions = {
            "gate": {
                "time": int(time.time()),
                "channel": "spot.order_book",
                "event": "subscribe",
                "payload": ["BTC_USDT", "20", "100ms"]
            },
            "okx": {
                "op": "subscribe",
                "args": [{"channel": "books", "instId": "BTC-USDT"}]
            }
        }

    async def diagnose_exchange(self, exchange: str) -> Dict:
        """诊断单个交易所的时间戳问题"""
        print(f"\n🔍 开始诊断 {exchange.upper()} WebSocket时间戳问题...")
        
        messages_collected = 0
        start_time = time.time()
        
        try:
            # 建立WebSocket连接
            uri = self.endpoints[exchange]
            async with websockets.connect(uri) as websocket:
                print(f"✅ {exchange.upper()} WebSocket连接成功")
                
                # 发送订阅消息
                subscription = self.subscriptions[exchange]
                await websocket.send(json.dumps(subscription))
                print(f"📡 {exchange.upper()} 订阅消息已发送")
                
                # 收集消息样本
                while (messages_collected < self.sample_size and 
                       time.time() - start_time < self.test_duration):
                    
                    try:
                        # 🔥 关键测试：记录接收时间戳
                        receive_timestamp = int(time.time() * 1000)
                        
                        # 接收消息
                        message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        
                        # 🔥 模拟队列延迟（这是问题的关键）
                        queue_delay_ms = 100  # 模拟100ms队列延迟
                        await asyncio.sleep(queue_delay_ms / 1000)
                        
                        # 处理时间戳
                        process_timestamp = int(time.time() * 1000)
                        
                        try:
                            data = json.loads(message)
                            
                            # 提取服务器时间戳
                            server_timestamp = self._extract_server_timestamp(exchange, data)
                            
                            if server_timestamp:
                                # 计算时间戳年龄
                                timestamp_age = process_timestamp - server_timestamp
                                queue_delay = process_timestamp - receive_timestamp
                                
                                # 记录数据
                                self.results[exchange]["messages"].append({
                                    "receive_time": receive_timestamp,
                                    "process_time": process_timestamp,
                                    "server_timestamp": server_timestamp,
                                    "timestamp_age_ms": timestamp_age,
                                    "queue_delay_ms": queue_delay,
                                    "message_size": len(message)
                                })
                                
                                self.results[exchange]["timestamp_ages"].append(timestamp_age)
                                self.results[exchange]["queue_delays"].append(queue_delay)
                                
                                messages_collected += 1
                                
                                # 实时显示关键数据
                                if messages_collected % 10 == 0:
                                    print(f"  📊 已收集 {messages_collected} 条消息，"
                                          f"平均时间戳年龄: {statistics.mean(self.results[exchange]['timestamp_ages']):.1f}ms")
                        
                        except json.JSONDecodeError:
                            continue
                            
                    except asyncio.TimeoutError:
                        print(f"  ⏰ {exchange.upper()} 接收超时，继续等待...")
                        continue
                        
        except Exception as e:
            print(f"❌ {exchange.upper()} 诊断异常: {e}")
            return {"error": str(e)}
        
        # 分析结果
        if self.results[exchange]["timestamp_ages"]:
            analysis = self._analyze_timestamp_data(exchange)
            print(f"✅ {exchange.upper()} 诊断完成，收集了 {messages_collected} 条消息")
            return analysis
        else:
            print(f"❌ {exchange.upper()} 未收集到有效数据")
            return {"error": "未收集到有效数据"}

    def _extract_server_timestamp(self, exchange: str, data: Dict) -> Optional[int]:
        """提取服务器时间戳"""
        try:
            if exchange == "gate":
                # Gate.io: 检查't'字段
                if 't' in data:
                    return int(data['t'])
                # 检查嵌套结构
                if 'result' in data and isinstance(data['result'], dict) and 't' in data['result']:
                    return int(data['result']['t'])
                    
            elif exchange == "okx":
                # OKX: 检查'ts'字段
                if 'data' in data and isinstance(data['data'], list) and len(data['data']) > 0:
                    if 'ts' in data['data'][0]:
                        return int(data['data'][0]['ts'])
                if 'ts' in data:
                    return int(data['ts'])
                    
            return None
            
        except (ValueError, TypeError, KeyError):
            return None

    def _analyze_timestamp_data(self, exchange: str) -> Dict:
        """分析时间戳数据"""
        ages = self.results[exchange]["timestamp_ages"]
        delays = self.results[exchange]["queue_delays"]
        
        if not ages:
            return {"error": "无数据可分析"}
        
        analysis = {
            "exchange": exchange,
            "sample_count": len(ages),
            "timestamp_age_stats": {
                "avg_ms": statistics.mean(ages),
                "std_ms": statistics.stdev(ages) if len(ages) > 1 else 0,
                "min_ms": min(ages),
                "max_ms": max(ages),
                "median_ms": statistics.median(ages)
            },
            "queue_delay_stats": {
                "avg_ms": statistics.mean(delays),
                "std_ms": statistics.stdev(delays) if len(delays) > 1 else 0,
                "min_ms": min(delays),
                "max_ms": max(delays)
            },
            "freshness_analysis": {
                "fresh_count": len([age for age in ages if age < 1000]),  # <1秒为新鲜
                "stale_count": len([age for age in ages if age >= 1000]),  # >=1秒为过期
                "freshness_rate": len([age for age in ages if age < 1000]) / len(ages) * 100
            }
        }
        
        return analysis

    async def run_diagnosis(self) -> Dict:
        """运行完整诊断"""
        print("🚀 开始WebSocket时间戳问题精确诊断")
        print(f"📊 测试配置: {self.sample_size}条消息样本，{self.test_duration}秒超时")
        print("🎯 诊断目标: 验证Gate.io和OKX的时间戳实时性问题")
        
        # 并行诊断两个交易所
        tasks = []
        for exchange in ["gate", "okx"]:
            task = asyncio.create_task(self.diagnose_exchange(exchange))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 整理结果
        final_results = {
            "gate": results[0] if not isinstance(results[0], Exception) else {"error": str(results[0])},
            "okx": results[1] if not isinstance(results[1], Exception) else {"error": str(results[1])},
            "diagnosis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_config": {
                "sample_size": self.sample_size,
                "test_duration": self.test_duration
            }
        }
        
        # 生成综合分析
        final_results["comprehensive_analysis"] = self._generate_comprehensive_analysis(final_results)
        
        return final_results

    def _generate_comprehensive_analysis(self, results: Dict) -> Dict:
        """生成综合分析报告"""
        analysis = {
            "problem_confirmed": False,
            "critical_issues": [],
            "recommendations": []
        }
        
        for exchange in ["gate", "okx"]:
            if "error" not in results[exchange]:
                data = results[exchange]
                avg_age = data["timestamp_age_stats"]["avg_ms"]
                freshness_rate = data["freshness_analysis"]["freshness_rate"]
                
                # 检查是否存在时间戳过期问题
                if avg_age > 1000:  # 平均年龄超过1秒
                    analysis["problem_confirmed"] = True
                    analysis["critical_issues"].append(
                        f"{exchange.upper()}: 平均时间戳年龄{avg_age:.1f}ms，超过1000ms阈值"
                    )
                
                if freshness_rate < 50:  # 新鲜度低于50%
                    analysis["critical_issues"].append(
                        f"{exchange.upper()}: 数据新鲜度仅{freshness_rate:.1f}%，低于50%阈值"
                    )
        
        # 生成修复建议
        if analysis["problem_confirmed"]:
            analysis["recommendations"] = [
                "实施瞬时时间戳标记系统：在websockets.recv()后立即打上高精度时间戳",
                "消除队列延迟污染：时间戳在消息进入队列前生成",
                "建立网络延迟监控：基于ping测试推算真实发送时间",
                "统一时间基准：三交易所使用相同的时间标准",
                "限制asyncio.Queue大小防止积压"
            ]
        
        return analysis

    def print_results(self, results: Dict):
        """打印诊断结果"""
        print("\n" + "="*80)
        print("🔥 WebSocket时间戳问题诊断报告")
        print("="*80)
        
        for exchange in ["gate", "okx"]:
            print(f"\n📊 {exchange.upper()} 诊断结果:")
            
            if "error" in results[exchange]:
                print(f"  ❌ 错误: {results[exchange]['error']}")
                continue
            
            data = results[exchange]
            print(f"  📈 样本数量: {data['sample_count']}")
            print(f"  ⏱️ 平均时间戳年龄: {data['timestamp_age_stats']['avg_ms']:.1f}ms")
            print(f"  📊 时间戳年龄范围: {data['timestamp_age_stats']['min_ms']:.1f}ms - {data['timestamp_age_stats']['max_ms']:.1f}ms")
            print(f"  🔄 平均队列延迟: {data['queue_delay_stats']['avg_ms']:.1f}ms")
            print(f"  ✅ 数据新鲜度: {data['freshness_analysis']['freshness_rate']:.1f}%")
        
        # 综合分析
        if "comprehensive_analysis" in results:
            analysis = results["comprehensive_analysis"]
            print(f"\n🎯 综合分析:")
            print(f"  🚨 问题确认: {'是' if analysis['problem_confirmed'] else '否'}")
            
            if analysis["critical_issues"]:
                print(f"  ❌ 关键问题:")
                for issue in analysis["critical_issues"]:
                    print(f"    - {issue}")
            
            if analysis["recommendations"]:
                print(f"  💡 修复建议:")
                for rec in analysis["recommendations"]:
                    print(f"    - {rec}")

    def save_results(self, results: Dict):
        """保存诊断结果"""
        timestamp = int(time.time())
        filename = f"websocket_timestamp_diagnosis_{timestamp}.json"
        filepath = os.path.join("diagnostic_scripts", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 诊断结果已保存到: {filepath}")

async def main():
    """主函数"""
    diagnostic = WebSocketTimestampDiagnostic()
    
    try:
        results = await diagnostic.run_diagnosis()
        diagnostic.print_results(results)
        diagnostic.save_results(results)
        
        return results
        
    except KeyboardInterrupt:
        print("\n⏹️ 诊断被用户中断")
    except Exception as e:
        print(f"\n❌ 诊断异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
