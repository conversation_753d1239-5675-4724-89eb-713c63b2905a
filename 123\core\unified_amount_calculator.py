#!/usr/bin/env python3
"""
🔥 统一金额计算器 - 解决动态金额调整与.env参数冲突
消除所有模块间的金额计算不一致问题，确保系统一致性
"""

import os
import logging
from decimal import Decimal, ROUND_DOWN
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

@dataclass
class AmountCalculationResult:
    """金额计算结果"""
    final_amount: Any  # 支持Decimal和float类型，避免类型不一致
    original_amount: float
    min_amount_applied: bool
    max_amount_applied: bool
    dynamic_adjustment_applied: bool
    calculation_source: str
    warnings: list

class UnifiedAmountCalculator:
    """
    🔥 统一金额计算器 - 解决所有金额计算不一致问题
    
    核心原则：
    1. 所有金额计算都通过此类统一处理
    2. 严格遵循.env配置参数
    3. 提供一致的动态调整逻辑
    4. 确保最小/最大金额限制
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self._load_config()
        
    def _load_config(self):
        """加载配置参数"""
        try:
            # 🔥 核心配置：金额限制
            self.min_order_amount_usd = float(os.getenv('MIN_ORDER_AMOUNT_USD', '35.0'))
            self.max_order_amount_usd = float(os.getenv('MAX_ORDER_AMOUNT_USD', '110.0'))
            self.single_trade_limit_usd = float(os.getenv('SINGLE_TRADE_LIMIT_USD', '250.0'))
            
            # 🔥 动态调整配置
            self.order_depth_safety_factor = float(os.getenv('ORDER_DEPTH_SAFETY_FACTOR', '0.9'))
            self.min_adjustment_ratio = float(os.getenv('MIN_ADJUSTMENT_RATIO', '0.3'))
            
            # 🔥 精度配置
            self.default_amount_precision = int(os.getenv('DEFAULT_AMOUNT_PRECISION', '6'))
            
            self.logger.info(f"✅ 统一金额计算器配置加载完成:")
            self.logger.info(f"   最小订单金额: ${self.min_order_amount_usd}")
            self.logger.info(f"   最大订单金额: ${self.max_order_amount_usd}")
            self.logger.info(f"   安全边际: {self.order_depth_safety_factor}")
            self.logger.info(f"   最小调整比例: {self.min_adjustment_ratio}")
            
        except Exception as e:
            self.logger.error(f"❌ 配置加载失败: {e}")
            # 使用应急默认值
            self.min_order_amount_usd = 35.0
            self.max_order_amount_usd = 110.0
            self.single_trade_limit_usd = 250.0
            self.order_depth_safety_factor = 0.9
            self.min_adjustment_ratio = 0.3
            self.default_amount_precision = 6
    
    def calculate_base_amount(self, price: float, target_usd: Optional[float] = None) -> AmountCalculationResult:
        """
        计算基础交易金额（币数量）
        
        Args:
            price: 当前价格
            target_usd: 目标USD金额，如果为None则使用min_order_amount_usd
            
        Returns:
            AmountCalculationResult: 计算结果
        """
        warnings = []
        
        # 确定目标USD金额
        if target_usd is None:
            target_usd = self.min_order_amount_usd
            
        original_amount = target_usd
        
        # 应用最小金额限制
        min_amount_applied = False
        if target_usd < self.min_order_amount_usd:
            target_usd = self.min_order_amount_usd
            min_amount_applied = True
            warnings.append(f"应用最小金额限制: ${original_amount:.2f} -> ${target_usd:.2f}")
            
        # 应用最大金额限制
        max_amount_applied = False
        if target_usd > self.max_order_amount_usd:
            target_usd = self.max_order_amount_usd
            max_amount_applied = True
            warnings.append(f"应用最大金额限制: ${original_amount:.2f} -> ${target_usd:.2f}")
        
        # 计算币数量
        if price <= 0:
            raise ValueError(f"价格必须大于0: {price}")
            
        coin_amount = target_usd / price
        
        # 精度处理 - 保持Decimal精度直到最终转换
        decimal_amount = Decimal(str(coin_amount))
        precision_factor = Decimal(f"1E-{self.default_amount_precision}")
        final_amount_decimal = (decimal_amount / precision_factor).quantize(Decimal('1'), rounding=ROUND_DOWN) * precision_factor
        
        # 保持Decimal精度，避免float转换引起的精度损失
        final_amount = final_amount_decimal
        
        return AmountCalculationResult(
            final_amount=final_amount,
            original_amount=original_amount,
            min_amount_applied=min_amount_applied,
            max_amount_applied=max_amount_applied,
            dynamic_adjustment_applied=False,
            calculation_source="base_calculation",
            warnings=warnings
        )
    
    def calculate_dynamic_amount(self, target_amount_usd: float, max_spot_value: float, 
                               max_futures_value: float) -> AmountCalculationResult:
        """
        动态金额调整计算 - 基于深度调整，确保不违反配置限制
        
        Args:
            target_amount_usd: 目标USD金额
            max_spot_value: 现货最大可用深度
            max_futures_value: 期货最大可用深度
            
        Returns:
            AmountCalculationResult: 调整后的结果
        """
        warnings = []
        original_amount = target_amount_usd
        
        # 🔥 动态调整：基于深度计算最大可执行金额
        max_executable = min(max_spot_value, max_futures_value) * self.order_depth_safety_factor
        
        # 应用深度限制
        depth_adjusted_amount = min(target_amount_usd, max_executable)
        dynamic_adjustment_applied = depth_adjusted_amount != target_amount_usd
        
        if dynamic_adjustment_applied:
            warnings.append(f"深度调整: ${target_amount_usd:.2f} -> ${depth_adjusted_amount:.2f}")
        
        # 🔥 核心修复：确保不低于最小配置金额
        final_amount = max(depth_adjusted_amount, self.min_order_amount_usd)
        min_amount_applied = final_amount != depth_adjusted_amount
        
        if min_amount_applied:
            warnings.append(f"最小金额保护: ${depth_adjusted_amount:.2f} -> ${final_amount:.2f}")
        
        # 检查调整比例是否合理
        adjustment_ratio = final_amount / original_amount
        if adjustment_ratio < self.min_adjustment_ratio:
            warnings.append(f"调整比例过大: {adjustment_ratio*100:.1f}% < {self.min_adjustment_ratio*100:.1f}%")
        
        return AmountCalculationResult(
            final_amount=final_amount,
            original_amount=original_amount,
            min_amount_applied=min_amount_applied,
            max_amount_applied=False,
            dynamic_adjustment_applied=dynamic_adjustment_applied,
            calculation_source="dynamic_adjustment",
            warnings=warnings
        )
    
    def validate_amount_consistency(self, amounts: Dict[str, float], context: str = "") -> bool:
        """
        验证多个金额的一致性
        
        Args:
            amounts: 金额字典 {"spot": amount, "futures": amount}
            context: 验证上下文
            
        Returns:
            bool: 是否一致
        """
        if len(amounts) < 2:
            return True
            
        amount_values = list(amounts.values())
        max_amount = max(amount_values)
        min_amount = min(amount_values)
        
        # 允许1%的误差
        tolerance = 0.01
        if max_amount > 0:
            relative_diff = (max_amount - min_amount) / max_amount
            if relative_diff > tolerance:
                self.logger.warning(f"⚠️ 金额不一致 [{context}]: {amounts}, 相对差异{relative_diff*100:.2f}%")
                return False
                
        return True
    
    def get_safe_amount(self, exchange: str, symbol: str, market_type: str,
                       amount: float, price: float) -> float:
        """
        获取安全的交易量 - 统一接口

        Args:
            exchange: 交易所
            symbol: 交易对
            market_type: 市场类型
            amount: 原始金额（忽略，使用配置计算）
            price: 当前价格

        Returns:
            float: 安全的交易量
        """
        # 🔥 修复：直接使用统一计算器的基础计算，忽略传入的amount
        # 这确保了所有模块都使用相同的金额计算逻辑
        if price > 0:
            result = self.calculate_base_amount(price)
            return result.final_amount
        else:
            # 没有价格时返回默认值
            return 0.001

# 🌟 全局实例
_global_amount_calculator = None

def get_unified_amount_calculator() -> UnifiedAmountCalculator:
    """获取全局统一金额计算器实例"""
    global _global_amount_calculator
    if _global_amount_calculator is None:
        _global_amount_calculator = UnifiedAmountCalculator()
    return _global_amount_calculator

def calculate_amount_with_config(price: float, target_usd: Optional[float] = None) -> AmountCalculationResult:
    """便捷函数：使用配置计算金额"""
    calculator = get_unified_amount_calculator()
    return calculator.calculate_base_amount(price, target_usd)

def validate_amount_against_config(amount_usd: float) -> Tuple[bool, str]:
    """便捷函数：验证金额是否符合配置"""
    calculator = get_unified_amount_calculator()
    
    if amount_usd < calculator.min_order_amount_usd:
        return False, f"金额${amount_usd:.2f}低于最小限制${calculator.min_order_amount_usd:.2f}"
    
    if amount_usd > calculator.max_order_amount_usd:
        return False, f"金额${amount_usd:.2f}超过最大限制${calculator.max_order_amount_usd:.2f}"
        
    return True, "金额符合配置要求"
