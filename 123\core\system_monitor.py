"""
🔥 系统监控和自动恢复模块
负责监控系统健康状态，自动检测和修复常见问题
"""

import asyncio
import time
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import get_logger


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    FAILED = "failed"


@dataclass
class HealthIssue:
    """健康问题数据类"""
    component: str
    issue_type: str
    severity: HealthStatus
    description: str
    timestamp: float
    auto_fixable: bool = False


class SystemMonitor:
    """🔥 系统监控器 - 自动检测和修复系统问题"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.health_issues: List[HealthIssue] = []
        self.last_check_time = 0
        self.check_interval = 30  # 30秒检查一次
        self.auto_fix_enabled = True
        
        # 监控统计
        self.stats = {
            'total_checks': 0,
            'issues_detected': 0,
            'auto_fixes_attempted': 0,
            'auto_fixes_successful': 0
        }
    
    async def start_monitoring(self):
        """启动系统监控"""
        self.logger.info("🔍 启动系统监控...")
        
        while True:
            try:
                await self.perform_health_check()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"❌ 系统监控异常: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def perform_health_check(self) -> Dict[str, Any]:
        """执行系统健康检查"""
        try:
            self.stats['total_checks'] += 1
            current_time = time.time()
            self.last_check_time = current_time
            
            # 清理旧的健康问题
            self._cleanup_old_issues()
            
            health_report = {
                'timestamp': current_time,
                'overall_status': HealthStatus.HEALTHY,
                'components': {},
                'issues': [],
                'auto_fixes_applied': []
            }
            
            # 1. 检查ArbitrageEngine状态
            arbitrage_health = await self._check_arbitrage_engine()
            health_report['components']['arbitrage_engine'] = arbitrage_health
            
            # 2. 检查ExecutionEngine状态
            execution_health = await self._check_execution_engine()
            health_report['components']['execution_engine'] = execution_health
            
            # 3. 检查WebSocket连接
            websocket_health = await self._check_websocket_connections()
            health_report['components']['websocket'] = websocket_health
            
            # 4. 检查数据流
            dataflow_health = await self._check_data_flow()
            health_report['components']['dataflow'] = dataflow_health
            
            # 5. 汇总健康状态
            all_statuses = [
                arbitrage_health['status'],
                execution_health['status'],
                websocket_health['status'],
                dataflow_health['status']
            ]
            
            # 确定整体状态
            if HealthStatus.CRITICAL in all_statuses or HealthStatus.FAILED in all_statuses:
                health_report['overall_status'] = HealthStatus.CRITICAL
            elif HealthStatus.WARNING in all_statuses:
                health_report['overall_status'] = HealthStatus.WARNING
            
            # 6. 收集所有问题
            for component_health in health_report['components'].values():
                if component_health.get('issues'):
                    health_report['issues'].extend(component_health['issues'])
            
            # 7. 自动修复
            if self.auto_fix_enabled and health_report['issues']:
                auto_fixes = await self._attempt_auto_fixes(health_report['issues'])
                health_report['auto_fixes_applied'] = auto_fixes
            
            # 8. 记录健康报告
            self._log_health_report(health_report)
            
            return health_report
            
        except Exception as e:
            self.logger.error(f"❌ 健康检查异常: {e}")
            return {
                'timestamp': time.time(),
                'overall_status': HealthStatus.FAILED,
                'error': str(e)
            }
    
    async def _check_arbitrage_engine(self) -> Dict[str, Any]:
        """检查ArbitrageEngine状态"""
        try:
            from core.arbitrage_engine import get_arbitrage_engine
            engine = get_arbitrage_engine()
            
            if not engine:
                return {
                    'status': HealthStatus.CRITICAL,
                    'issues': [HealthIssue(
                        component='arbitrage_engine',
                        issue_type='not_initialized',
                        severity=HealthStatus.CRITICAL,
                        description='ArbitrageEngine未初始化',
                        timestamp=time.time(),
                        auto_fixable=False
                    )]
                }
            
            issues = []
            
            # 检查状态一致性
            if hasattr(engine, 'is_executing') and hasattr(engine, 'current_status'):
                if engine.is_executing and engine.current_status.value not in ['executing', 'preparing']:
                    issues.append(HealthIssue(
                        component='arbitrage_engine',
                        issue_type='state_inconsistency',
                        severity=HealthStatus.WARNING,
                        description=f'状态不一致: is_executing={engine.is_executing}, status={engine.current_status.value}',
                        timestamp=time.time(),
                        auto_fixable=True
                    ))
            
            # 检查是否卡在ERROR状态
            if hasattr(engine, 'current_status') and engine.current_status.value == 'error':
                # 检查错误状态持续时间
                if hasattr(engine, '_last_error_log_time'):
                    for session_id, error_time in engine._last_error_log_time.items():
                        if time.time() - error_time > 60:  # 错误状态超过60秒
                            issues.append(HealthIssue(
                                component='arbitrage_engine',
                                issue_type='stuck_in_error',
                                severity=HealthStatus.CRITICAL,
                                description=f'错误状态持续超过60秒: {session_id}',
                                timestamp=time.time(),
                                auto_fixable=True
                            ))
            
            status = HealthStatus.HEALTHY
            if issues:
                severities = [issue.severity for issue in issues]
                if HealthStatus.CRITICAL in severities:
                    status = HealthStatus.CRITICAL
                elif HealthStatus.WARNING in severities:
                    status = HealthStatus.WARNING
            
            return {
                'status': status,
                'issues': issues,
                'details': {
                    'is_executing': getattr(engine, 'is_executing', None),
                    'current_status': getattr(engine, 'current_status', None).value if hasattr(engine, 'current_status') else None
                }
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.FAILED,
                'error': str(e),
                'issues': [HealthIssue(
                    component='arbitrage_engine',
                    issue_type='check_failed',
                    severity=HealthStatus.FAILED,
                    description=f'检查失败: {str(e)}',
                    timestamp=time.time(),
                    auto_fixable=False
                )]
            }
    
    async def _check_execution_engine(self) -> Dict[str, Any]:
        """检查ExecutionEngine状态"""
        try:
            from core.execution_engine import get_execution_engine
            engine = get_execution_engine()
            
            if not engine:
                return {
                    'status': HealthStatus.CRITICAL,
                    'issues': [HealthIssue(
                        component='execution_engine',
                        issue_type='not_initialized',
                        severity=HealthStatus.CRITICAL,
                        description='ExecutionEngine未初始化',
                        timestamp=time.time(),
                        auto_fixable=False
                    )]
                }
            
            issues = []
            
            # 检查是否卡在FAILED状态
            if hasattr(engine, 'current_status') and engine.current_status.value == 'failed':
                issues.append(HealthIssue(
                    component='execution_engine',
                    issue_type='stuck_in_failed',
                    severity=HealthStatus.WARNING,
                    description='ExecutionEngine处于失败状态',
                    timestamp=time.time(),
                    auto_fixable=True
                ))
            
            # 检查执行锁状态
            if hasattr(engine, 'execution_lock') and engine.execution_lock.locked():
                issues.append(HealthIssue(
                    component='execution_engine',
                    issue_type='lock_held',
                    severity=HealthStatus.WARNING,
                    description='执行锁被持有',
                    timestamp=time.time(),
                    auto_fixable=False  # 锁可能正在正常使用
                ))
            
            status = HealthStatus.HEALTHY
            if issues:
                severities = [issue.severity for issue in issues]
                if HealthStatus.CRITICAL in severities:
                    status = HealthStatus.CRITICAL
                elif HealthStatus.WARNING in severities:
                    status = HealthStatus.WARNING
            
            return {
                'status': status,
                'issues': issues,
                'details': {
                    'current_status': getattr(engine, 'current_status', None).value if hasattr(engine, 'current_status') else None,
                    'lock_held': getattr(engine, 'execution_lock', None).locked() if hasattr(engine, 'execution_lock') else None
                }
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.FAILED,
                'error': str(e),
                'issues': []
            }
    
    async def _check_websocket_connections(self) -> Dict[str, Any]:
        """🔥 **统一实现**：100%委托给统一连接池管理器，移除重复逻辑"""
        try:
            # 🚨 **彻底修复**：完全委托给统一连接池管理器
            # 移除所有重复的WebSocket健康检查逻辑
            issues = []

            # 🔥 **统一实现**：直接调用统一连接池管理器的健康检查
            try:
                from websocket.unified_connection_pool_manager import get_connection_pool_manager
                pool_manager = get_connection_pool_manager()

                # 🔥 **关键**：使用统一接口，避免重复实现
                health_status = await pool_manager.get_overall_health_status()

                return {
                    'status': health_status.get('status', HealthStatus.HEALTHY),
                    'issues': health_status.get('issues', []),
                    'details': health_status.get('details', {})
                }

            except Exception as e:
                issues.append(f"统一连接池管理器不可用: {str(e)}")
                return {
                    'status': HealthStatus.FAILED,
                    'issues': issues,
                    'details': {'error': str(e)}
                }

        except Exception as e:
            return {
                'status': HealthStatus.FAILED,
                'error': str(e),
                'issues': ['WebSocket状态检查异常']
            }

    async def _attempt_auto_fixes(self, issues: List[HealthIssue]) -> List[Dict[str, Any]]:
        """尝试自动修复问题"""
        auto_fixes = []

        for issue in issues:
            if not issue.auto_fixable:
                continue

            self.stats['auto_fixes_attempted'] += 1
            fix_result = await self._apply_auto_fix(issue)
            auto_fixes.append(fix_result)

            if fix_result['success']:
                self.stats['auto_fixes_successful'] += 1

        return auto_fixes

    async def _apply_auto_fix(self, issue: HealthIssue) -> Dict[str, Any]:
        """应用自动修复"""
        try:
            self.logger.info(f"🔧 尝试自动修复: {issue.component}.{issue.issue_type}")

            if issue.component == 'arbitrage_engine':
                return await self._fix_arbitrage_engine_issue(issue)
            elif issue.component == 'execution_engine':
                return await self._fix_execution_engine_issue(issue)
            else:
                return {
                    'issue': issue.issue_type,
                    'success': False,
                    'message': '不支持的组件自动修复'
                }

        except Exception as e:
            self.logger.error(f"❌ 自动修复异常: {e}")
            return {
                'issue': issue.issue_type,
                'success': False,
                'message': f'修复异常: {str(e)}'
            }

    async def _fix_arbitrage_engine_issue(self, issue: HealthIssue) -> Dict[str, Any]:
        """修复ArbitrageEngine问题"""
        try:
            from core.arbitrage_engine import get_arbitrage_engine
            engine = get_arbitrage_engine()

            if not engine:
                return {
                    'issue': issue.issue_type,
                    'success': False,
                    'message': 'ArbitrageEngine不可用'
                }

            if issue.issue_type == 'state_inconsistency':
                # 修复状态不一致
                engine._reset_execution_state(force_full_reset=True)
                self.logger.info("✅ 已修复ArbitrageEngine状态不一致")
                return {
                    'issue': issue.issue_type,
                    'success': True,
                    'message': '状态不一致已修复'
                }

            elif issue.issue_type == 'stuck_in_error':
                # 修复卡在错误状态
                engine._reset_execution_state(force_full_reset=True)
                engine.current_status = engine.ArbitrageStatus.SCANNING
                self.logger.info("✅ 已修复ArbitrageEngine错误状态卡住")
                return {
                    'issue': issue.issue_type,
                    'success': True,
                    'message': '错误状态卡住已修复'
                }

            return {
                'issue': issue.issue_type,
                'success': False,
                'message': '未知的ArbitrageEngine问题类型'
            }

        except Exception as e:
            return {
                'issue': issue.issue_type,
                'success': False,
                'message': f'修复ArbitrageEngine失败: {str(e)}'
            }

    async def _fix_execution_engine_issue(self, issue: HealthIssue) -> Dict[str, Any]:
        """修复ExecutionEngine问题"""
        try:
            from core.execution_engine import get_execution_engine
            engine = get_execution_engine()

            if not engine:
                return {
                    'issue': issue.issue_type,
                    'success': False,
                    'message': 'ExecutionEngine不可用'
                }

            if issue.issue_type == 'stuck_in_failed':
                # 修复卡在失败状态
                await engine._reset_execution_state()
                self.logger.info("✅ 已修复ExecutionEngine失败状态")
                return {
                    'issue': issue.issue_type,
                    'success': True,
                    'message': '失败状态已修复'
                }

            return {
                'issue': issue.issue_type,
                'success': False,
                'message': '未知的ExecutionEngine问题类型'
            }

        except Exception as e:
            return {
                'issue': issue.issue_type,
                'success': False,
                'message': f'修复ExecutionEngine失败: {str(e)}'
            }

    def _cleanup_old_issues(self):
        """清理旧的健康问题"""
        current_time = time.time()
        # 保留最近5分钟的问题
        self.health_issues = [
            issue for issue in self.health_issues
            if current_time - issue.timestamp < 300
        ]

    def _log_health_report(self, report: Dict[str, Any]):
        """记录健康报告"""
        if report['overall_status'] == HealthStatus.HEALTHY:
            self.logger.debug(f"✅ 系统健康检查通过")
        elif report['overall_status'] == HealthStatus.WARNING:
            self.logger.warning(f"⚠️ 系统健康检查发现警告: {len(report['issues'])}个问题")
        elif report['overall_status'] == HealthStatus.CRITICAL:
            self.logger.error(f"🚨 系统健康检查发现严重问题: {len(report['issues'])}个问题")

        # 记录自动修复结果
        if report.get('auto_fixes_applied'):
            successful_fixes = [fix for fix in report['auto_fixes_applied'] if fix['success']]
            self.logger.info(f"🔧 自动修复结果: {len(successful_fixes)}/{len(report['auto_fixes_applied'])}个修复成功")

    def get_stats(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        return {
            **self.stats,
            'last_check_time': self.last_check_time,
            'active_issues': len(self.health_issues)
        }

    async def analyze_error_and_get_recovery_strategy(self, arbitrage_engine, execution_engine) -> Dict[str, Any]:
        """🔥 统一错误分析和恢复策略制定"""
        try:
            error_type = self._analyze_error_type(execution_engine)

            # 根据错误类型制定恢复策略
            if error_type == "network_timeout":
                return {
                    'type': error_type,
                    'cooldown': 5,
                    'description': '网络超时错误，采用快速恢复策略'
                }
            elif error_type == "orderbook_sync":
                return {
                    'type': error_type,
                    'cooldown': 10,
                    'description': '订单簿同步错误，采用中等恢复策略'
                }
            elif error_type == "execution_failure":
                return {
                    'type': error_type,
                    'cooldown': 15,
                    'description': '执行失败错误，采用保守恢复策略'
                }
            else:
                return {
                    'type': error_type,
                    'cooldown': 10,
                    'description': '未知错误类型，采用默认恢复策略'
                }

        except Exception as e:
            self.logger.error(f"❌ 错误分析异常: {e}")
            return {
                'type': 'unknown',
                'cooldown': 10,
                'description': '错误分析异常，采用默认策略'
            }

    def _analyze_error_type(self, execution_engine) -> str:
        """🔥 统一错误类型分析"""
        try:
            # 检查ExecutionEngine的最后错误
            if hasattr(execution_engine, 'current_execution') and execution_engine.current_execution:
                error_msg = getattr(execution_engine.current_execution, 'error_message', '')

                if '订单簿数据非同步' in error_msg or '时间差' in error_msg:
                    return "orderbook_sync"
                elif '网络' in error_msg or 'timeout' in error_msg.lower():
                    return "network_timeout"
                elif '执行失败' in error_msg or '并行执行失败' in error_msg:
                    return "execution_failure"

            return "unknown"

        except Exception:
            return "unknown"

    async def perform_health_check_for_component(self, component_name: str, component_instance) -> bool:
        """🔥 统一组件健康检查"""
        try:
            if component_name == 'arbitrage_engine':
                return await self._check_arbitrage_engine_health(component_instance)
            elif component_name == 'execution_engine':
                return await self._check_execution_engine_health(component_instance)
            elif component_name == 'opportunity_scanner':
                return await self._check_opportunity_scanner_health(component_instance)
            else:
                self.logger.warning(f"⚠️ 未知组件类型: {component_name}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 组件健康检查异常: {e}")
            return False

    async def _check_arbitrage_engine_health(self, engine) -> bool:
        """检查ArbitrageEngine健康状态"""
        try:
            health_issues = []

            # 1. WebSocket连接检查
            if hasattr(engine, 'monitor_websocket_health'):
                ws_health = await engine.monitor_websocket_health()
                if not ws_health:
                    health_issues.append("WebSocket连接异常")

            # 2. 状态一致性检查
            if hasattr(engine, 'is_executing') and hasattr(engine, 'current_status'):
                if engine.is_executing and engine.current_status.value not in ['executing', 'preparing']:
                    health_issues.append("状态不一致")

            if health_issues:
                self.logger.warning(f"⚠️ ArbitrageEngine健康检查发现问题: {', '.join(health_issues)}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"❌ ArbitrageEngine健康检查异常: {e}")
            return False

    async def _check_execution_engine_health(self, engine) -> bool:
        """检查ExecutionEngine健康状态"""
        try:
            health_issues = []

            # 检查ExecutionEngine状态
            if hasattr(engine, 'current_status'):
                if engine.current_status.value == 'failed':
                    health_issues.append("ExecutionEngine处于失败状态")

            if health_issues:
                self.logger.warning(f"⚠️ ExecutionEngine健康检查发现问题: {', '.join(health_issues)}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"❌ ExecutionEngine健康检查异常: {e}")
            return False

    async def _check_opportunity_scanner_health(self, scanner) -> bool:
        """🔥 新增：检查OpportunityScanner健康状态"""
        try:
            health_issues = []

            # 检查基本属性
            if not hasattr(scanner, 'running'):
                health_issues.append("缺少running属性")

            # 检查市场数据是否正常
            if hasattr(scanner, 'market_data') and not scanner.market_data:
                health_issues.append("市场数据为空")

            # 检查支持的交易对
            if hasattr(scanner, 'supported_symbols') and not scanner.supported_symbols:
                health_issues.append("支持的交易对为空")

            if health_issues:
                self.logger.warning(f"⚠️ OpportunityScanner健康检查发现问题: {', '.join(health_issues)}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"❌ OpportunityScanner健康检查异常: {e}")
            return False

    async def verify_system_ready_for_recovery(self) -> bool:
        """🔥 统一系统恢复就绪验证"""
        try:
            # 获取系统组件
            from core.arbitrage_engine import get_arbitrage_engine
            engine = get_arbitrage_engine()

            if not engine:
                self.logger.warning("⚠️ ArbitrageEngine不可用")
                return False

            # 1. 基本状态检查
            if hasattr(engine, 'is_executing') and engine.is_executing:
                self.logger.warning("⚠️ 系统仍在执行状态，未准备好")
                return False

            # 2. WebSocket数据流检查
            if hasattr(engine, 'opportunity_scanner') and engine.opportunity_scanner:
                data_count = len(engine.opportunity_scanner.market_data)
                if data_count < 10:  # 至少需要10个数据源
                    self.logger.warning(f"⚠️ WebSocket数据不足: {data_count}个数据源")
                    return False

            # 3. 交易所连接检查
            if hasattr(engine, 'exchanges') and not engine.exchanges:
                self.logger.warning("⚠️ 交易所连接未初始化")
                return False

            self.logger.info("✅ 系统恢复就绪验证通过")
            return True

        except Exception as e:
            self.logger.error(f"❌ 系统恢复就绪验证异常: {e}")
            return False

    async def trigger_recovery(self) -> Dict[str, Any]:
        """🔥 触发系统恢复机制"""
        try:
            self.logger.info("🔧 触发系统恢复机制...")

            # 获取系统组件
            from core.arbitrage_engine import get_arbitrage_engine
            engine = get_arbitrage_engine()

            if not engine:
                return {
                    'success': False,
                    'message': 'ArbitrageEngine不可用',
                    'actions_taken': []
                }

            actions_taken = []

            # 1. 强制重置执行状态
            if hasattr(engine, '_reset_execution_state'):
                try:
                    engine._reset_execution_state(force_full_reset=True)
                    actions_taken.append('执行状态重置')
                    self.logger.info("✅ 执行状态已重置")
                except Exception as e:
                    self.logger.error(f"❌ 执行状态重置失败: {e}")

            # 2. 重置锁定状态
            if hasattr(engine, 'lock'):
                try:
                    # 对于asyncio.Lock，我们不能直接重置，但可以检查状态
                    if engine.lock.locked():
                        self.logger.warning("⚠️ 检测到锁定状态，等待自动释放")
                    actions_taken.append('锁定状态检查')
                except Exception as e:
                    self.logger.error(f"❌ 锁定状态检查失败: {e}")

            # 3. 重置状态标志
            if hasattr(engine, 'is_executing'):
                try:
                    engine.is_executing = False
                    actions_taken.append('执行标志重置')
                    self.logger.info("✅ 执行标志已重置")
                except Exception as e:
                    self.logger.error(f"❌ 执行标志重置失败: {e}")

            # 4. 设置为扫描状态
            if hasattr(engine, 'current_status'):
                try:
                    from core.arbitrage_engine import ArbitrageStatus
                    engine.current_status = ArbitrageStatus.SCANNING
                    actions_taken.append('状态设置为扫描')
                    self.logger.info("✅ 状态已设置为扫描")
                except Exception as e:
                    self.logger.error(f"❌ 状态设置失败: {e}")

            # 5. 清理错误会话
            if hasattr(engine, 'current_session') and engine.current_session:
                try:
                    engine.current_session = None
                    actions_taken.append('错误会话清理')
                    self.logger.info("✅ 错误会话已清理")
                except Exception as e:
                    self.logger.error(f"❌ 错误会话清理失败: {e}")

            # 6. 验证恢复结果
            recovery_success = await self.verify_system_ready_for_recovery()

            result = {
                'success': recovery_success,
                'message': '系统恢复完成' if recovery_success else '系统恢复部分成功',
                'actions_taken': actions_taken,
                'timestamp': time.time()
            }

            if recovery_success:
                self.logger.info("✅ 系统恢复机制执行成功")
            else:
                self.logger.warning("⚠️ 系统恢复机制执行部分成功")

            return result

        except Exception as e:
            self.logger.error(f"❌ 系统恢复机制执行失败: {e}")
            return {
                'success': False,
                'message': f'系统恢复失败: {str(e)}',
                'actions_taken': [],
                'timestamp': time.time()
            }


    async def _check_data_flow(self) -> Dict[str, Any]:
        """检查数据流状态"""
        try:
            # 这里可以添加数据流检查逻辑
            # 暂时返回健康状态
            return {
                'status': HealthStatus.HEALTHY,
                'issues': [],
                'details': {
                    'data_sources': 'unknown'  # 可以从OpportunityScanner获取
                }
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.FAILED,
                'error': str(e),
                'issues': []
            }


# 全局单例
_system_monitor_instance = None

def get_system_monitor() -> SystemMonitor:
    """获取系统监控器实例"""
    global _system_monitor_instance
    if _system_monitor_instance is None:
        _system_monitor_instance = SystemMonitor()
    return _system_monitor_instance
