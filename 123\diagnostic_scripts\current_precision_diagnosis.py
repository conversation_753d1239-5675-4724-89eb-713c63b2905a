#!/usr/bin/env python3
"""
专项精度问题深度诊断脚本 - 当前系统架构精度缺陷全面分析

基于全面审查，发现以下关键精度架构问题：

🚨 CRITICAL精度破坏点：
1. ExecutionEngine.py:1678 - float(adjusted_amount_str) - 破坏字符串精度
2. ExecutionEngine.py:2131,2147 - float(truncate_quantity()) - 破坏Decimal精度
3. 三交易所多处float()转换 - 在返回结果时破坏精度
4. Gate.io:532 - float(formatted_amount) - 合约数量精度破坏
5. TradingRulesPreloader中混合使用Decimal和float

🔍 架构分析目标：
- 精确定位所有精度破坏点
- 验证Decimal vs float使用正确性  
- 检查步长截断逻辑精度损失
- 分析API调用链路精度传递
- 提供精确修复方案
"""

import os
import sys
import json
import time
import traceback
import logging
from decimal import Decimal, getcontext, ROUND_DOWN
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass

# 设置高精度环境
getcontext().prec = 28

@dataclass 
class PrecisionIssue:
    """精度问题数据结构"""
    file_path: str
    line_number: int
    issue_type: str
    description: str
    severity: str
    current_code: str
    recommended_fix: str
    impact_analysis: str

@dataclass
class FloatConversionIssue:
    """Float转换问题"""
    location: str
    original_type: str
    converted_type: str  
    precision_loss: float
    test_input: Any
    test_output: Any

@dataclass
class DiagnosisReport:
    """诊断报告"""
    test_name: str
    status: str
    precision_issues: List[PrecisionIssue] 
    float_conversion_issues: List[FloatConversionIssue]
    recommendations: List[str]
    severity_summary: Dict[str, int]

class CurrentPrecisionDiagnosis:
    """当前系统精度问题深度诊断"""
    
    def __init__(self):
        """初始化诊断器"""
        self.logger = logging.getLogger(__name__)
        self.timestamp = int(time.time())
        self.precision_issues: List[PrecisionIssue] = []
        self.float_conversion_issues: List[FloatConversionIssue] = []
        self.diagnosis_reports: List[DiagnosisReport] = []
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(f'/root/myproject/123/79 优化了系统预热 大幅度优化性能/diagnostic_results/current_precision_diagnosis_{self.timestamp}.log')
            ]
        )

    def diagnose_execution_engine_precision(self) -> DiagnosisReport:
        """诊断ExecutionEngine精度问题"""
        self.logger.info("🔍 诊断ExecutionEngine精度处理逻辑")
        
        issues = []
        float_issues = []
        
        # 🚨 CRITICAL: Line 1678精度破坏点
        issues.append(PrecisionIssue(
            file_path="core/execution_engine.py",
            line_number=1678,
            issue_type="float_conversion_precision_loss",
            description="将format_amount_unified()返回的高精度字符串转换为float，引入精度误差",
            severity="CRITICAL",
            current_code="adjusted_amount = float(adjusted_amount_str)",
            recommended_fix="adjusted_amount = adjusted_amount_str  # 保持字符串格式",
            impact_analysis="破坏Bybit精度要求，导致API 170137错误"
        ))
        
        # 测试float转换精度损失
        test_value = "10257.9132"
        float_result = float(test_value)
        precision_loss = abs(Decimal(test_value) - Decimal(str(float_result)))
        
        float_issues.append(FloatConversionIssue(
            location="ExecutionEngine.py:1678",
            original_type="str",
            converted_type="float",
            precision_loss=float(precision_loss),
            test_input=test_value,
            test_output=float_result
        ))
        
        # 🚨 CRITICAL: Line 2131,2147 Decimal->float转换
        issues.append(PrecisionIssue(
            file_path="core/execution_engine.py", 
            line_number=2131,
            issue_type="decimal_to_float_conversion",
            description="将Decimal.truncate_quantity()结果转换为float，破坏高精度计算",
            severity="CRITICAL",
            current_code="spot_final = float(spot_rule.truncate_quantity(spot_decimal))",
            recommended_fix="spot_final = spot_rule.truncate_quantity(spot_decimal)  # 保持Decimal类型",
            impact_analysis="破坏截断逻辑的精度，可能导致步长不符合要求"
        ))
        
        issues.append(PrecisionIssue(
            file_path="core/execution_engine.py",
            line_number=2147, 
            issue_type="decimal_to_float_conversion",
            description="期货数量Decimal->float转换，与现货处理不一致",
            severity="CRITICAL", 
            current_code="futures_final = float(futures_rule.truncate_quantity(futures_decimal))",
            recommended_fix="futures_final = futures_rule.truncate_quantity(futures_decimal)  # 保持Decimal类型",
            impact_analysis="破坏期货精度，影响对冲质量"
        ))

        return DiagnosisReport(
            test_name="ExecutionEngine精度诊断",
            status="FAILED",
            precision_issues=issues,
            float_conversion_issues=float_issues,
            recommendations=[
                "立即删除Line 1678的float()转换",
                "保持truncate_quantity()返回的Decimal类型", 
                "统一现货和期货的数量类型处理"
            ],
            severity_summary={"CRITICAL": 3, "HIGH": 0, "MEDIUM": 0}
        )

    def diagnose_trading_rules_precision(self) -> DiagnosisReport:
        """诊断TradingRulesPreloader精度问题"""
        self.logger.info("🔍 诊断TradingRulesPreloader精度处理")
        
        issues = []
        float_issues = []
        
        # 🔍 format_amount_unified混合精度问题
        issues.append(PrecisionIssue(
            file_path="core/trading_rules_preloader.py",
            line_number=1774,
            issue_type="decimal_to_float_formatting", 
            description="在Decimal计算后使用float()进行格式化，可能引入精度误差",
            severity="HIGH",
            current_code='formatted = f"{float(adjusted):.{decimal_places}f}"',
            recommended_fix='formatted = format(adjusted, f".{decimal_places}f")  # 直接Decimal格式化',
            impact_analysis="在格式化阶段可能引入微小精度误差，影响API调用"
        ))
        
        # 🔍 _apply_bybit_trailing_zero_fix中的float转换
        issues.append(PrecisionIssue(
            file_path="core/trading_rules_preloader.py",
            line_number=1382,
            issue_type="validation_float_conversion",
            description="在尾随零修复验证中使用float转换，可能破坏精度",
            severity="MEDIUM", 
            current_code="float_val = float(formatted_value)",
            recommended_fix="decimal_val = Decimal(formatted_value)  # 使用Decimal验证",
            impact_analysis="验证逻辑使用float可能导致验证结果不准确"
        ))
        
        # 🔍 format_amount_with_contract_conversion精度问题
        issues.append(PrecisionIssue(
            file_path="core/trading_rules_preloader.py", 
            line_number=1845,
            issue_type="contract_conversion_float",
            description="合约转换中将精度处理结果转换为float，破坏字符串精度",
            severity="HIGH",
            current_code="formatted_amount_float = float(formatted_amount)",
            recommended_fix="# 直接使用formatted_amount字符串进行合约转换计算",
            impact_analysis="OKX期货合约转换可能因精度问题导致张数计算错误"
        ))
        
        # 测试Decimal vs float精度对比
        test_value = Decimal("175.817")
        step_size = Decimal("0.001") 
        
        # Decimal计算
        decimal_result = (test_value / step_size).quantize(Decimal('1'), rounding=ROUND_DOWN) * step_size
        
        # 模拟当前代码的float转换
        float_intermediate = float(str(decimal_result))
        float_formatted = f"{float_intermediate:.3f}"
        
        precision_loss = abs(decimal_result - Decimal(float_formatted))
        
        float_issues.append(FloatConversionIssue(
            location="TradingRulesPreloader格式化链路",
            original_type="Decimal",
            converted_type="float", 
            precision_loss=float(precision_loss),
            test_input=str(test_value),
            test_output=float_formatted
        ))

        return DiagnosisReport(
            test_name="TradingRulesPreloader精度诊断", 
            status="FAILED",
            precision_issues=issues,
            float_conversion_issues=float_issues,
            recommendations=[
                "在format_amount_unified中避免float()格式化",
                "统一使用Decimal进行所有精度计算",
                "合约转换避免中间float转换"
            ],
            severity_summary={"CRITICAL": 0, "HIGH": 2, "MEDIUM": 1}
        )

    def diagnose_exchange_precision_consistency(self) -> DiagnosisReport:
        """诊断三交易所精度处理一致性"""
        self.logger.info("🔍 诊断三交易所精度处理一致性")
        
        issues = []
        float_issues = []
        
        # Gate.io精度问题
        issues.append(PrecisionIssue(
            file_path="exchanges/gate_exchange.py",
            line_number=532,
            issue_type="exchange_float_conversion",
            description="Gate期货下单前将格式化金额转换为float，破坏精度",
            severity="CRITICAL",
            current_code="formatted_amount_float = float(formatted_amount)",
            recommended_fix="# 直接使用formatted_amount字符串，避免float转换",
            impact_analysis="期货下单数量精度被破坏，可能导致API错误或对冲失败"
        ))
        
        # Bybit精度问题  
        issues.append(PrecisionIssue(
            file_path="exchanges/bybit_exchange.py",
            line_number=874,
            issue_type="return_value_float_conversion", 
            description="在返回结果中使用float()转换，破坏执行数量精度",
            severity="HIGH",
            current_code='"actual_amount": float(formatted_amount)',
            recommended_fix='"actual_amount": formatted_amount  # 保持字符串格式',
            impact_analysis="返回给ExecutionEngine的执行数量精度被破坏"
        ))
        
        # OKX类似问题
        issues.append(PrecisionIssue(
            file_path="exchanges/okx_exchange.py", 
            line_number=976,
            issue_type="return_value_float_conversion",
            description="与Bybit相同的返回值精度破坏模式",
            severity="HIGH", 
            current_code='"actual_amount": float(formatted_amount)',
            recommended_fix='"actual_amount": formatted_amount  # 保持字符串格式',
            impact_analysis="三交易所精度处理不一致，影响系统统一性"
        ))
        
        # 测试三交易所精度一致性
        test_amount = "175.817"
        exchanges_float_results = {
            "gate": float(test_amount),
            "bybit": float(test_amount), 
            "okx": float(test_amount)
        }
        
        # 检查是否完全一致（考虑浮点精度）
        values = list(exchanges_float_results.values())
        is_consistent = all(abs(v - values[0]) < 1e-10 for v in values)
        
        if not is_consistent:
            float_issues.append(FloatConversionIssue(
                location="三交易所返回值处理",
                original_type="str", 
                converted_type="float",
                precision_loss=0.0,  # 在这个测试中一致
                test_input=test_amount,
                test_output=exchanges_float_results
            ))

        return DiagnosisReport(
            test_name="三交易所精度一致性诊断",
            status="FAILED", 
            precision_issues=issues,
            float_conversion_issues=float_issues,
            recommendations=[
                "统一删除所有交易所的float()转换", 
                "保持API返回值的字符串格式",
                "建立交易所精度处理标准规范"
            ],
            severity_summary={"CRITICAL": 1, "HIGH": 2, "MEDIUM": 0}
        )

    def diagnose_step_truncation_precision(self) -> DiagnosisReport:
        """诊断步长截断逻辑精度"""
        self.logger.info("🔍 诊断步长截断逻辑精度问题")
        
        issues = []
        float_issues = []
        
        # 测试truncate_quantity精度
        test_cases = [
            ("10257.9132", "0.0001"),  # Bybit案例
            ("175.817", "0.001"),      # DOGE案例
            ("0.123456789", "0.000001") # 高精度案例
        ]
        
        for amount_str, step_str in test_cases:
            amount_decimal = Decimal(amount_str)
            step_decimal = Decimal(step_str)
            
            # 模拟当前truncate_quantity逻辑
            truncated = (amount_decimal // step_decimal) * step_decimal
            
            # 检验是否为步长整数倍
            remainder = truncated % step_decimal
            is_valid = remainder == 0
            
            if not is_valid:
                issues.append(PrecisionIssue(
                    file_path="core/trading_rules_preloader.py",
                    line_number=85,
                    issue_type="truncation_precision_error",
                    description=f"截断结果{truncated}不是步长{step_decimal}的整数倍",
                    severity="HIGH",
                    current_code="truncated = (qty // self.qty_step) * self.qty_step",
                    recommended_fix="使用quantize()方法确保精度",
                    impact_analysis="步长截断不准确可能导致API拒绝"
                ))
            
            # 测试转换为float后的精度损失
            float_result = float(truncated)
            decimal_from_float = Decimal(str(float_result))
            precision_loss = abs(truncated - decimal_from_float)
            
            if precision_loss > 0:
                float_issues.append(FloatConversionIssue(
                    location=f"truncate_quantity({amount_str}, {step_str})",
                    original_type="Decimal", 
                    converted_type="float",
                    precision_loss=float(precision_loss),
                    test_input=amount_str,
                    test_output=float_result
                ))

        return DiagnosisReport(
            test_name="步长截断精度诊断",
            status="PASSED" if not issues else "FAILED",
            precision_issues=issues,
            float_conversion_issues=float_issues, 
            recommendations=[
                "保持truncate_quantity返回Decimal类型",
                "避免截断后立即转换为float", 
                "使用quantize确保精度一致性"
            ],
            severity_summary={"CRITICAL": 0, "HIGH": len(issues), "MEDIUM": 0}
        )

    def diagnose_api_parameter_precision(self) -> DiagnosisReport:
        """诊断API调用参数精度处理"""
        self.logger.info("🔍 诊断API调用参数精度处理")
        
        issues = []
        float_issues = []
        
        # 模拟API调用链路精度传递
        test_amount = "10257.9132"
        
        # 链路1: ExecutionEngine -> format_amount_unified -> str
        formatted_str = test_amount  # 模拟format_amount_unified返回
        
        # 链路2: ExecutionEngine -> float() 转换 [问题点]
        converted_float = float(formatted_str)
        
        # 链路3: Exchange -> API调用 -> str()参数
        api_parameter = str(converted_float)
        
        # 检查精度损失
        original_decimal = Decimal(test_amount)
        final_decimal = Decimal(api_parameter)
        total_precision_loss = abs(original_decimal - final_decimal)
        
        if total_precision_loss > 0:
            issues.append(PrecisionIssue(
                file_path="API调用链路",
                line_number=0,
                issue_type="api_parameter_precision_loss", 
                description=f"从{test_amount}到API参数{api_parameter}的精度损失",
                severity="CRITICAL",
                current_code="str(float(formatted_amount))",
                recommended_fix="直接使用formatted_amount字符串作为API参数",
                impact_analysis="API参数精度损失导致交易所拒绝订单"
            ))
            
            float_issues.append(FloatConversionIssue(
                location="完整API调用链路",
                original_type="str",
                converted_type="str(via float)",
                precision_loss=float(total_precision_loss), 
                test_input=test_amount,
                test_output=api_parameter
            ))

        # 测试不同交易所的参数格式要求
        exchange_formats = {
            "bybit": {"type": "str", "precision": "步长精确"},
            "gate": {"type": "int", "precision": "整数"},  
            "okx": {"type": "str", "precision": "合约张数"}
        }
        
        for exchange, format_req in exchange_formats.items():
            if exchange == "gate" and "." in api_parameter:
                issues.append(PrecisionIssue(
                    file_path=f"exchanges/{exchange}_exchange.py",
                    line_number=0,
                    issue_type="api_format_mismatch",
                    description=f"{exchange}要求整数参数，但传入{api_parameter}",
                    severity="HIGH", 
                    current_code=f"API调用使用{api_parameter}",
                    recommended_fix="根据交易所要求进行格式转换",
                    impact_analysis=f"{exchange} API可能拒绝非整数参数"
                ))

        return DiagnosisReport(
            test_name="API参数精度诊断",
            status="FAILED" if issues else "PASSED",
            precision_issues=issues,
            float_conversion_issues=float_issues,
            recommendations=[
                "建立统一的API参数精度标准",
                "每个交易所根据要求进行最终格式转换",
                "避免中间float转换导致的精度损失"
            ],
            severity_summary={"CRITICAL": 1 if total_precision_loss > 0 else 0, "HIGH": len([i for i in issues if i.severity == "HIGH"]), "MEDIUM": 0}
        )

    def run_comprehensive_diagnosis(self) -> Dict[str, Any]:
        """运行全面精度诊断"""
        self.logger.info("🚀 开始当前系统精度问题全面诊断")
        self.logger.info("=" * 80)
        
        # 执行所有诊断测试
        diagnostic_tests = [
            self.diagnose_execution_engine_precision,
            self.diagnose_trading_rules_precision, 
            self.diagnose_exchange_precision_consistency,
            self.diagnose_step_truncation_precision,
            self.diagnose_api_parameter_precision
        ]
        
        for test_func in diagnostic_tests:
            try:
                report = test_func()
                self.diagnosis_reports.append(report)
                self.precision_issues.extend(report.precision_issues)
                self.float_conversion_issues.extend(report.float_conversion_issues)
                self.logger.info(f"✅ {report.test_name}: {report.status}")
            except Exception as e:
                self.logger.error(f"❌ 诊断测试失败 {test_func.__name__}: {str(e)}")
                traceback.print_exc()
        
        return self.generate_final_report()

    def generate_final_report(self) -> Dict[str, Any]:
        """生成最终诊断报告"""
        self.logger.info("📋 生成最终精度诊断报告")
        
        # 统计严重程度
        severity_count = {"CRITICAL": 0, "HIGH": 0, "MEDIUM": 0, "LOW": 0}
        for issue in self.precision_issues:
            severity_count[issue.severity] += 1
        
        # 按严重程度排序问题
        critical_issues = [i for i in self.precision_issues if i.severity == "CRITICAL"]
        high_issues = [i for i in self.precision_issues if i.severity == "HIGH"]
        
        # 生成修复优先级
        priority_fixes = []
        for issue in critical_issues:
            priority_fixes.append(f"🚨 CRITICAL: {issue.file_path}:{issue.line_number} - {issue.description}")
        for issue in high_issues[:3]:  # 只显示前3个高优先级
            priority_fixes.append(f"⚠️ HIGH: {issue.file_path}:{issue.line_number} - {issue.description}")
        
        report = {
            "诊断时间": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(self.timestamp)),
            "系统状态": "存在严重精度问题" if severity_count["CRITICAL"] > 0 else "需要优化",
            "问题统计": {
                "总问题数": len(self.precision_issues),
                "CRITICAL级别": severity_count["CRITICAL"], 
                "HIGH级别": severity_count["HIGH"],
                "MEDIUM级别": severity_count["MEDIUM"],
                "float转换问题": len(self.float_conversion_issues)
            },
            "关键发现": {
                "最严重问题": "ExecutionEngine.py:1678 float()转换破坏精度",
                "影响范围": "所有三个交易所的精度处理链路",
                "主要原因": "在精度敏感环节使用float()转换",
                "潜在影响": "API调用被拒绝，对冲质量下降，交易失败"
            },
            "详细问题列表": [
                {
                    "文件": issue.file_path,
                    "行号": issue.line_number,
                    "类型": issue.issue_type,
                    "描述": issue.description,
                    "严重程度": issue.severity,
                    "当前代码": issue.current_code,
                    "推荐修复": issue.recommended_fix,
                    "影响分析": issue.impact_analysis
                }
                for issue in sorted(self.precision_issues, key=lambda x: {"CRITICAL": 0, "HIGH": 1, "MEDIUM": 2}.get(x.severity, 3))
            ],
            "float转换问题": [
                {
                    "位置": issue.location,
                    "原始类型": issue.original_type, 
                    "转换类型": issue.converted_type,
                    "精度损失": issue.precision_loss,
                    "测试输入": issue.test_input,
                    "测试输出": issue.test_output
                }
                for issue in self.float_conversion_issues
            ],
            "诊断测试结果": [
                {
                    "测试名称": report.test_name,
                    "状态": report.status,
                    "问题数量": len(report.precision_issues),
                    "关键建议": report.recommendations
                }
                for report in self.diagnosis_reports
            ],
            "紧急修复建议": priority_fixes,
            "修复原则": [
                "🎯 核心原则：保持字符串精度，避免不必要的float转换",
                "🔧 执行引擎：删除Line 1678的float()转换，保持字符串传递",
                "⚖️ 精度计算：统一使用Decimal进行所有高精度计算",
                "🏭 交易所接口：在最终API调用前才进行必要的类型转换",  
                "🔗 数据链路：建立端到端的精度保证机制"
            ],
            "验证要求": [
                "修复后必须通过Bybit 170137错误测试",
                "验证三交易所精度处理完全一致", 
                "确保truncate_quantity精度不损失",
                "API参数精度符合各交易所要求"
            ]
        }
        
        # 保存报告
        report_path = f'/root/myproject/123/79 优化了系统预热 大幅度优化性能/diagnostic_results/current_precision_full_diagnosis_{self.timestamp}.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📄 完整诊断报告已保存: {report_path}")
        
        # 输出关键统计
        self.logger.info("🎯 精度诊断关键结论:")
        self.logger.error(f"   🚨 CRITICAL问题: {severity_count['CRITICAL']}个")
        self.logger.error(f"   ⚠️ HIGH问题: {severity_count['HIGH']}个") 
        self.logger.warning(f"   💭 MEDIUM问题: {severity_count['MEDIUM']}个")
        self.logger.info(f"   📊 Float转换问题: {len(self.float_conversion_issues)}个")
        
        if severity_count["CRITICAL"] > 0:
            self.logger.error("   ❌ 系统存在严重精度问题，需要立即修复！")
        else:
            self.logger.info("   ✅ 无严重精度问题，但建议进行优化")
        
        return report

def main():
    """主函数"""
    try:
        print("🔧 当前系统精度问题深度诊断工具")
        print("=" * 50)
        
        diagnosis = CurrentPrecisionDiagnosis()
        report = diagnosis.run_comprehensive_diagnosis()
        
        print(f"\n📊 诊断完成！")
        print(f"🚨 CRITICAL问题: {report['问题统计']['CRITICAL级别']}个")
        print(f"⚠️ HIGH问题: {report['问题统计']['HIGH级别']}个") 
        print(f"💭 MEDIUM问题: {report['问题统计']['MEDIUM级别']}个")
        print(f"📄 详细报告已生成")
        
        if report['问题统计']['CRITICAL级别'] > 0:
            print("\n🚨 发现严重精度问题，建议立即修复！")
            print("关键修复点:")
            for fix in report['紧急修复建议'][:3]:
                print(f"  - {fix}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 诊断过程出错: {str(e)}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())