#!/usr/bin/env python3
"""
数据新鲜度检查失败精确诊断脚本
分析数据堆积80+秒的根本原因

问题现象：
- Gate.io: timestamp_age_ms: 80862, max_age_ms: 1000, discarded_timestamp: 1754412617489
- OKX: timestamp_age_ms: 86237, max_age_ms: 1000, discarded_timestamp: 1754412611601

需要分析：
1. 时间戳问题：是否时间戳计算错误
2. 数据新鲜度检查问题：是否检查逻辑有缺陷
3. 数据阻塞检查问题：是否阻塞检测函数重复或冲突
"""

import sys
import os
import time
import json
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def analyze_timestamp_issue():
    """分析时间戳问题"""
    print("🔍 分析1：时间戳问题诊断")
    print("=" * 50)
    
    # 分析日志中的时间戳
    discarded_timestamps = [1754412617489, 1754412611601]  # 从日志中提取
    current_time_ms = int(time.time() * 1000)
    
    print(f"当前时间戳: {current_time_ms}")
    print(f"当前时间: {datetime.fromtimestamp(current_time_ms/1000)}")
    print()
    
    for i, ts in enumerate(discarded_timestamps):
        exchange = "Gate.io" if i == 0 else "OKX"
        age_ms = current_time_ms - ts
        age_seconds = age_ms / 1000
        
        print(f"{exchange}:")
        print(f"  丢弃的时间戳: {ts}")
        print(f"  时间戳对应时间: {datetime.fromtimestamp(ts/1000)}")
        print(f"  当前年龄: {age_ms}ms ({age_seconds:.1f}秒)")
        print(f"  是否超过1000ms阈值: {'是' if age_ms > 1000 else '否'}")
        print()
    
    # 检查时间戳是否合理
    now = datetime.now()
    for i, ts in enumerate(discarded_timestamps):
        exchange = "Gate.io" if i == 0 else "OKX"
        ts_time = datetime.fromtimestamp(ts/1000)
        time_diff = now - ts_time
        
        if time_diff.total_seconds() > 3600:  # 超过1小时
            print(f"⚠️ {exchange}时间戳异常：时间差{time_diff.total_seconds():.1f}秒")
        elif time_diff.total_seconds() > 300:  # 超过5分钟
            print(f"🔧 {exchange}时间戳延迟：时间差{time_diff.total_seconds():.1f}秒")
        else:
            print(f"✅ {exchange}时间戳正常：时间差{time_diff.total_seconds():.1f}秒")

def analyze_data_freshness_check():
    """分析数据新鲜度检查逻辑"""
    print("\n🔍 分析2：数据新鲜度检查逻辑诊断")
    print("=" * 50)
    
    # 检查unified_timestamp_processor.py中的逻辑
    timestamp_processor_file = project_root / "websocket" / "unified_timestamp_processor.py"
    
    if not timestamp_processor_file.exists():
        print("❌ 未找到unified_timestamp_processor.py文件")
        return
    
    with open(timestamp_processor_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键配置
    issues = []
    
    # 检查max_age_ms配置
    if "max_age_ms = 1000" in content:
        print("✅ 发现max_age_ms = 1000配置")
    else:
        issues.append("未找到max_age_ms = 1000配置")
    
    # 检查时间戳处理逻辑
    if "_extract_server_timestamp_for_monitoring" in content:
        print("✅ 发现_extract_server_timestamp_for_monitoring方法")
    else:
        issues.append("未找到_extract_server_timestamp_for_monitoring方法")
    
    # 检查数据新鲜度检查失败日志记录
    if "数据新鲜度检查失败，丢弃过期时间戳" in content:
        print("✅ 发现数据新鲜度检查失败日志记录")
    else:
        issues.append("未找到数据新鲜度检查失败日志记录")
    
    # 检查时间戳标准化
    if "_normalize_timestamp_format" in content:
        print("✅ 发现_normalize_timestamp_format方法")
    else:
        issues.append("未找到_normalize_timestamp_format方法")
    
    if issues:
        print("\n⚠️ 发现的问题：")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("\n✅ 数据新鲜度检查逻辑基本正常")

def analyze_blocking_detection():
    """分析阻塞检测函数"""
    print("\n🔍 分析3：阻塞检测函数诊断")
    print("=" * 50)
    
    # 检查enhanced_blocking_tracker.py
    blocking_tracker_file = project_root / "websocket" / "enhanced_blocking_tracker.py"
    
    if not blocking_tracker_file.exists():
        print("❌ 未找到enhanced_blocking_tracker.py文件")
        return
    
    with open(blocking_tracker_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否有重复的阻塞检测
    blocking_methods = []
    
    if "def detect_data_flow_blocking" in content:
        blocking_methods.append("detect_data_flow_blocking")
    
    if "def log_data_staleness_event" in content:
        blocking_methods.append("log_data_staleness_event")
    
    if "def _analyze_blocking_cause" in content:
        blocking_methods.append("_analyze_blocking_cause")
    
    print(f"发现阻塞检测方法: {len(blocking_methods)}个")
    for method in blocking_methods:
        print(f"  - {method}")
    
    # 检查阻塞事件创建阈值
    if "duration > 120:" in content:
        print("✅ 发现阻塞事件创建阈值: 120秒")
    elif "duration > 30:" in content:
        print("⚠️ 发现阻塞事件创建阈值: 30秒 (可能过于敏感)")
    else:
        print("❌ 未找到阻塞事件创建阈值")
    
    # 检查是否有其他文件也在做阻塞检测
    other_files_with_blocking = []
    
    # 检查ws_client.py
    ws_client_file = project_root / "websocket" / "ws_client.py"
    if ws_client_file.exists():
        with open(ws_client_file, 'r', encoding='utf-8') as f:
            ws_content = f.read()
        if "silent_duration" in ws_content or "data_flow_timeout" in ws_content:
            other_files_with_blocking.append("ws_client.py")
    
    # 检查各交易所WebSocket文件
    for exchange in ["gate_ws.py", "bybit_ws.py", "okx_ws.py"]:
        exchange_file = project_root / "websocket" / exchange
        if exchange_file.exists():
            with open(exchange_file, 'r', encoding='utf-8') as f:
                exchange_content = f.read()
            if "_monitor_data_flow" in exchange_content or "data_flow_timeout" in exchange_content:
                other_files_with_blocking.append(exchange)
    
    if other_files_with_blocking:
        print(f"\n⚠️ 发现其他文件也有阻塞检测逻辑: {other_files_with_blocking}")
        print("这可能导致重复检测和冲突")
    else:
        print("\n✅ 只有enhanced_blocking_tracker在做阻塞检测")

def analyze_message_queue_delay():
    """分析消息队列延迟"""
    print("\n🔍 分析4：消息队列延迟诊断")
    print("=" * 50)
    
    # 检查ws_client.py中的消息队列实现
    ws_client_file = project_root / "websocket" / "ws_client.py"
    
    if not ws_client_file.exists():
        print("❌ 未找到ws_client.py文件")
        return
    
    with open(ws_client_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查消息队列相关代码
    queue_features = []
    
    if "receive_timestamp_ms" in content:
        queue_features.append("消息接收时间戳标记")
    
    if "_message_queue" in content:
        queue_features.append("消息队列机制")
    
    if "queue_delay_ms" in content:
        queue_features.append("队列延迟计算")
    
    if "_unified_message_distributor" in content:
        queue_features.append("统一消息分发器")
    
    print(f"发现消息队列特性: {len(queue_features)}个")
    for feature in queue_features:
        print(f"  ✅ {feature}")
    
    # 检查是否有队列延迟阈值
    if "queue_delay_ms > 100" in content:
        print("✅ 发现队列延迟阈值: 100ms")
    else:
        print("⚠️ 未找到队列延迟阈值设置")

def generate_diagnosis_report():
    """生成诊断报告"""
    print("\n📋 诊断报告总结")
    print("=" * 50)
    
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    report = {
        "diagnosis_time": current_time,
        "problem_description": "数据新鲜度检查失败，数据堆积80+秒",
        "log_evidence": {
            "gate_io": {
                "timestamp_age_ms": 80862,
                "max_age_ms": 1000,
                "discarded_timestamp": 1754412617489
            },
            "okx": {
                "timestamp_age_ms": 86237,
                "max_age_ms": 1000,
                "discarded_timestamp": 1754412611601
            }
        },
        "analysis_results": {
            "timestamp_issue": "需要进一步分析时间戳是否来自过期的消息队列",
            "freshness_check": "逻辑基本正常，1000ms阈值合理",
            "blocking_detection": "需要检查是否有重复的阻塞检测函数",
            "message_queue": "需要检查消息队列是否有积压导致延迟"
        },
        "suspected_root_cause": "消息队列积压导致80+秒前的消息现在才被处理",
        "next_steps": [
            "检查WebSocket消息队列是否有积压",
            "分析消息分发器是否正常工作",
            "确认enhanced_blocking_tracker是否是唯一的阻塞检测器",
            "检查是否有其他因素导致消息处理延迟"
        ]
    }
    
    # 保存诊断报告
    report_file = project_root / "diagnostic_scripts" / f"data_freshness_diagnosis_{int(time.time())}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"📄 诊断报告已保存到: {report_file}")
    
    return report

def main():
    """主函数"""
    print("🚨 数据新鲜度检查失败精确诊断")
    print("=" * 60)
    print("问题：Gate.io和OKX数据堆积80+秒才被处理")
    print("目标：精准定位根本原因")
    print()
    
    # 执行各项分析
    analyze_timestamp_issue()
    analyze_data_freshness_check()
    analyze_blocking_detection()
    analyze_message_queue_delay()
    
    # 生成诊断报告
    report = generate_diagnosis_report()
    
    print("\n🎯 初步结论：")
    print("根据分析，最可能的原因是消息队列积压，导致80+秒前的消息现在才被处理。")
    print("这不是时间戳计算错误，而是消息处理延迟问题。")
    print("\n建议下一步创建更详细的消息队列诊断脚本。")

if __name__ == "__main__":
    main()
