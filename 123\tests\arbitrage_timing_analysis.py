#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🕐 套利系统时序分析器 - 精确测量两个关键阶段的用时
"""

import asyncio
import time
import sys
import os
from typing import Dict, Any, Optional

# 设置项目路径
project_root = '/root/myproject/123/77 修复了开仓差价变化错误的问题/123'
sys.path.insert(0, project_root)
os.chdir(project_root)

class ArbitrageTimingAnalyzer:
    """套利时序分析器"""
    
    def __init__(self):
        self.timing_data = {
            "phase1": {
                "name": "发现差价 -> 锁定差价",
                "start": 0,
                "end": 0,
                "duration_ms": 0
            },
            "phase2": {
                "name": "等待趋同 -> 平仓",
                "start": 0,
                "end": 0,
                "duration_ms": 0
            }
        }
        self.selected_scenario = None  # 用于报告生成
    
    async def analyze_phase1_timing(self):
        """阶段1: 发现差价到锁定差价的时序分析"""
        
        print("🕐 阶段1时序分析：发现差价 -> 锁定差价")
        print("=" * 60)
        
        phase1_start = time.time()
        self.timing_data["phase1"]["start"] = phase1_start
        
        try:
            # === 子阶段1.1: 发现差价 ===
            step1_start = time.time()
            print("🔍 子阶段1.1: 模拟发现差价...")
            
            # 模拟OpportunityScanner发现价差的过程
            from core.opportunity_scanner import ArbitrageOpportunity
            
            # 创建模拟的套利机会
            mock_opportunity = ArbitrageOpportunity(
                symbol="ADA-USDT",
                base_amount=100.0,
                exchange1_name="gate",
                exchange1_market="spot",
                exchange1_price=0.3450,
                exchange1_value=34.50,
                exchange2_name="bybit",
                exchange2_market="futures",
                exchange2_price=0.3485,
                exchange2_value=34.85,
                spread_value=0.35,
                spread_percent=0.0101,  # 1.01%
                profit_estimate=0.30,
                buy_exchange="gate",
                buy_market="spot",
                sell_exchange="bybit", 
                sell_market="futures",
                timestamp=int(time.time() * 1000)
            )
            
            step1_time = (time.time() - step1_start) * 1000
            print(f"✅ 差价发现完成: {step1_time:.1f}ms")
            print(f"   发现价差: {mock_opportunity.spread_percent*100:.2f}%")
            
            # === 子阶段1.2: ExecutionEngine准备 ===
            step2_start = time.time()
            print("⚡ 子阶段1.2: ExecutionEngine准备...")
            
            # 使用预热连接创建ExecutionEngine
            from core.exchange_connection_preheater import get_connection_preheater
            from core.execution_engine import ExecutionEngine
            
            preheater = get_connection_preheater()
            
            # 确保预热完成
            if not preheater.is_preheating_complete():
                print("🔄 启动预热...")
                await preheater.start_preheating_all_exchanges()
            
            # 🔥 关键修复：创建ExecutionEngine后不重新设置connection_preheater
            # ExecutionEngine会在初始化时自动获取预热器，或在_initialize_exchanges中重新获取
            execution_engine = ExecutionEngine()
            await execution_engine._initialize_exchanges()
            
            step2_time = (time.time() - step2_start) * 1000
            print(f"✅ ExecutionEngine就绪: {step2_time:.1f}ms")
            print(f"   可用交易所: {len(execution_engine.exchanges)}")
            
            # === 子阶段1.3: 订单准备和计算 ===
            step3_start = time.time()
            print("📊 子阶段1.3: 订单准备和计算...")
            
            # 模拟订单计算过程
            from core.unified_opening_manager import get_opening_manager
            opening_manager = get_opening_manager()
            
            # 模拟准备开仓参数（不实际下单）
            mock_spot_params = {
                "symbol": "ADA-USDT",
                "side": "buy",
                "quantity": 100.0,
                "price": 0.3450,
                "market_type": "spot"
            }
            
            mock_futures_params = {
                "symbol": "ADA-USDT", 
                "side": "sell",
                "quantity": 100.0,
                "price": 0.3485,
                "market_type": "futures"
            }
            
            step3_time = (time.time() - step3_start) * 1000
            print(f"✅ 订单参数准备完成: {step3_time:.1f}ms")
            
            # === 子阶段1.4: 模拟锁定差价（并行下单） ===
            step4_start = time.time()
            print("🎯 子阶段1.4: 模拟锁定差价（并行下单）...")
            
            # 模拟并行下单过程（不实际执行）
            async def mock_spot_order():
                await asyncio.sleep(0.015)  # 模拟15ms网络延迟
                return {"success": True, "order_id": "spot_123", "filled": 100.0}
            
            async def mock_futures_order():
                await asyncio.sleep(0.020)  # 模拟20ms网络延迟  
                return {"success": True, "order_id": "futures_456", "filled": 100.0}
            
            # 并行执行
            spot_result, futures_result = await asyncio.gather(
                mock_spot_order(),
                mock_futures_order()
            )
            
            step4_time = (time.time() - step4_start) * 1000
            print(f"✅ 差价锁定完成: {step4_time:.1f}ms")
            print(f"   现货订单: {'成功' if spot_result['success'] else '失败'}")
            print(f"   期货订单: {'成功' if futures_result['success'] else '失败'}")
            
            # === 阶段1总结 ===
            phase1_end = time.time()
            self.timing_data["phase1"]["end"] = phase1_end
            phase1_total = (phase1_end - phase1_start) * 1000
            self.timing_data["phase1"]["duration_ms"] = phase1_total
            
            print(f"\n📊 阶段1总结:")
            print(f"   发现差价: {step1_time:.1f}ms")
            print(f"   引擎准备: {step2_time:.1f}ms")
            print(f"   订单准备: {step3_time:.1f}ms")
            print(f"   锁定差价: {step4_time:.1f}ms")
            print(f"   🎯 阶段1总计: {phase1_total:.1f}ms")
            
            return phase1_total
            
        except Exception as e:
            print(f"❌ 阶段1分析异常: {e}")
            import traceback
            traceback.print_exc()
            return -1
    
    async def analyze_phase2_timing(self):
        """阶段2: 等待趋同到平仓的时序分析"""
        
        print("\n🕐 阶段2时序分析：等待趋同 -> 平仓")
        print("=" * 60)
        
        phase2_start = time.time()
        self.timing_data["phase2"]["start"] = phase2_start
        
        try:
            # === 子阶段2.1: 趋同监控初始化 ===
            step1_start = time.time()
            print("📊 子阶段2.1: 趋同监控初始化...")
            
            from core.convergence_monitor import init_convergence_monitor
            
            # 模拟配置
            mock_config = {
                "MAX_CONVERGENCE_WAIT": "1800",  # 30分钟
                "ENABLE_DYNAMIC_THRESHOLD": "true"
            }
            
            # 初始化趋同监控器
            convergence_monitor = init_convergence_monitor(mock_config)
            
            step1_time = (time.time() - step1_start) * 1000
            print(f"✅ 趋同监控器就绪: {step1_time:.1f}ms")
            
            # === 子阶段2.2: 模拟价格趋同过程 ===
            step2_start = time.time()
            print("⏳ 子阶段2.2: 模拟价格趋同过程...")
            
            # 模拟价格趋同的时间（实际中这是等待时间，这里压缩到几秒）
            convergence_scenarios = [
                {"name": "快速趋同", "wait_time": 0.1, "probability": 0.2},     # 100ms - 20%概率
                {"name": "正常趋同", "wait_time": 0.5, "probability": 0.5},     # 500ms - 50%概率  
                {"name": "缓慢趋同", "wait_time": 2.0, "probability": 0.3},     # 2秒 - 30%概率
            ]
            
            # 选择正常趋同场景进行测试
            selected_scenario = convergence_scenarios[1]  # 正常趋同
            self.selected_scenario = selected_scenario  # 保存到实例变量
            print(f"   模拟场景: {selected_scenario['name']}")
            
            await asyncio.sleep(selected_scenario['wait_time'])
            
            step2_time = (time.time() - step2_start) * 1000
            print(f"✅ 价格趋同完成: {step2_time:.1f}ms (模拟{selected_scenario['name']})")
            
            # === 子阶段2.3: 趋同检测和平仓决策 ===
            step3_start = time.time()
            print("🎯 子阶段2.3: 趋同检测和平仓决策...")
            
            # 模拟趋同检测逻辑
            current_spread = 0.0005  # 0.05% - 已趋同
            threshold = 0.002        # 0.2% - 平仓阈值
            
            convergence_detected = abs(current_spread) < threshold
            
            step3_time = (time.time() - step3_start) * 1000
            print(f"✅ 趋同检测完成: {step3_time:.1f}ms")
            print(f"   当前价差: {current_spread*100:.3f}%")
            print(f"   平仓阈值: {threshold*100:.3f}%")
            print(f"   趋同状态: {'已趋同' if convergence_detected else '未趋同'}")
            
            # === 子阶段2.4: 执行平仓操作 ===
            step4_start = time.time()
            print("💼 子阶段2.4: 执行平仓操作...")
            
            if convergence_detected:
                # 模拟平仓订单（反向操作）
                async def mock_close_spot():
                    await asyncio.sleep(0.018)  # 模拟18ms
                    return {"success": True, "order_id": "close_spot_789", "filled": 100.0}
                
                async def mock_close_futures():
                    await asyncio.sleep(0.022)  # 模拟22ms
                    return {"success": True, "order_id": "close_futures_101", "filled": 100.0}
                
                # 并行平仓
                close_spot_result, close_futures_result = await asyncio.gather(
                    mock_close_spot(),
                    mock_close_futures()
                )
                
                step4_time = (time.time() - step4_start) * 1000
                print(f"✅ 平仓操作完成: {step4_time:.1f}ms")
                print(f"   平仓现货: {'成功' if close_spot_result['success'] else '失败'}")
                print(f"   平仓期货: {'成功' if close_futures_result['success'] else '失败'}")
            else:
                step4_time = 0
                print("⚠️ 未满足平仓条件，继续等待")
            
            # === 阶段2总结 ===
            phase2_end = time.time()
            self.timing_data["phase2"]["end"] = phase2_end
            phase2_total = (phase2_end - phase2_start) * 1000
            self.timing_data["phase2"]["duration_ms"] = phase2_total
            
            print(f"\n📊 阶段2总结:")
            print(f"   监控初始化: {step1_time:.1f}ms")
            print(f"   价格趋同: {step2_time:.1f}ms")
            print(f"   趋同检测: {step3_time:.1f}ms")
            print(f"   平仓操作: {step4_time:.1f}ms")
            print(f"   🎯 阶段2总计: {phase2_total:.1f}ms")
            
            return phase2_total
            
        except Exception as e:
            print(f"❌ 阶段2分析异常: {e}")
            import traceback
            traceback.print_exc()
            return -1
    
    def generate_timing_report(self):
        """生成完整的时序分析报告"""
        
        print("\n" + "=" * 80)
        print("🕐 套利系统完整时序分析报告")
        print("=" * 80)
        
        # 阶段1分析
        phase1_ms = self.timing_data["phase1"]["duration_ms"]
        print(f"\n🚀 阶段1: {self.timing_data['phase1']['name']}")
        print(f"   ⏱️ 用时: {phase1_ms:.1f}ms ({phase1_ms/1000:.3f}秒)")
        
        if phase1_ms > 0:
            if phase1_ms < 100:
                phase1_level = "🔥 优秀"
            elif phase1_ms < 500:
                phase1_level = "✅ 良好"
            elif phase1_ms < 1000:
                phase1_level = "⚠️ 一般"
            else:
                phase1_level = "❌ 需优化"
            
            print(f"   📈 性能评级: {phase1_level}")
            print(f"   🎯 1秒目标: {'✅ 达成' if phase1_ms < 1000 else '❌ 未达成'}")
        
        # 阶段2分析
        phase2_ms = self.timing_data["phase2"]["duration_ms"]
        print(f"\n⏳ 阶段2: {self.timing_data['phase2']['name']}")
        print(f"   ⏱️ 用时: {phase2_ms:.1f}ms ({phase2_ms/1000:.3f}秒)")
        
        if phase2_ms > 0:
            scenario_name = self.selected_scenario['name'] if self.selected_scenario else "未知场景"
            print(f"   💡 说明: 包含{scenario_name}模拟")
            print(f"   🔄 实际场景: 趋同等待时间通常为几分钟到30分钟")
            
            # 实际场景时间估算
            actual_convergence_times = {
                "快速市场": "1-5分钟",
                "正常市场": "5-15分钟", 
                "缓慢市场": "15-30分钟",
                "极端情况": "30分钟+"
            }
            
            print(f"   📊 实际趋同时间预估:")
            for scenario, duration in actual_convergence_times.items():
                print(f"      {scenario}: {duration}")
        
        # 总体分析
        if phase1_ms > 0 and phase2_ms > 0:
            total_execution_ms = phase1_ms + phase2_ms
            print(f"\n📊 总体分析:")
            print(f"   🎯 核心执行时间: {phase1_ms:.1f}ms (阶段1)")
            print(f"   ⏳ 模拟总时间: {total_execution_ms:.1f}ms")
            print(f"   🏆 关键性能指标: 阶段1是核心，决定能否抓住套利机会")
            
            print(f"\n🎯 关键结论:")
            print(f"   ✅ 阶段1 (发现->锁定): {phase1_ms:.1f}ms - 这是系统核心竞争力")
            print(f"   ⏳ 阶段2 (等待->平仓): 主要受市场趋同速度影响，系统响应时间<50ms")
            
            if phase1_ms < 100:
                print(f"   🎉 系统已达到高频交易级别的响应速度！")
            elif phase1_ms < 1000:
                print(f"   ✅ 系统响应速度良好，具备市场竞争力")
            else:
                print(f"   ⚠️ 系统响应速度需要进一步优化")

async def main():
    """主函数"""
    print("🚀 启动套利系统时序分析器")
    print("=" * 80)
    
    analyzer = ArbitrageTimingAnalyzer()
    
    # 分析阶段1
    phase1_result = await analyzer.analyze_phase1_timing()
    
    if phase1_result > 0:
        # 分析阶段2
        phase2_result = await analyzer.analyze_phase2_timing()
        
        # 生成完整报告
        analyzer.generate_timing_report()
        
        return True
    else:
        print("❌ 阶段1分析失败，停止测试")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)