#!/usr/bin/env python3
"""
🏛️ 机构级别复杂系统级联测试：模块间交互逻辑验证
验证瞬时时间戳标记系统的系统协同一致性

测试覆盖：
1. 模块之间的交互逻辑
2. 状态联动
3. 多币种切换
4. 多交易所分支
5. 系统协同一致性
"""

import sys
import os
import time
import asyncio
import json
from typing import Dict, Any, List

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

class InstitutionalSystemCascadeTest:
    """机构级别复杂系统级联测试器"""
    
    def __init__(self):
        self.test_results = {
            "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_type": "复杂系统级联测试",
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": []
        }
    
    def run_test(self, test_name: str, test_func, *args, **kwargs):
        """运行单个测试"""
        self.test_results["total_tests"] += 1
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = asyncio.run(test_func(*args, **kwargs))
            else:
                result = test_func(*args, **kwargs)
                
            if result:
                self.test_results["passed_tests"] += 1
                status = "PASS"
                print(f"✅ {test_name}: PASS")
            else:
                self.test_results["failed_tests"] += 1
                status = "FAIL"
                print(f"❌ {test_name}: FAIL")
                
            self.test_results["test_details"].append({
                "name": test_name,
                "status": status,
                "result": result
            })
            
        except Exception as e:
            self.test_results["failed_tests"] += 1
            status = "ERROR"
            print(f"💥 {test_name}: ERROR - {e}")
            
            self.test_results["test_details"].append({
                "name": test_name,
                "status": status,
                "error": str(e)
            })
    
    def test_cross_exchange_timestamp_consistency(self) -> bool:
        """测试跨交易所时间戳一致性"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            current_time = int(time.time() * 1000)
            test_data = {"receive_timestamp_ms": current_time}
            
            # 获取三个交易所的时间戳
            timestamps = {}
            for exchange in ["gate", "okx", "bybit"]:
                processor = get_timestamp_processor(exchange)
                timestamps[exchange] = processor.get_synced_timestamp(test_data)
            
            # 验证时间戳一致性（考虑网络延迟补偿差异）
            max_diff = 0
            for i, (ex1, ts1) in enumerate(timestamps.items()):
                for j, (ex2, ts2) in enumerate(timestamps.items()):
                    if i < j:
                        diff = abs(ts1 - ts2)
                        max_diff = max(max_diff, diff)
            
            # 最大差异不应超过50ms（网络延迟补偿差异）
            if max_diff > 50:
                print(f"跨交易所时间戳差异过大: {max_diff}ms")
                return False
            
            print(f"跨交易所时间戳一致性验证通过: 最大差异{max_diff}ms")
            return True
            
        except Exception as e:
            print(f"跨交易所时间戳一致性测试异常: {e}")
            return False
    
    def test_multi_symbol_switching(self) -> bool:
        """测试多币种切换"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT"]
            current_time = int(time.time() * 1000)
            
            for exchange in ["gate", "okx", "bybit"]:
                processor = get_timestamp_processor(exchange)
                
                # 模拟不同币种的时间戳处理
                for symbol in symbols:
                    test_data = {
                        "receive_timestamp_ms": current_time,
                        "symbol": symbol,
                        "exchange": exchange
                    }
                    
                    result = processor.get_synced_timestamp(test_data)
                    
                    # 验证结果有效性
                    if not isinstance(result, int) or result <= 0:
                        print(f"{exchange}-{symbol}时间戳处理失败")
                        return False
                    
                    # 验证时间戳在合理范围内
                    if abs(result - current_time) > 200:
                        print(f"{exchange}-{symbol}时间戳偏差过大: {abs(result - current_time)}ms")
                        return False
            
            print("多币种切换测试通过")
            return True
            
        except Exception as e:
            print(f"多币种切换测试异常: {e}")
            return False
    
    async def test_websocket_message_flow_integration(self) -> bool:
        """测试WebSocket消息流集成"""
        try:
            from websocket.ws_client import WebSocketClient
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            # 模拟WebSocket消息处理流程
            current_time = int(time.time() * 1000)
            
            # 创建模拟消息
            mock_message = {
                "original_message": '{"symbol": "BTCUSDT", "data": "test"}',
                "receive_timestamp_ms": current_time
            }
            
            # 测试消息处理流程
            for exchange in ["gate", "okx", "bybit"]:
                processor = get_timestamp_processor(exchange)
                
                # 模拟消息解析后的数据
                parsed_data = {
                    "symbol": "BTCUSDT",
                    "data": "test",
                    "receive_timestamp_ms": current_time
                }
                
                # 验证时间戳处理
                result = processor.get_synced_timestamp(parsed_data)
                
                if not isinstance(result, int) or abs(result - current_time) > 200:
                    print(f"{exchange}消息流集成测试失败")
                    return False
            
            print("WebSocket消息流集成测试通过")
            return True
            
        except Exception as e:
            print(f"WebSocket消息流集成测试异常: {e}")
            return False
    
    def test_state_synchronization(self) -> bool:
        """测试状态同步"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            # 测试时间戳处理器状态同步
            processors = {}
            for exchange in ["gate", "okx", "bybit"]:
                processors[exchange] = get_timestamp_processor(exchange)
            
            # 验证处理器状态
            for exchange, processor in processors.items():
                # 检查基本状态
                if not hasattr(processor, 'exchange_name'):
                    print(f"{exchange}处理器缺少exchange_name属性")
                    return False
                
                if processor.exchange_name.lower() != exchange:
                    print(f"{exchange}处理器exchange_name不匹配")
                    return False
                
                # 检查网络延迟补偿状态
                delay = processor._get_network_delay_compensation()
                if not isinstance(delay, int) or delay <= 0:
                    print(f"{exchange}网络延迟补偿状态异常")
                    return False
            
            print("状态同步测试通过")
            return True
            
        except Exception as e:
            print(f"状态同步测试异常: {e}")
            return False
    
    def test_concurrent_processing(self) -> bool:
        """测试并发处理"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            import concurrent.futures
            import threading
            
            def process_timestamp(exchange, symbol, iteration):
                processor = get_timestamp_processor(exchange)
                current_time = int(time.time() * 1000)
                test_data = {
                    "receive_timestamp_ms": current_time,
                    "symbol": symbol,
                    "iteration": iteration
                }
                return processor.get_synced_timestamp(test_data)
            
            # 并发测试参数
            exchanges = ["gate", "okx", "bybit"]
            symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
            iterations = 50
            
            # 创建并发任务
            tasks = []
            for exchange in exchanges:
                for symbol in symbols:
                    for i in range(iterations):
                        tasks.append((exchange, symbol, i))
            
            # 执行并发测试
            with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
                futures = [executor.submit(process_timestamp, *task) for task in tasks]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            # 验证结果
            current_time = int(time.time() * 1000)
            for result in results:
                if not isinstance(result, int) or result <= 0:
                    print("并发处理结果无效")
                    return False
                
                if abs(result - current_time) > 1000:  # 1秒容差
                    print(f"并发处理时间戳偏差过大: {abs(result - current_time)}ms")
                    return False
            
            print(f"并发处理测试通过: {len(results)}个任务")
            return True
            
        except Exception as e:
            print(f"并发处理测试异常: {e}")
            return False
    
    def test_error_propagation(self) -> bool:
        """测试错误传播"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            # 测试错误情况下的系统行为
            for exchange in ["gate", "okx", "bybit"]:
                processor = get_timestamp_processor(exchange)
                
                # 测试1：无效数据不应导致系统崩溃
                invalid_data_sets = [
                    None,
                    {},
                    {"invalid": "data"},
                    {"receive_timestamp_ms": "invalid"},
                    {"receive_timestamp_ms": -1},
                    {"receive_timestamp_ms": float('inf')},
                ]
                
                for invalid_data in invalid_data_sets:
                    try:
                        result = processor.get_synced_timestamp(invalid_data)
                        # 应该返回有效的时间戳，不应该抛出异常
                        if not isinstance(result, int) or result <= 0:
                            print(f"{exchange}错误处理失败: 无效数据{invalid_data}")
                            return False
                    except Exception as e:
                        print(f"{exchange}错误传播测试失败: {e}")
                        return False
            
            print("错误传播测试通过")
            return True
            
        except Exception as e:
            print(f"错误传播测试异常: {e}")
            return False
    
    def test_resource_cleanup(self) -> bool:
        """测试资源清理"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            import gc
            import weakref
            
            # 创建处理器引用
            processors = []
            weak_refs = []
            
            for i in range(100):
                for exchange in ["gate", "okx", "bybit"]:
                    processor = get_timestamp_processor(exchange)
                    processors.append(processor)
                    weak_refs.append(weakref.ref(processor))
            
            # 清理强引用
            processors.clear()
            
            # 强制垃圾回收
            gc.collect()
            
            # 检查弱引用（注意：由于全局缓存，处理器实例不会被回收）
            # 这里主要测试没有内存泄漏
            alive_count = sum(1 for ref in weak_refs if ref() is not None)
            
            # 由于使用全局缓存，处理器实例应该仍然存在
            if alive_count < 3:  # 至少应该有3个（每个交易所一个）
                print(f"资源清理异常: 存活实例{alive_count}个")
                return False
            
            print(f"资源清理测试通过: 存活实例{alive_count}个（符合预期）")
            return True
            
        except Exception as e:
            print(f"资源清理测试异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有复杂系统级联测试"""
        print("🏛️ 开始机构级别复杂系统级联测试")
        print("="*60)
        
        # 运行所有测试
        self.run_test("跨交易所时间戳一致性", self.test_cross_exchange_timestamp_consistency)
        self.run_test("多币种切换", self.test_multi_symbol_switching)
        self.run_test("WebSocket消息流集成", self.test_websocket_message_flow_integration)
        self.run_test("状态同步", self.test_state_synchronization)
        self.run_test("并发处理", self.test_concurrent_processing)
        self.run_test("错误传播", self.test_error_propagation)
        self.run_test("资源清理", self.test_resource_cleanup)
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        success_rate = (self.test_results["passed_tests"] / self.test_results["total_tests"]) * 100
        
        print("\n" + "="*60)
        print("🏛️ 复杂系统级联测试报告")
        print("="*60)
        print(f"总测试数: {self.test_results['total_tests']}")
        print(f"通过测试: {self.test_results['passed_tests']}")
        print(f"失败测试: {self.test_results['failed_tests']}")
        print(f"成功率: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("🎉 复杂系统级联测试100%通过！系统协同一致性验证成功！")
        else:
            print("⚠️ 存在测试失败，需要进一步修复")
            
        # 保存测试结果
        timestamp = int(time.time())
        filename = f"institutional_system_cascade_test_{timestamp}.json"
        filepath = os.path.join("diagnostic_scripts", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 测试结果已保存到: {filepath}")
        
        return success_rate == 100

def main():
    """主函数"""
    tester = InstitutionalSystemCascadeTest()
    success = tester.run_all_tests()
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
