#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚨 终极验证测试 - 100%诚实的修复效果验证
不掩盖任何问题，完全暴露真实情况
"""

import asyncio
import time
import sys
import os

# 设置项目路径
project_root = '/root/myproject/123/77 修复了开仓差价变化错误的问题/123'
sys.path.insert(0, project_root)
os.chdir(project_root)

class UltimateVerificationTest:
    """终极验证测试"""
    
    def __init__(self):
        self.test_results = []
        self.errors = []
    
    def log_result(self, test_name: str, success: bool, details: str):
        """记录测试结果"""
        result = {
            'test': test_name,
            'success': success,
            'details': details,
            'timestamp': time.time()
        }
        self.test_results.append(result)
        
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}: {details}")
    
    async def test_1_preheater_basic_function(self):
        """测试1: 预热器基础功能"""
        print("\n🧪 测试1: 预热器基础功能")
        print("-" * 40)
        
        try:
            from core.exchange_connection_preheater import get_connection_preheater
            
            # 获取预热器实例
            preheater = get_connection_preheater()
            if not preheater:
                self.log_result("预热器获取", False, "get_connection_preheater返回None")
                return False
            
            self.log_result("预热器获取", True, f"类型: {type(preheater).__name__}")
            
            # 检查必要方法
            required_methods = [
                'start_preheating_all_exchanges',
                'is_preheating_complete', 
                'get_all_preheated_exchanges'
            ]
            
            for method in required_methods:
                if hasattr(preheater, method):
                    self.log_result(f"方法检查-{method}", True, "方法存在")
                else:
                    self.log_result(f"方法检查-{method}", False, "方法不存在")
                    return False
            
            return True
            
        except Exception as e:
            self.log_result("预热器基础功能", False, f"异常: {e}")
            return False
    
    async def test_2_preheater_execution(self):
        """测试2: 预热器执行测试"""
        print("\n🧪 测试2: 预热器执行测试")
        print("-" * 40)
        
        try:
            from core.exchange_connection_preheater import get_connection_preheater
            preheater = get_connection_preheater()
            
            # 检查初始状态
            initial_complete = preheater.is_preheating_complete()
            self.log_result("初始预热状态", True, f"预热完成: {initial_complete}")
            
            # 执行预热
            print("开始执行预热...")
            start_time = time.time()
            
            result = await preheater.start_preheating_all_exchanges()
            
            preheat_time = (time.time() - start_time) * 1000
            
            if result:
                self.log_result("预热执行", True, f"成功，耗时: {preheat_time:.1f}ms")
            else:
                self.log_result("预热执行", False, f"失败，耗时: {preheat_time:.1f}ms")
                return False
            
            # 检查预热后状态
            final_complete = preheater.is_preheating_complete()
            self.log_result("预热后状态", final_complete, f"预热完成: {final_complete}")
            
            # 获取预热连接
            exchanges = preheater.get_all_preheated_exchanges()
            if exchanges and len(exchanges) > 0:
                self.log_result("预热连接获取", True, f"获得{len(exchanges)}个交易所: {list(exchanges.keys())}")
                return True
            else:
                self.log_result("预热连接获取", False, "无法获取预热连接")
                return False
                
        except Exception as e:
            self.log_result("预热器执行", False, f"异常: {e}")
            return False
    
    async def test_3_execution_engine_with_preheater(self):
        """测试3: ExecutionEngine使用预热器"""
        print("\n🧪 测试3: ExecutionEngine使用预热器")
        print("-" * 40)
        
        try:
            # 确保预热完成
            from core.exchange_connection_preheater import get_connection_preheater
            preheater = get_connection_preheater()
            
            if not preheater.is_preheating_complete():
                print("预热未完成，先执行预热...")
                await preheater.start_preheating_all_exchanges()
            
            # 创建ExecutionEngine
            from core.execution_engine import ExecutionEngine
            
            print("创建ExecutionEngine...")
            create_start = time.time()
            execution_engine = ExecutionEngine()
            create_time = (time.time() - create_start) * 1000
            
            self.log_result("ExecutionEngine创建", True, f"耗时: {create_time:.1f}ms")
            
            # 测试交易所初始化
            print("执行交易所初始化...")
            init_start = time.time()
            
            await execution_engine._initialize_exchanges()
            
            init_time = (time.time() - init_start) * 1000
            
            # 检查结果
            if execution_engine.exchanges and len(execution_engine.exchanges) > 0:
                self.log_result("交易所初始化", True, 
                    f"耗时: {init_time:.1f}ms, 获得{len(execution_engine.exchanges)}个交易所")
                
                # 关键测试：初始化时间是否<100ms
                if init_time < 100:
                    self.log_result("性能测试", True, f"初始化时间{init_time:.1f}ms < 100ms")
                    return True
                else:
                    self.log_result("性能测试", False, f"初始化时间{init_time:.1f}ms ≥ 100ms")
                    return False
            else:
                self.log_result("交易所初始化", False, f"耗时: {init_time:.1f}ms, 无交易所")
                return False
                
        except Exception as e:
            self.log_result("ExecutionEngine测试", False, f"异常: {e}")
            return False
    
    async def test_4_clean_slate_test(self):
        """测试4: 全新状态测试 - 模拟系统首次启动"""
        print("\n🧪 测试4: 全新状态测试")
        print("-" * 40)
        
        try:
            # 清理可能的缓存状态
            print("清理全局状态...")
            
            # 重新导入模块，确保全新状态
            import importlib
            
            # 清理预热器状态
            if 'core.exchange_connection_preheater' in sys.modules:
                preheater_module = sys.modules['core.exchange_connection_preheater']
                if hasattr(preheater_module, '_connection_preheater'):
                    preheater_module._connection_preheater = None
            
            # 重新导入
            from core.exchange_connection_preheater import get_connection_preheater
            from core.execution_engine import ExecutionEngine
            
            # 全新的预热过程
            print("开始全新预热过程...")
            fresh_preheater = get_connection_preheater()
            
            # 记录整个流程
            total_start = time.time()
            
            # 1. 预热
            preheat_start = time.time()
            preheat_result = await fresh_preheater.start_preheating_all_exchanges()
            preheat_time = (time.time() - preheat_start) * 1000
            
            if not preheat_result:
                self.log_result("全新预热", False, f"预热失败: {preheat_time:.1f}ms")
                return False
            
            self.log_result("全新预热", True, f"预热成功: {preheat_time:.1f}ms")
            
            # 2. ExecutionEngine使用预热结果
            execution_start = time.time()
            fresh_engine = ExecutionEngine()
            await fresh_engine._initialize_exchanges()
            execution_time = (time.time() - execution_start) * 1000
            
            total_time = (time.time() - total_start) * 1000
            
            # 分析结果
            success = (
                execution_time < 100 and  # 执行时间<100ms
                len(fresh_engine.exchanges) >= 2 and  # 至少2个交易所
                preheat_result  # 预热成功
            )
            
            details = f"预热{preheat_time:.1f}ms + 执行{execution_time:.1f}ms = 总计{total_time:.1f}ms"
            
            if success:
                self.log_result("全新状态测试", True, details)
                self.log_result("1秒目标", total_time < 1000, f"总时间: {total_time:.1f}ms")
                return True
            else:
                self.log_result("全新状态测试", False, details)
                return False
                
        except Exception as e:
            self.log_result("全新状态测试", False, f"异常: {e}")
            return False
    
    async def test_5_error_detection(self):
        """测试5: 错误检测 - 查找可能的问题"""
        print("\n🧪 测试5: 错误检测")
        print("-" * 40)
        
        errors_found = []
        
        try:
            # 检查单例模式
            from core.exchange_connection_preheater import get_connection_preheater
            
            preheater1 = get_connection_preheater()
            preheater2 = get_connection_preheater()
            
            if preheater1 is preheater2:
                self.log_result("单例模式", True, "同一实例")
            else:
                self.log_result("单例模式", False, "不同实例 - 可能的问题")
                errors_found.append("单例模式失效")
            
            # 检查状态持久性
            if preheater1.is_preheating_complete():
                exchanges_1 = preheater1.get_all_preheated_exchanges()
                exchanges_2 = preheater2.get_all_preheated_exchanges()
                
                if exchanges_1 is exchanges_2 or (exchanges_1 and exchanges_2 and len(exchanges_1) == len(exchanges_2)):
                    self.log_result("状态持久性", True, "状态一致")
                else:
                    self.log_result("状态持久性", False, "状态不一致 - 可能的问题")
                    errors_found.append("状态持久性问题")
            
            # 检查内存泄漏迹象
            import gc
            gc.collect()
            
            # 检查ExecutionEngine创建是否会重复初始化
            from core.execution_engine import ExecutionEngine
            
            engine1 = ExecutionEngine()
            engine2 = ExecutionEngine()
            
            # 检查它们的preheater是否相同
            if hasattr(engine1, 'connection_preheater') and hasattr(engine2, 'connection_preheater'):
                if engine1.connection_preheater is engine2.connection_preheater:
                    self.log_result("ExecutionEngine单例", True, "共享预热器")
                else:
                    self.log_result("ExecutionEngine单例", False, "不同预热器实例")
                    errors_found.append("ExecutionEngine单例问题")
            
            return len(errors_found) == 0
            
        except Exception as e:
            self.log_result("错误检测", False, f"异常: {e}")
            return False
    
    def generate_final_report(self):
        """生成最终报告"""
        print("\n" + "=" * 80)
        print("🚨 终极验证报告 - 100%诚实结果")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        
        print(f"\n📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过数: {passed_tests}")
        print(f"   失败数: {total_tests - passed_tests}")
        print(f"   成功率: {(passed_tests/total_tests*100):.1f}%")
        
        print(f"\n📋 详细结果:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['test']}: {result['details']}")
        
        # 失败的测试
        failed_tests = [r for r in self.test_results if not r['success']]
        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for failed in failed_tests:
                print(f"   - {failed['test']}: {failed['details']}")
        
        # 最终结论
        print(f"\n🎯 最终结论:")
        if passed_tests == total_tests:
            print("✅ 所有测试通过 - 修复功能100%实现且无错误")
            return True
        elif passed_tests >= total_tests * 0.8:
            print("⚠️ 大部分测试通过 - 存在部分问题需要修复")
            return False
        else:
            print("❌ 多数测试失败 - 修复存在严重问题")
            return False

async def main():
    """主函数"""
    print("🚨 启动终极验证测试")
    print("⚠️ 此测试将100%诚实地暴露所有问题")
    print("=" * 80)
    
    test_suite = UltimateVerificationTest()
    
    # 执行所有测试
    tests = [
        test_suite.test_1_preheater_basic_function(),
        test_suite.test_2_preheater_execution(),
        test_suite.test_3_execution_engine_with_preheater(),
        test_suite.test_4_clean_slate_test(),
        test_suite.test_5_error_detection()
    ]
    
    for test in tests:
        try:
            await test
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
            import traceback
            traceback.print_exc()
    
    # 生成最终报告
    success = test_suite.generate_final_report()
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 验证结论: 修复功能100%实现且无错误")
    else:
        print("🚨 验证结论: 存在问题，需要进一步修复")
    print("=" * 80)
    
    sys.exit(0 if success else 1)