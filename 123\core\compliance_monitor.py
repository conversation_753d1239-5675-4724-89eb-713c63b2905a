#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🔥 合规性验证监控器
确保所有预热操作符合交易所API规范和系统一致性要求
"""

import asyncio
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from utils.logger import get_logger

logger = get_logger(__name__)


class ComplianceValidator:
    """
    🔥 合规性验证器 - 确保预热操作符合所有规范
    """
    
    def __init__(self):
        # 交易所API限制配置 - 🔥 修复：基于官方文档的准确限制
        self.api_limits = {
            "gate": {
                # Gate.io官方文档：公共端点200次/10秒 = 20次/秒，私有端点保守设置15次/秒
                "max_requests_per_second": 15,  # 修复：从8提升到15
                "max_connections": 5,
                "max_websocket_connections": 3,  # 官方文档：最大300连接/IP，设置保守值
                "preheat_interval_ms": 100  # 修复：从200ms降到100ms提高效率
            },
            "bybit": {
                # Bybit官方文档：600次/5秒 = 120次/秒，设置保守值50次/秒
                "max_requests_per_second": 50,  # 修复：从10大幅提升到50
                "max_connections": 5,
                "max_websocket_connections": 3,  # 官方文档：500连接/5分钟，设置保守值
                "preheat_interval_ms": 50   # 修复：从150ms降到50ms
            },
            "okx": {
                # OKX官方文档：根据不同endpoint有不同限制，设置合理平衡值30次/秒
                "max_requests_per_second": 30,  # 修复：从20提升到30
                "max_connections": 5,
                "max_websocket_connections": 3,  # 官方文档：30连接/channel/sub-account
                "preheat_interval_ms": 80   # 修复：从100ms降到80ms
            }
        }
        
        # 一致性要求配置
        self.consistency_requirements = {
            "websocket_timeout_ms": 1000,
            "data_freshness_ms": 1000,
            "timestamp_tolerance_ms": 1000,
            "orderbook_timeout_ms": 1000,
            "execution_timeout_ms": 30000,
            "max_price_age_ms": 1000
        }
        
        self.validation_history = []
        
    async def validate_preheat_compliance(self, exchanges: Dict[str, Any], preheat_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔥 验证预热操作的合规性
        """
        compliance_report = {
            "overall_compliant": False,
            "api_compliance": {},
            "consistency_compliance": {},
            "performance_compliance": {},
            "violations": [],
            "recommendations": []
        }
        
        try:
            logger.info("🔍 开始合规性验证...")
            
            # 1. 验证API限制合规性
            api_compliance = await self._validate_api_compliance(exchanges, preheat_results)
            compliance_report["api_compliance"] = api_compliance
            
            # 2. 验证一致性要求
            consistency_compliance = await self._validate_consistency_compliance(exchanges)
            compliance_report["consistency_compliance"] = consistency_compliance
            
            # 3. 验证性能指标合规性
            performance_compliance = await self._validate_performance_compliance(preheat_results)
            compliance_report["performance_compliance"] = performance_compliance
            
            # 4. 生成违规报告和建议
            violations, recommendations = self._generate_compliance_feedback(
                api_compliance, consistency_compliance, performance_compliance
            )
            compliance_report["violations"] = violations
            compliance_report["recommendations"] = recommendations
            
            # 5. 综合合规性评估
            compliance_report["overall_compliant"] = (
                api_compliance.get("compliant", False) and
                consistency_compliance.get("compliant", False) and
                performance_compliance.get("compliant", False) and
                len(violations) == 0
            )
            
            # 记录验证历史
            self.validation_history.append({
                "timestamp": time.time(),
                "compliant": compliance_report["overall_compliant"],
                "violations_count": len(violations),
                "report": compliance_report
            })
            
            if compliance_report["overall_compliant"]:
                logger.info("✅ 合规性验证通过")
            else:
                logger.warning(f"⚠️ 合规性验证发现问题: {len(violations)}个违规")
                
        except Exception as e:
            logger.error(f"❌ 合规性验证失败: {e}")
            compliance_report["error"] = str(e)
        
        return compliance_report
    
    async def _validate_api_compliance(self, exchanges: Dict[str, Any], preheat_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔥 验证API限制合规性
        """
        api_compliance = {
            "compliant": True,
            "exchange_compliance": {},
            "violations": []
        }
        
        try:
            for exchange_name, exchange in exchanges.items():
                exchange_limits = self.api_limits.get(exchange_name.lower(), {})
                if not exchange_limits:
                    continue
                
                exchange_compliance = {
                    "compliant": True,
                    "rate_limit_ok": True,
                    "connection_limit_ok": True,
                    "preheat_interval_ok": True
                }
                
                # 检查连接数限制
                connection_stats = preheat_results.get("detailed_results", {}).get("stage1_connection", {}).get("stats", {})
                total_connections = connection_stats.get("total_connections", 0)
                
                if total_connections > exchange_limits.get("max_connections", 5):
                    exchange_compliance["connection_limit_ok"] = False
                    exchange_compliance["compliant"] = False
                    api_compliance["violations"].append(
                        f"{exchange_name}: 连接数超限 ({total_connections} > {exchange_limits['max_connections']})"
                    )
                
                # 检查WebSocket连接限制
                ws_stats = preheat_results.get("detailed_results", {}).get("stage2_infrastructure", {}).get("websocket_stats", {})
                ws_connections = ws_stats.get("connections_preheated", 0)
                
                if ws_connections > exchange_limits.get("max_websocket_connections", 3):
                    exchange_compliance["connection_limit_ok"] = False
                    exchange_compliance["compliant"] = False
                    api_compliance["violations"].append(
                        f"{exchange_name}: WebSocket连接数超限 ({ws_connections} > {exchange_limits['max_websocket_connections']})"
                    )
                
                # 检查预热间隔是否合理
                stage_times = [
                    preheat_results.get("detailed_results", {}).get("stage1_connection", {}).get("time_ms", 0),
                    preheat_results.get("detailed_results", {}).get("stage2_infrastructure", {}).get("time_ms", 0),
                    preheat_results.get("detailed_results", {}).get("stage3_execution", {}).get("time_ms", 0)
                ]
                
                min_required_interval = exchange_limits.get("preheat_interval_ms", 100)
                if any(stage_time > 0 and stage_time < min_required_interval for stage_time in stage_times):
                    exchange_compliance["preheat_interval_ok"] = False
                    exchange_compliance["compliant"] = False
                    api_compliance["violations"].append(
                        f"{exchange_name}: 预热间隔过短，可能触发API限制"
                    )
                
                api_compliance["exchange_compliance"][exchange_name] = exchange_compliance
                
                if not exchange_compliance["compliant"]:
                    api_compliance["compliant"] = False
                    
        except Exception as e:
            logger.warning(f"⚠️ API合规性验证失败: {e}")
            api_compliance["compliant"] = False
            api_compliance["error"] = str(e)
        
        return api_compliance
    
    async def _validate_consistency_compliance(self, exchanges: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔥 验证三交易所一致性要求
        """
        consistency_compliance = {
            "compliant": True,
            "timeout_consistency": True,
            "threshold_consistency": True,
            "logic_consistency": True,
            "violations": []
        }
        
        try:
            # 验证关键阈值一致性
            threshold_checks = [
                ("websocket_timeout", 1000, "WebSocket超时阈值"),
                ("data_freshness", 1000, "数据新鲜度阈值"),
                ("timestamp_tolerance", 1000, "时间戳容忍度"),
                ("orderbook_timeout", 1000, "订单簿超时")
            ]
            
            for threshold_name, expected_value, description in threshold_checks:
                if not await self._check_threshold_consistency(exchanges, threshold_name, expected_value):
                    consistency_compliance["threshold_consistency"] = False
                    consistency_compliance["compliant"] = False
                    consistency_compliance["violations"].append(
                        f"{description}不一致: 要求{expected_value}ms"
                    )
            
            # 验证处理逻辑一致性
            logic_consistency = await self._check_logic_consistency(exchanges)
            if not logic_consistency:
                consistency_compliance["logic_consistency"] = False
                consistency_compliance["compliant"] = False
                consistency_compliance["violations"].append("三交易所处理逻辑存在不一致")
            
        except Exception as e:
            logger.warning(f"⚠️ 一致性合规验证失败: {e}")
            consistency_compliance["compliant"] = False
            consistency_compliance["error"] = str(e)
        
        return consistency_compliance
    
    async def _check_threshold_consistency(self, exchanges: Dict[str, Any], threshold_name: str, expected_value: int) -> bool:
        """
        🔥 检查阈值一致性
        """
        try:
            # 检查环境变量配置
            import os
            env_mappings = {
                "websocket_timeout": "WEBSOCKET_TIMEOUT_MS",
                "data_freshness": "DATA_FRESHNESS_MS",
                "timestamp_tolerance": "TIMESTAMP_TOLERANCE",
                "orderbook_timeout": "ORDERBOOK_TIMEOUT_MS"
            }
            
            env_var = env_mappings.get(threshold_name)
            if env_var:
                env_value = os.getenv(env_var)
                if env_value and int(env_value) != expected_value:
                    logger.warning(f"⚠️ 环境变量{env_var}值({env_value})不符合要求({expected_value})")
                    return False
            
            # 检查代码中的硬编码阈值
            # 这里可以添加更详细的代码检查逻辑
            return True
            
        except Exception as e:
            logger.debug(f"阈值一致性检查失败: {threshold_name} - {e}")
            return False
    
    async def _check_logic_consistency(self, exchanges: Dict[str, Any]) -> bool:
        """
        🔥 检查三交易所逻辑一致性
        """
        try:
            # 检查每个交易所是否都有相同的核心方法
            required_methods = [
                "preheat_api_connections",
                "preheat_execution_price_cache",
                "get_cached_execution_price"
            ]
            
            method_consistency = {}
            for exchange_name, exchange in exchanges.items():
                for method_name in required_methods:
                    has_method = hasattr(exchange, method_name)
                    if method_name not in method_consistency:
                        method_consistency[method_name] = []
                    method_consistency[method_name].append((exchange_name, has_method))
            
            # 检查是否所有交易所都有相同的方法
            for method_name, exchange_methods in method_consistency.items():
                has_method_count = sum(1 for _, has_method in exchange_methods if has_method)
                if has_method_count != len(exchanges):
                    logger.warning(f"⚠️ 方法{method_name}在交易所间不一致")
                    return False
            
            return True
            
        except Exception as e:
            logger.debug(f"逻辑一致性检查失败: {e}")
            return False
    
    async def _validate_performance_compliance(self, preheat_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔥 验证性能指标合规性
        """
        performance_compliance = {
            "compliant": True,
            "total_time_ok": True,
            "stage_times_ok": True,
            "improvement_ok": True,
            "violations": []
        }
        
        try:
            # 检查总预热时间（不应超过5分钟）
            total_time = preheat_results.get("total_time_ms", 0)
            max_preheat_time = 300000  # 5分钟
            
            if total_time > max_preheat_time:
                performance_compliance["total_time_ok"] = False
                performance_compliance["compliant"] = False
                performance_compliance["violations"].append(
                    f"总预热时间超限: {total_time:.0f}ms > {max_preheat_time}ms"
                )
            
            # 检查各阶段时间合理性
            stage_time_limits = {
                "stage1_connection": 30000,    # 30秒
                "stage2_infrastructure": 60000, # 60秒
                "stage3_execution": 90000,      # 90秒
                "stage4_leverage": 120000,      # 120秒
                "stage5_validation": 30000      # 30秒
            }
            
            detailed_results = preheat_results.get("detailed_results", {})
            for stage_name, time_limit in stage_time_limits.items():
                stage_time = detailed_results.get(stage_name, {}).get("time_ms", 0)
                if stage_time > time_limit:
                    performance_compliance["stage_times_ok"] = False
                    performance_compliance["compliant"] = False
                    performance_compliance["violations"].append(
                        f"{stage_name}耗时超限: {stage_time:.0f}ms > {time_limit}ms"
                    )
            
            # 检查性能改善是否达到预期
            improvement = preheat_results.get("performance_improvement", 0)
            min_improvement = 10.0  # 至少10倍改善
            
            if improvement < min_improvement:
                performance_compliance["improvement_ok"] = False
                performance_compliance["compliant"] = False
                performance_compliance["violations"].append(
                    f"性能改善不足: {improvement:.1f}倍 < {min_improvement}倍"
                )
                
        except Exception as e:
            logger.warning(f"⚠️ 性能合规验证失败: {e}")
            performance_compliance["compliant"] = False
            performance_compliance["error"] = str(e)
        
        return performance_compliance
    
    def _generate_compliance_feedback(self, api_compliance: Dict, consistency_compliance: Dict, performance_compliance: Dict) -> Tuple[List[str], List[str]]:
        """
        🔥 生成合规反馈和建议
        """
        violations = []
        recommendations = []
        
        # 收集所有违规
        violations.extend(api_compliance.get("violations", []))
        violations.extend(consistency_compliance.get("violations", []))
        violations.extend(performance_compliance.get("violations", []))
        
        # 生成建议
        if not api_compliance.get("compliant", True):
            recommendations.append("建议: 调整API调用频率，确保不超过交易所限制")
            recommendations.append("建议: 减少并发连接数，分批进行预热操作")
        
        if not consistency_compliance.get("compliant", True):
            recommendations.append("建议: 统一所有交易所的关键阈值配置")
            recommendations.append("建议: 确保三交易所使用相同的处理逻辑")
        
        if not performance_compliance.get("compliant", True):
            recommendations.append("建议: 优化预热流程，减少不必要的等待时间")
            recommendations.append("建议: 并行执行更多预热操作")
        
        return violations, recommendations
    
    def get_compliance_history(self) -> List[Dict[str, Any]]:
        """
        🔥 获取合规验证历史
        """
        return self.validation_history.copy()


class ComplianceMonitor:
    """
    🔥 合规性持续监控器
    """
    
    def __init__(self):
        self.validator = ComplianceValidator()
        self.monitoring_active = False
        self.monitor_task = None
        self.alert_thresholds = {
            "violation_count": 3,      # 3个违规触发告警
            "compliance_rate": 0.8,    # 80%合规率以下触发告警
            "performance_degradation": 0.5  # 50%性能下降触发告警
        }
        
    async def start_continuous_monitoring(self, exchanges: Dict[str, Any], check_interval: int = 300) -> bool:
        """
        🔥 开始持续合规监控
        """
        try:
            if self.monitoring_active:
                logger.info("合规监控已在运行")
                return True
            
            self.monitoring_active = True
            self.monitor_task = asyncio.create_task(
                self._monitor_loop(exchanges, check_interval)
            )
            
            logger.info(f"✅ 启动持续合规监控，检查间隔: {check_interval}秒")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动合规监控失败: {e}")
            return False
    
    async def _monitor_loop(self, exchanges: Dict[str, Any], check_interval: int):
        """
        🔥 监控循环
        """
        try:
            while self.monitoring_active:
                try:
                    # 执行合规性检查
                    compliance_report = await self.validator.validate_preheat_compliance(exchanges, {})
                    
                    # 检查是否需要告警
                    await self._check_alerts(compliance_report)
                    
                    # 等待下次检查
                    await asyncio.sleep(check_interval)
                    
                except Exception as e:
                    logger.error(f"❌ 合规监控循环异常: {e}")
                    await asyncio.sleep(check_interval)
                    
        except asyncio.CancelledError:
            logger.info("合规监控已停止")
        except Exception as e:
            logger.error(f"❌ 合规监控异常: {e}")
    
    async def _check_alerts(self, compliance_report: Dict[str, Any]):
        """
        🔥 检查告警条件
        """
        try:
            violations_count = len(compliance_report.get("violations", []))
            
            # 违规数量告警
            if violations_count >= self.alert_thresholds["violation_count"]:
                logger.warning(f"🚨 合规告警: 发现{violations_count}个违规项")
                await self._send_alert("high_violations", compliance_report)
            
            # 合规率告警
            if not compliance_report.get("overall_compliant", True):
                logger.warning("🚨 合规告警: 整体合规性不达标")
                await self._send_alert("compliance_failure", compliance_report)
                
        except Exception as e:
            logger.error(f"❌ 告警检查失败: {e}")
    
    async def _send_alert(self, alert_type: str, compliance_report: Dict[str, Any]):
        """
        🔥 发送告警
        """
        try:
            alert_message = {
                "type": alert_type,
                "timestamp": time.time(),
                "violations": compliance_report.get("violations", []),
                "recommendations": compliance_report.get("recommendations", [])
            }
            
            # 这里可以集成实际的告警系统
            logger.warning(f"📢 合规告警: {alert_type}")
            for violation in alert_message["violations"]:
                logger.warning(f"   违规: {violation}")
            for recommendation in alert_message["recommendations"]:
                logger.info(f"   建议: {recommendation}")
                
        except Exception as e:
            logger.error(f"❌ 发送告警失败: {e}")
    
    async def stop_monitoring(self):
        """
        🔥 停止监控
        """
        try:
            self.monitoring_active = False
            if self.monitor_task and not self.monitor_task.done():
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("✅ 合规监控已停止")
            
        except Exception as e:
            logger.error(f"❌ 停止合规监控失败: {e}")


# 🔥 全局实例
_compliance_monitor = None

def get_compliance_monitor() -> ComplianceMonitor:
    """获取合规性监控器实例"""
    global _compliance_monitor
    if _compliance_monitor is None:
        _compliance_monitor = ComplianceMonitor()
    return _compliance_monitor