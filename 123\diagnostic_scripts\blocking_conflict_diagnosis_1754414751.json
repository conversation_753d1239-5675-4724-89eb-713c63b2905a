{"diagnosis_time": "2025-08-05T19:25:51.636182", "problem_type": "重复阻塞检测函数冲突", "severity": "CRITICAL", "performance_impact": {"expected_latency": "<30ms", "actual_latency": "1939秒 (Gate.io), 1945秒 (OKX)", "performance_degradation": "99.998%"}, "conflict_analysis": {"files_with_blocking_logic": 5, "total_blocking_functions": 1, "total_blocking_indicators": 10}, "detailed_findings": {"websocket/enhanced_blocking_tracker.py": {"functions": ["def _handle_blocking"], "indicators": ["silent_duration变量", "last_data_time变量", "blocking_event处理"], "has_blocking_logic": true}, "websocket/ws_client.py": {"functions": [], "indicators": ["silent_duration变量"], "has_blocking_logic": true}, "websocket/gate_ws.py": {"functions": [], "indicators": ["data_flow_timeout变量", "last_data_time变量"], "has_blocking_logic": true}, "websocket/bybit_ws.py": {"functions": [], "indicators": ["data_flow_timeout变量", "last_data_time变量"], "has_blocking_logic": true}, "websocket/okx_ws.py": {"functions": [], "indicators": ["data_flow_timeout变量", "last_data_time变量"], "has_blocking_logic": true}}, "root_cause": "多个文件同时进行阻塞检测，导致系统资源冲突和消息队列积压", "fix_strategy": "保留enhanced_blocking_tracker作为唯一阻塞检测器，清理其他文件中的重复逻辑", "expected_improvement": "恢复<30ms延迟性能，消除消息队列积压"}