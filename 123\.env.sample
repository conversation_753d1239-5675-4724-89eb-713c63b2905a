# WebSocket订单簿配置

# 订单簿深度配置
GATE_SPOT_ORDERBOOK_DEPTH=10
GATE_FUTURES_ORDERBOOK_DEPTH=10
BYBIT_SPOT_ORDERBOOK_DEPTH=50
BYBIT_FUTURES_ORDERBOOK_DEPTH=50
OKX_SPOT_ORDERBOOK_DEPTH=20
OKX_FUTURES_ORDERBOOK_DEPTH=20

# 分析深度级别（前N档）- 🔥 升级：统一30档深度
ANALYSIS_DEPTH_LEVELS=30
MAX_DEPTH_LEVELS=30

# WebSocket连接配置
WS_CONNECTION_TIMEOUT=10
WS_HEARTBEAT_INTERVAL=20
WS_RECONNECT_DELAY=2.0
WS_MAX_RECONNECT_ATTEMPTS=5

# 数据处理配置
MAX_PROCESSING_TIME_MS=30
DATA_FRESHNESS_MS=1000
SYNC_TOLERANCE_MS=1000

# 订单簿质量要求 - 🔥 升级：统一30档深度
MIN_ASKS_DEPTH=5
MIN_BIDS_DEPTH=5
REQUIRED_DEPTH_FOR_ANALYSIS=30

# 测试配置
TEST_SYMBOLS=BTC-USDT,ETH-USDT
TEST_DURATION=60
DEBUG_MODE=false

# 报告配置
GENERATE_DETAILED_REPORT=true
REPORT_OUTPUT_DIR=./reports

# 🔥 修复：网络配置符合修复质量保证要求
# 确保Gate.io连接超时问题彻底解决
HTTP_KEEP_ALIVE=1
CONNECTION_TIMEOUT=5.0          # 🔥 修复：5秒连接超时（符合修复要求）
TOTAL_TIMEOUT=10.0              # 🔥 修复：10秒总超时（符合修复要求）
LIMIT_PER_HOST=20               # 20个连接/主机（合理配置）
KEEPALIVE_TIMEOUT=60            # 60秒保持连接（合理配置）
MAX_RETRIES=3                   # 3次重试（合理配置）
RETRY_DELAY=50                  # 50ms重试间隔（合理配置）
SSL_VERIFY=true

# 🔥 手动极限性能配置 - VPS专用
MANUAL_EXTREME_PERFORMANCE=true # 启用手动极限性能模式
CPU_CACHE_INTERVAL=1            # CPU监控缓存间隔1秒
WEBSOCKET_HEARTBEAT_FACTOR=20   # WebSocket心跳频率因子
ZERO_DELAY_MODE=true            # 零延迟模式

# 🔥 统一连接池管理配置 - 第31个核心统一模块
# 连接池基础配置
MAX_CONNECTIONS_PER_EXCHANGE=4  # 每个交易所最大连接数
CONNECTION_POOL_SIZE=12         # 总连接池大小（3交易所×4连接）
CONNECTION_MONITOR_INTERVAL=5.0 # 连接监控间隔（秒）

# 智能重连配置
BASE_RECONNECT_DELAY=1.0        # 基础重连延迟（秒）
MAX_RECONNECT_DELAY=120.0       # 最大重连延迟（秒）
RECONNECT_JITTER=0.1            # 重连抖动比例（10%）
MAX_RECONNECT_ATTEMPTS=10       # 最大重连次数

# 定期重启配置
CONNECTION_RESTART_INTERVAL=24.0 # 连接重启间隔（小时）
RESTART_WINDOW_START=2          # 重启窗口开始时间（凌晨2点）
RESTART_WINDOW_END=6            # 重启窗口结束时间（凌晨6点）

# 连接质量监控配置
QUALITY_CHECK_INTERVAL=30.0     # 质量检查间隔（秒）
POOR_QUALITY_THRESHOLD=0.05     # 质量阈值（5%错误率）
EXCELLENT_LATENCY_THRESHOLD=50.0 # 优秀延迟阈值（毫秒）
GOOD_LATENCY_THRESHOLD=100.0    # 良好延迟阈值（毫秒）
FAIR_LATENCY_THRESHOLD=200.0    # 一般延迟阈值（毫秒）

# 数据缓冲配置
DATA_BUFFER_SIZE=1000           # 数据缓冲大小
BUFFER_RETENTION_SECONDS=300.0  # 缓冲保留时间（秒）

# 故障切换配置
FAILOVER_ENABLED=true           # 启用故障切换
FAILOVER_THRESHOLD_ERRORS=5     # 故障切换错误阈值
FAILOVER_THRESHOLD_LATENCY=500.0 # 故障切换延迟阈值（毫秒）

# 心跳配置
HEARTBEAT_INTERVAL=30.0         # 心跳间隔（秒）
HEARTBEAT_TIMEOUT=10.0          # 心跳超时（秒）
