-### **套利流程**: 达到期货溢价（+）阈值开仓 →锁定差价-> 等待趋同 → 现货溢价（-）达到阈值-> 平仓
+ 代表期货溢价   -代表现货溢价 不代表负数！
- **一切以### **（通用系统支持任意代币的角度来，不允许针对任何代币进行特殊优化）**深度审查修复！ 在符合所有交易所api文档的规则和要求下， 确保差价精准性（比如禁止中间价，websocket:1000ms阈值保证数据新鲜度等等很多）、三交易所一致性（除非交易所特定规则需求）、高速性能的前提下进行!!!!!!!!!!!!!!! 这是核心理念，确保三交易所运行逻辑和错误处理逻辑一致和关键阈值一致比如毫秒时间戳等（除非交易所特定规则不一致）
 主要任务：  1按照两个md文档来修复现有系统！ 现在有缺陷！ 确保完美修复 没有任何问题！ 
 E执行链路.md
ExecutionEngine深度审查与Bug定位报告_简洁版.md

2 bybit 精度问题，我需要提示你！ 目前 精度系统和系统预热有逻辑缺陷！  我放在文档的273行 进行审查 ，不要有逻辑缺陷，这是个通用系统（随时更换代币，支持任意代币）

3  这是总结的需要修复和优化的问题汇总：

差价重复验证瓶颈 - 1964ms（两个文档都提到）
非真正并行执行 - 2545ms（符合API规则的最优执行链路)
WebSocket数据重复获取 - 384ms（主要在第一个文档）
HTTP请求头不统一 - 200ms延迟
TCP连接层优化缺失 - 50ms延迟
精度错误处理不完善 - 
会话复用效率问题 - 10-20ms延迟
异步任务管理缺陷 - 资源泄漏风险

## 🔧 优化规则与执行要求（标准版）

### 一、链路完整性与一致性优化
1. **接口统一性**
   * 所有接口参数必须标准化，入参顺序、命名风格全链路保持一致；
   * 函数、方法调用不能混用不同风格（如驼峰与下划线）；
2. **链路稳定性保障**
   * 禁止出现链路中断，如：调用丢失、数据类型不匹配、上下游字段未透传等；
   * 所有数据结构必须保证端到端的透传、无歧义、无重复、无缺失；
3. **冗余路径清理**
   * 自动识别并合并重复调用链，消除无效路径与冗余环节，确保最短调用链；
   * 所有链路节点应具有唯一职责，不得重复执行相同逻辑；

### 二、优化后文档同步与记录要求
4. **文档更新强制执行**
   * 所有修复、优化完成后，必须**同步更新**下列文档：
     * ✅ **修复类更新**：记录于 `123/docs/07B_核心问题修复专项文档.md`
     * ✅ **功能类新增或链路结构调整**：更新 `123/docs/07_全流程工作流文档.md`
   * 保持 `07_全流程工作流文档` 的**权威性、完整性、逻辑闭环**；

### 三、修复行为规范
5. **手动修复唯一允许方式**
   * 禁止使用任何形式的**修复脚本**；
   * 必须由开发者进行**手动分析、手动修复、手动验证**；
6. **优化确认标准（100%要求）**
   所有优化必须满足以下**6 项全量确认标准**，**逐项自查**、**严禁放行未确认内容**：
   ✅ 没有重复造轮子，100%使用已有统一模块；
   ✅ 没有引入任何新 Bug，修复闭环；
   ✅ 功能完整、逻辑闭环、职责清晰；
   ✅ 接口风格统一，无不兼容、无歧义；
   ✅ 链路稳定无断点，测试100%通过；
   ✅ 无冗余节点、无重复功能、无脏数据传递。
   * 请务必逐条自审确认，并由测试组进行最终验证盖章；

### 四、数据来源要求
7. **真实数据验证**
   * 禁止引入或使用任何**模拟数据、伪数据、硬编码数据**进行测试或逻辑验证；
   * 所有测试必须基于**真实生产数据或权威测试数据源**进行回归验证；

 