#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 预热机制效果验证测试 - 专门测试预热是否解决4.7秒瓶颈
"""

import asyncio
import time
import sys
import os

# 设置项目路径
project_root = '/root/myproject/123/77 修复了开仓差价变化错误的问题/123'
sys.path.insert(0, project_root)
os.chdir(project_root)

async def test_preheating_effectiveness():
    """测试预热机制的实际效果"""
    
    print("🚀 预热机制效果验证测试")
    print("=" * 60)
    
    total_start = time.time()
    
    try:
        # === 第一阶段：预热连接 ===
        print("\n⚡ 第一阶段：启动连接预热...")
        preheat_start = time.time()
        
        from core.exchange_connection_preheater import get_connection_preheater
        preheater = get_connection_preheater()
        
        # 启动预热
        preheat_success = await preheater.start_preheating_all_exchanges()
        preheat_time = (time.time() - preheat_start) * 1000
        
        print(f"📊 预热结果: {'成功' if preheat_success else '失败'}")
        print(f"📊 预热耗时: {preheat_time:.1f}ms")
        
        if not preheat_success:
            print("❌ 预热失败，无法继续测试")
            return False
        
        # === 第二阶段：零延迟获取预热连接 ===
        print("\n🔥 第二阶段：测试零延迟获取...")
        get_start = time.time()
        
        # 验证预热完成状态
        is_complete = preheater.is_preheating_complete()
        print(f"📊 预热完成状态: {is_complete}")
        
        # 零延迟获取所有预热连接
        preheated_exchanges = preheater.get_all_preheated_exchanges()
        get_time = (time.time() - get_start) * 1000
        
        print(f"📊 获取耗时: {get_time:.1f}ms")
        print(f"📊 获得交易所: {list(preheated_exchanges.keys()) if preheated_exchanges else '无'}")
        
        if not preheated_exchanges or get_time > 100:
            print("❌ 零延迟获取失败或太慢")
            return False
        
        # === 第三阶段：测试ExecutionEngine使用预热连接 ===
        print("\n🎯 第三阶段：测试ExecutionEngine使用预热连接...")
        execution_start = time.time()
        
        from core.execution_engine import ExecutionEngine
        execution_engine = ExecutionEngine()
        
        # 设置预热器连接（模拟main.py中的设置）
        execution_engine.connection_preheater = preheater
        
        # 初始化交易所（应该使用预热连接）
        await execution_engine._initialize_exchanges()
        
        execution_time = (time.time() - execution_start) * 1000
        
        print(f"📊 ExecutionEngine初始化耗时: {execution_time:.1f}ms")
        print(f"📊 获得交易所数量: {len(execution_engine.exchanges) if execution_engine.exchanges else 0}")
        
        # === 结果分析 ===
        total_time = (time.time() - total_start) * 1000
        
        print("\n" + "=" * 60)
        print("📊 预热机制效果分析")
        print("=" * 60)
        print(f"⚡ 预热阶段耗时: {preheat_time:.1f}ms")
        print(f"🔥 零延迟获取耗时: {get_time:.1f}ms")
        print(f"🎯 ExecutionEngine初始化耗时: {execution_time:.1f}ms")
        print(f"📊 总耗时: {total_time:.1f}ms ({total_time/1000:.3f}秒)")
        
        # 效果评估
        print("\n🎯 效果评估:")
        
        # 评估1: 预热连接是否生效
        if execution_time < 500:  # ExecutionEngine初始化小于500ms
            print(f"✅ 预热机制生效: ExecutionEngine初始化仅{execution_time:.1f}ms")
            preheating_effective = True
        else:
            print(f"❌ 预热机制未生效: ExecutionEngine仍需{execution_time:.1f}ms")
            preheating_effective = False
        
        # 评估2: 性能提升程度
        if execution_time < 100:
            performance_level = "🔥 优秀"
        elif execution_time < 300:
            performance_level = "✅ 良好"
        elif execution_time < 1000:
            performance_level = "⚠️ 一般"
        else:
            performance_level = "❌ 差"
        
        print(f"📈 性能水平: {performance_level} ({execution_time:.1f}ms)")
        
        # 评估3: 1秒目标达成
        one_second_target = total_time < 1000
        print(f"🎯 1秒目标: {'✅ 达成' if one_second_target else '❌ 未达成'} ({total_time/1000:.3f}秒)")
        
        # 评估4: 4.7秒瓶颈解决
        bottleneck_solved = execution_time < 1000  # 小于1秒认为瓶颈解决
        print(f"🔥 4.7秒瓶颈: {'✅ 已解决' if bottleneck_solved else '❌ 未解决'}")
        
        # 最终结论
        print("\n🏁 最终结论:")
        if preheating_effective and bottleneck_solved:
            print("🎉 预热机制工作正常，4.7秒瓶颈已解决！")
            success = True
        elif preheating_effective:
            print("✅ 预热机制部分有效，但仍需优化")
            success = True
        else:
            print("❌ 预热机制未生效，需要修复")
            success = False
        
        return success
        
    except Exception as e:
        total_time = (time.time() - total_start) * 1000
        print(f"❌ 测试异常: {e}")
        print(f"📊 异常前耗时: {total_time:.1f}ms")
        
        import traceback
        print(f"异常堆栈:\n{traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    print("🚀 启动预热机制效果验证测试")
    
    success = await test_preheating_effectiveness()
    
    if success:
        print("\n✅ 测试通过：预热机制工作正常")
        return True
    else:
        print("\n❌ 测试失败：预热机制需要修复")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)