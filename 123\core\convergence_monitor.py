#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
价差趋同监控器 - 负责监控价差趋同并触发平仓
从ExecutionEngine中分离出来的专门模块
"""

import asyncio
import time
import os
import inspect
from typing import Dict, List, Any, Optional, Tuple
from utils.logger import get_logger
from datetime import datetime
# 🚀 新增：动态趋同阈值模块 - 零破坏性集成
from core.dynamic_convergence_threshold import DynamicConvergenceThreshold
# 🚀 新增：资金费率感知动态阈值模块
from core.funding_aware_dynamic_threshold import get_funding_aware_dynamic_threshold

# 全局单例
_INSTANCE = None

def get_convergence_monitor():
    """获取ConvergenceMonitor单例"""
    global _INSTANCE
    if not _INSTANCE:
        logger = get_logger("ConvergenceMonitor")
        logger.warning("ConvergenceMonitor未初始化，返回None")
    return _INSTANCE

def init_convergence_monitor(config=None, exchanges=None, opportunity_scanner=None):
    """🔥 **架构优化**: 初始化ConvergenceMonitor单例 - 强制使用动态阈值"""
    global _INSTANCE
    if _INSTANCE is None:
        if not config:
            # 🔥 **架构优化**: 移除固定阈值配置，强制使用动态阈值
            config = {
                "MAX_CONVERGENCE_WAIT": os.getenv("MAX_CONVERGENCE_WAIT", "1800"),  # 默认30分钟
                "ENABLE_DYNAMIC_THRESHOLD": "true"  # 强制启用动态阈值
            }
        _INSTANCE = ConvergenceMonitor(config, exchanges, opportunity_scanner)
    return _INSTANCE

class ConvergenceMonitor:
    """🔥 新增：价差趋同监控器 - 专门监控价差趋同至目标区间"""

    def __init__(self, config=None, exchanges=None, opportunity_scanner=None):
        """🔥 修复：添加OpportunityScanner依赖注入，解决价格获取问题"""
        self.logger = get_logger(self.__class__.__name__)
        self.exchanges = exchanges or {}
        self.opportunity_scanner = opportunity_scanner  # 🔥 关键修复：注入OpportunityScanner实例

        # 🔥 **架构优化**: 强制使用动态阈值配置
        if not config:
            config = {
                "MAX_CONVERGENCE_WAIT": os.getenv("MAX_CONVERGENCE_WAIT", "1800"),  # 默认30分钟
                "ENABLE_DYNAMIC_THRESHOLD": "true"  # 强制启用动态阈值
            }

        # 🔥 **架构优化**: 移除固定阈值配置，只保留必要参数
        self.max_wait_time = int(config.get("MAX_CONVERGENCE_WAIT", "1800"))  # 默认30分钟

        # 添加活跃监控数据
        self.active_monitors = {}

        # 🔥 **架构优化**: 强制启用动态趋同阈值
        self.enable_dynamic_threshold = True  # 强制启用
        
        # 🚀 新增：启用资金费率感知功能
        self.enable_funding_awareness = self._get_config_bool("ENABLE_FUNDING_AWARENESS", True)

        # 🔥 **架构优化**: 创建资金费率感知动态阈值实例
        # 使用标准动态阈值配置
        base_threshold = 0.002  # 标准初始阈值：0.2%

        # 从环境变量读取动态阈值配置
        initial_threshold = float(os.getenv("DYNAMIC_INITIAL_THRESHOLD", str(base_threshold)))
        final_threshold = float(os.getenv("DYNAMIC_FINAL_THRESHOLD", str(base_threshold * 0.1)))
        # 🔥 修复：使用MAX_CONVERGENCE_WAIT作为动态阈值的max_duration，确保时间配置一致
        max_duration = float(os.getenv("DYNAMIC_MAX_DURATION", str(self.max_wait_time)))
        decay_function = os.getenv("DYNAMIC_DECAY_FUNCTION", "exponential")

        # 🚀 优化：使用资金费率感知动态阈值替代原有动态阈值
        if self.enable_funding_awareness:
            self.dynamic_threshold = get_funding_aware_dynamic_threshold(
                initial_threshold=initial_threshold,
                final_threshold=final_threshold,
                max_duration=max_duration,
                decay_function=decay_function,
                enable_funding_awareness=True
            )
            self.logger.info("✅ 资金费率感知动态阈值已启用")
        else:
            # 降级到原始动态阈值
            from core.dynamic_convergence_threshold import DynamicConvergenceThreshold
            self.dynamic_threshold = DynamicConvergenceThreshold(
                initial_threshold=initial_threshold,
                final_threshold=final_threshold,
                max_duration=max_duration,
                decay_function=decay_function
            )
            self.logger.info("✅ 标准动态阈值已启用（未启用资金费率感知）")
            
        self.logger.info(f"   📊 初始阈值: {initial_threshold*100:.3f}%")
        self.logger.info(f"   📊 最终阈值: {final_threshold*100:.3f}%")
        self.logger.info(f"   ⏱️ 最大持续时间: {max_duration}秒")
        self.logger.info(f"   📈 衰减函数: {decay_function}")
        self.logger.info(f"   🎯 资金费率感知: {'启用' if self.enable_funding_awareness else '禁用'}")

        self.logger.info("✅ 价差趋同监控器初始化完成")
        self.logger.info(f"  📊 平仓阈值: 使用动态阈值系统")
        self.logger.info(f"  ⏱️ 最大等待时间: {self.max_wait_time}秒")

    def _get_config_bool(self, key: str, default: bool) -> bool:
        """获取布尔配置值"""
        import os
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')

    async def start_monitoring(self, symbol: str, spot_exchange: Any, futures_exchange: Any,
                             initial_spread: float = None, target_spread: float = None,
                             opening_snapshot: Dict[str, Any] = None) -> bool:
        """🔥 修复：开始监控价差趋同 - 支持开仓快照数据"""
        try:
            # 停止已有的监控
            if symbol in self.active_monitors:
                self.logger.info(f"⚠️ 已存在监控: {symbol}，先停止")
                await self.stop_monitoring(symbol)

            # 🔥 修复：使用已导入的os模块

            # 🔥 **架构优化**: 完全使用动态阈值，移除固定阈值残留
            # 动态阈值系统不需要target_spread参数
            target_spread_decimal = None  # 动态阈值系统中不使用固定值

            # 🔥 **关键修复**：创建新的监控信息，包含开仓快照
            self.active_monitors[symbol] = {
                "spot_exchange": spot_exchange,
                "futures_exchange": futures_exchange,
                "start_time": time.time(),  # 使用时间戳
                "initial_spread": initial_spread,
                "target_spread": target_spread_decimal,
                "last_spread": initial_spread,
                "last_update_time": time.time(),
                "is_active": True,
                "opening_snapshot": opening_snapshot,  # 🔥 新增：保存开仓时的快照数据
                "last_snapshot": None  # 🔥 新增：保存最后一次快照
            }
            
            self.logger.info(f"🔍 开始监控价差趋同: {symbol} ({spot_exchange} ↔ {futures_exchange})")
            self.logger.info(f"   初始价差: {initial_spread*100:.2f}%")
            self.logger.info(f"   目标价差: {target_spread_decimal*100:.2f}%")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 启动趋同监控失败: {e}")
            return False

    async def stop_monitoring(self, symbol: str, reason: str = "手动停止") -> bool:
        """停止监控指定交易对的价差趋同"""
        if symbol in self.active_monitors:
            self.active_monitors[symbol]["is_active"] = False
            
            elapsed = time.time() - self.active_monitors[symbol]["start_time"]
            self.logger.info(f"⏹️ 停止监控 {symbol}，持续时间: {elapsed:.1f}秒，原因: {reason}")
            
            return True
        return False
        
    async def resume_monitoring(self, symbol: str) -> bool:
        """🔥 新增：恢复监控 - 平仓失败后继续监控价差"""
        if symbol in self.active_monitors:
            monitor_info = self.active_monitors[symbol]
            if not monitor_info["is_active"]:
                monitor_info["is_active"] = True
                self.logger.info(f"🔄 恢复监控 {symbol}，继续等待价差趋同")
                return True
            else:
                self.logger.debug(f"📊 {symbol} 监控已经是活跃状态")
                return True
        else:
            self.logger.warning(f"⚠️ 无法恢复监控 {symbol}，未找到监控记录")
            return False

    async def get_current_spread_with_opening_snapshot(self, symbol: str) -> float:
        """🔥 新增：使用开仓快照数据进行趋同监控 - 确保数据一致性"""
        if symbol not in self.active_monitors:
            self.logger.warning(f"⚠️ 未找到监控: {symbol}")
            return 0.0

        monitor_info = self.active_monitors[symbol]
        opening_snapshot = monitor_info.get("opening_snapshot")

        if opening_snapshot:
            # 🔥 使用开仓时的快照数据，确保与开仓计算完全一致
            try:
                from core.unified_order_spread_calculator import get_order_spread_calculator
                calculator = get_order_spread_calculator()

                # 🔥 使用开仓快照中的订单簿数据
                order_result = calculator.calculate_order_based_spread(
                    opening_snapshot['spot_orderbook'],
                    opening_snapshot['futures_orderbook'],
                    100.0, "opening"
                )

                if order_result:
                    self.logger.debug(f"✅ 使用开仓快照数据: {symbol}")
                    self.logger.debug(f"   快照时间戳: {opening_snapshot['snapshot_timestamp']}")
                    return order_result.executable_spread
                else:
                    self.logger.warning(f"⚠️ 开仓快照计算失败: {symbol}")

            except Exception as e:
                self.logger.error(f"❌ 开仓快照计算异常: {e}")

        # 🔥 备用方案：使用实时数据快照
        return await self.get_current_spread(symbol)

    async def get_current_spread(self, symbol: str) -> float:
        """🔥 修复：获取当前价差 - 使用DataSnapshotValidator统一快照机制"""
        if symbol not in self.active_monitors:
            self.logger.warning(f"⚠️ 未找到监控: {symbol}")
            return 0.0

        monitor_info = self.active_monitors[symbol]

        # 🚀 新增：详细时间戳记录
        start_time = time.time()

        try:
            # 🔥 关键修复：使用OpportunityScanner.market_data获取实时价格
            if not self.opportunity_scanner:
                self.logger.warning("⚠️ OpportunityScanner实例未注入，返回上次记录的价差")
                return monitor_info.get("last_spread", 0)

            # 🔥 获取交易所名称（标准化处理）
            spot_exchange_name = self._get_exchange_name(monitor_info["spot_exchange"])
            futures_exchange_name = self._get_exchange_name(monitor_info["futures_exchange"])

            # 🔥 构造数据key，符合OpportunityScanner.market_data的key格式
            spot_key = f"{spot_exchange_name}_spot_{symbol}"
            futures_key = f"{futures_exchange_name}_futures_{symbol}"

            # 🔥 从OpportunityScanner.market_data获取市场数据
            spot_market_data = self.opportunity_scanner.market_data.get(spot_key)
            futures_market_data = self.opportunity_scanner.market_data.get(futures_key)

            if not spot_market_data or not futures_market_data:
                self.logger.error(f"❌ 缺少市场数据: spot={bool(spot_market_data)}, futures={bool(futures_market_data)}")
                return monitor_info.get("last_spread", 0)

            # 🔥 验证价格数据有效性
            spot_price = spot_market_data.price
            futures_price = futures_market_data.price
            if spot_price is None or futures_price is None or spot_price <= 0 or futures_price <= 0:
                self.logger.debug(f"⚠️ 价格数据不完整: spot={spot_price}, futures={futures_price}")
                return monitor_info.get("last_spread", 0)

            # 🔥 **核心修复**：添加与开仓完全相同的DataSnapshotValidator快照机制
            spot_orderbook = spot_market_data.orderbook if hasattr(spot_market_data, 'orderbook') else {}
            futures_orderbook = futures_market_data.orderbook if hasattr(futures_market_data, 'orderbook') else {}

            # 🔥 **关键修复1**：使用DataSnapshotValidator创建统一快照
            from core.data_snapshot_validator import DataSnapshotValidator
            snapshot_validator = DataSnapshotValidator()

            # 🔥 **关键修复2**：创建数据快照，确保时间戳一致性
            data_snapshot = snapshot_validator.create_validated_snapshot(
                spot_market_data, futures_market_data,
                spot_orderbook, futures_orderbook
            )

            if not data_snapshot:
                self.logger.warning(f"⚠️ 数据快照创建失败: {symbol}")
                return monitor_info.get("last_spread", 0)

            # 🔥 **关键修复3**：使用快照数据进行计算，确保与开仓一致
            from core.unified_order_spread_calculator import get_order_spread_calculator
            calculator = get_order_spread_calculator()

            # 🔥 使用快照中的订单簿数据，确保数据一致性
            order_result = calculator.calculate_order_based_spread(
                data_snapshot['spot_orderbook'],
                data_snapshot['futures_orderbook'],
                100.0, "opening"  # 🔥 修复：使用opening上下文，与OpportunityScanner一致
            )

            if order_result is None:
                self.logger.warning(f"⚠️ Order差价计算被拒绝，可能因滑点过大: {symbol}")
                self.logger.warning(f"   现货订单簿: {len(spot_orderbook.get('asks', []))}档asks, {len(spot_orderbook.get('bids', []))}档bids")
                self.logger.warning(f"   期货订单簿: {len(futures_orderbook.get('asks', []))}档asks, {len(futures_orderbook.get('bids', []))}档bids")
                self.logger.warning(f"   建议: 检查{symbol}的流动性或调整滑点阈值")
                return monitor_info.get("last_spread", 0)

            # 🔥 **关键修复4**：将计算结果添加到快照中（缓存机制）
            data_snapshot['calculation_result'] = order_result

            # 🔥 使用Order计算结果中的加权平均价格，确保与OpportunityScanner一致
            current_spread = order_result.executable_spread
            spot_price = order_result.spot_execution_price
            futures_price = order_result.futures_execution_price

            # 🔥 **关键修复5**：记录快照元数据，追踪数据来源
            self.logger.debug(f"✅ 使用数据快照计算价差: {symbol}")
            self.logger.debug(f"   快照时间戳: {data_snapshot['snapshot_timestamp']}")
            self.logger.debug(f"   验证元数据: {data_snapshot.get('validation_metadata', {})}")

            # 🔥 **关键修复6**：更新监控信息（使用快照计算结果）
            monitor_info["last_spread"] = current_spread
            monitor_info["last_update_time"] = time.time()
            monitor_info["spot_price"] = spot_price  # Order加权平均价格
            monitor_info["futures_price"] = futures_price  # Order加权平均价格
            monitor_info["last_snapshot"] = data_snapshot  # 🔥 新增：保存最后一次快照

            # 🚀 新增：详细时间戳日志
            elapsed_ms = (time.time() - start_time) * 1000
            elapsed_total = time.time() - monitor_info["start_time"]

            # 🔥 统一精度：价格和差价都使用8位小数，确保最高精度
            self.logger.info(f"📊 [价差跟踪-快照] {symbol} | "
                           f"现货${spot_price:.8f} ↔ 期货${futures_price:.8f} | "
                           f"差价={current_spread*100:.8f}% | "
                           f"获取耗时={elapsed_ms:.1f}ms | "
                           f"监控总时长={elapsed_total:.3f}s | "
                           f"快照时间戳={data_snapshot['snapshot_timestamp']} | "
                           f"上下文=opening")

            return current_spread

        except Exception as e:
            self.logger.error(f"❌ 获取价差异常: {e}")
            return monitor_info.get("last_spread", 0)

    async def detect_convergence_signal(self, symbol: str) -> bool:
        """检测价差是否已经趋同到目标区间"""
        if symbol not in self.active_monitors or not self.active_monitors[symbol]["is_active"]:
            return False

        monitor_info = self.active_monitors[symbol]

        # 🚀 新增：趋同检测时间戳记录
        detection_start = time.time()

        current_spread = await self.get_current_spread(symbol)
        target_spread = monitor_info["target_spread"]

        # 🔥 **架构优化**: 简化平仓逻辑，统一使用动态阈值判断
        # 动态阈值系统根据时间自动调整阈值

        # 🚀 详细趋同检测日志
        elapsed_total = time.time() - monitor_info["start_time"]

        # 🔥 **架构优化**: 使用动态阈值进行平仓判断
        should_close = await self.is_convergence_target_reached(current_spread, symbol=symbol)

        # 🔥 **架构优化**: 获取动态阈值信息用于日志记录
        actual_threshold = 0.002  # 默认值，实际使用动态阈值
        threshold_type = "动态"

        if self.enable_dynamic_threshold and self.dynamic_threshold and symbol in self.active_monitors:
            start_time = self.active_monitors[symbol]["start_time"]
            threshold_info = self.dynamic_threshold.get_threshold_info(start_time)
            actual_threshold = -threshold_info['current_threshold']  # 转换为负值（现货溢价阈值）
            threshold_type = "动态"

        self.logger.info(f"🔍 [趋同检测] {symbol} | "
                        f"当前价差={current_spread*100:.3f}% | "
                        f"{threshold_type}阈值={actual_threshold*100:.3f}% | "
                        f"监控时长={elapsed_total:.1f}s | "
                        f"判断结果={'平仓' if should_close else '继续等待'}")

        # 🚀 新增：详细逻辑判断日志
        detection_time = (time.time() - detection_start) * 1000

        if should_close:
            # 🔥 修复：统一的平仓触发日志，避免重复记录
            if self.enable_dynamic_threshold and self.dynamic_threshold and symbol in self.active_monitors:
                start_time = self.active_monitors[symbol]["start_time"]
                threshold_info = self.dynamic_threshold.get_threshold_info(start_time)
                current_threshold = threshold_info['current_threshold']
                self.logger.info(f"✅ [平仓触发] {symbol} | "
                               f"动态阈值平仓 | "
                               f"当前价差={current_spread*100:.3f}% | "
                               f"动态阈值={current_threshold*100:.3f}% | "
                               f"时间进度={threshold_info['time_progress_pct']:.1f}% | "
                               f"检测耗时={detection_time:.1f}ms")
            else:
                self.logger.info(f"✅ [平仓触发] {symbol} | "
                               f"动态阈值平仓 | "
                               f"当前价差={current_spread*100:.3f}% | "
                               f"使用动态阈值系统 | "
                               f"检测耗时={detection_time:.1f}ms")
            # 🔥 关键修复：正确返回True，触发平仓
            return True
        else:
            # 🔥 修复：简化等待日志，避免过多debug信息
            if current_spread >= 0:
                self.logger.debug(f"⏳ 期货溢价等待中: {symbol} | 当前={current_spread*100:.3f}% (等待价差收敛至现货溢价)")
            else:
                # 现货溢价但未达到阈值
                if self.enable_dynamic_threshold and self.dynamic_threshold and symbol in self.active_monitors:
                    start_time = self.active_monitors[symbol]["start_time"]
                    current_threshold = self.dynamic_threshold.calculate_current_threshold(start_time)
                    self.logger.debug(f"⏳ 现货溢价未达动态阈值: {symbol} | 当前={current_spread*100:.3f}% < 阈值={current_threshold*100:.3f}%")
                else:
                    self.logger.debug(f"⏳ 现货溢价未达动态阈值: {symbol} | 当前={current_spread*100:.3f}% < 动态阈值")

        # 检查是否超时 - 🔥 关键修复：最大时间到达后强制平仓
        elapsed = time.time() - monitor_info["start_time"]
        # 🔥 修复：优先使用实例配置，然后是环境变量
        max_wait = self.max_wait_time  # 使用实例配置的最大等待时间

        if elapsed > max_wait:
            self.logger.warning(f"⚠️ 趋同监控超时: {elapsed:.1f}秒 > {max_wait}秒")
            self.logger.info(f"🔥 [强制平仓] {symbol} | "
                           f"超时强制平仓 | "
                           f"当前价差={current_spread*100:.3f}% | "
                           f"超时时间={elapsed:.1f}秒 > {max_wait}秒 | "
                           f"强制平仓原因=达到最大等待时间")
            # 🔥 关键修复：最大时间到达后返回True，强制平仓
            return True

        # 🔥 关键修复：未达到平仓条件时正确返回False
        return False

    def get_monitoring_status(self, symbol: str) -> Dict:
        """获取价差监控状态"""
        try:
            if symbol not in self.active_monitors:
                return {"active": False, "symbol": symbol}
            
            monitor_info = self.active_monitors[symbol]
            current_time = time.time()
            
            return {
                "active": True,
                "symbol": symbol,
                "spot_exchange": monitor_info["spot_exchange"],
                "futures_exchange": monitor_info["futures_exchange"],
                "duration": current_time - monitor_info["start_time"],
                "initial_spread": monitor_info["initial_spread"],
                "initial_spread_percent": monitor_info["initial_spread"] * 100,
                "current_spread": monitor_info["last_spread"],
                "current_spread_percent": monitor_info["last_spread"] * 100,
                "target_spread": monitor_info["target_spread"],
                "target_spread_percent": monitor_info["target_spread"] * 100,
                "last_update_time": monitor_info["last_update_time"],
                "spot_price": monitor_info["spot_price"],
                "futures_price": monitor_info["futures_price"]
            }
        
        except Exception as e:
            self.logger.error(f"获取监控状态异常: {e}")
            return {"active": False, "symbol": symbol, "error": str(e)}

    async def monitor_price_convergence(self, symbol: str) -> Dict[str, Any]:
        """🔥 监控价格趋同 - 按照全流程工作流.md要求"""
        try:
            if symbol not in self.active_monitors:
                return {"error": "未找到活跃监控", "symbol": symbol}
            
            monitor_info = self.active_monitors[symbol]
            
            # 获取当前价差
            current_spread = await self.get_current_spread(symbol)
            
            # 检查趋同状态
            is_converged = await self.detect_convergence_signal(symbol)
            
            # 计算监控时长
            elapsed_time = time.time() - monitor_info["start_time"]
            
            return {
                "symbol": symbol,
                "current_spread": current_spread,
                "current_spread_percent": current_spread * 100,
                "target_spread": monitor_info["target_spread"],
                "target_spread_percent": monitor_info["target_spread"] * 100,
                "is_converged": is_converged,
                "elapsed_time": elapsed_time,
                "spot_price": monitor_info.get("spot_price", 0),
                "futures_price": monitor_info.get("futures_price", 0),
                "status": "converged" if is_converged else "monitoring"
            }
            
        except Exception as e:
            self.logger.error(f"❌ 监控价格趋同异常: {e}")
            return {"error": str(e), "symbol": symbol}

    def _get_exchange_name(self, exchange) -> str:
        """🔥 使用统一的交易所名称获取函数"""
        from exchanges.exchanges_base import get_exchange_name
        return get_exchange_name(exchange)

    async def is_convergence_target_reached(self, current_spread: float, target_spread: float = None, symbol: str = None) -> bool:
        """🔥 **架构优化**: 统一使用动态阈值系统"""

        # 🔥 **强制要求**: 必须提供symbol参数以使用动态阈值
        if not symbol:
            self.logger.error("❌ 阈值判断必须提供symbol参数以使用动态阈值")
            raise ValueError("symbol参数是必需的，系统要求使用动态阈值")

        # 🔥 **强制要求**: 必须启用动态阈值
        if not self.enable_dynamic_threshold or not self.dynamic_threshold:
            self.logger.error("❌ 系统要求启用动态阈值")
            raise ValueError("必须启用动态阈值，ENABLE_DYNAMIC_THRESHOLD=true")

        # 🔥 **强制要求**: 必须存在监控信息
        if symbol not in self.active_monitors:
            self.logger.error(f"❌ 未找到{symbol}的监控信息，无法进行动态阈值判断")
            raise ValueError(f"未找到{symbol}的监控信息")

        # 🔥 **统一逻辑**: 使用资金费率感知动态阈值判断
        monitor_info = self.active_monitors[symbol]
        start_time = monitor_info["start_time"]
        
        # 🚀 新增：如果启用资金费率感知，使用资金费率感知判断
        if self.enable_funding_awareness and hasattr(self.dynamic_threshold, 'should_close_position_with_funding_awareness'):
            try:
                # 获取交易所信息
                spot_exchange_name = self._get_exchange_name(monitor_info["spot_exchange"])
                futures_exchange_name = self._get_exchange_name(monitor_info["futures_exchange"])
                
                # 优先使用期货交易所的资金费率（因为我们是期货空头）
                exchange_for_funding = futures_exchange_name
                
                # 使用资金费率感知判断
                should_close, funding_decision = await self.dynamic_threshold.should_close_position_with_funding_awareness(
                    current_spread, start_time, exchange_for_funding, symbol
                )
                
                # 记录资金费率感知决策信息
                self.logger.debug(f"📊 资金费率感知决策: {symbol}")
                self.logger.debug(f"   当前差价: {current_spread*100:.3f}%")
                self.logger.debug(f"   使用阈值: {funding_decision.threshold_used*100:.3f}%")
                self.logger.debug(f"   原始阈值: {funding_decision.original_threshold*100:.3f}%")
                self.logger.debug(f"   资金费率调整: {'是' if funding_decision.funding_adjusted else '否'}")
                if funding_decision.funding_rate is not None:
                    self.logger.debug(f"   资金费率: {funding_decision.funding_rate*10000:.2f}bp")
                    self.logger.debug(f"   资金费率状态: {funding_decision.funding_state}")
                self.logger.debug(f"   时间进度: {funding_decision.time_progress*100:.1f}%")
                self.logger.debug(f"   重置次数: {funding_decision.reset_count}")
                self.logger.debug(f"   决策结果: {'平仓' if should_close else '继续等待'}")
                self.logger.debug(f"   决策原因: {funding_decision.decision_reason}")
                
                return should_close
                
            except Exception as e:
                self.logger.warning(f"⚠️ 资金费率感知判断异常，降级到标准动态阈值: {symbol} - {e}")
                # 降级到标准动态阈值判断

        # 使用标准动态阈值判断（降级方案）
        should_close, decision_info = self.dynamic_threshold.should_close_position(
            current_spread, start_time
        )

        # 记录动态阈值决策信息
        current_threshold = decision_info['current_threshold']
        self.logger.debug(f"📊 动态阈值决策: {symbol}")
        self.logger.debug(f"   当前差价: {current_spread*100:.3f}%")
        self.logger.debug(f"   动态阈值: {current_threshold*100:.3f}%")
        self.logger.debug(f"   时间进度: {decision_info['time_progress_pct']:.1f}%")
        self.logger.debug(f"   决策结果: {'平仓' if should_close else '继续等待'}")
        self.logger.debug(f"   决策原因: {decision_info['decision_reason']}")

        return should_close

    async def get_convergence_status(self, current_spread: float, elapsed_time: float, symbol: str = None) -> Dict:
        """获取趋同状态信息 - 支持动态阈值"""
        try:
            remaining_time = max(0, self.max_wait_time - elapsed_time)
            progress = elapsed_time / self.max_wait_time if self.max_wait_time > 0 else 1.0

            # 🚀 新增：传递symbol参数支持动态阈值
            target_reached = await self.is_convergence_target_reached(current_spread, symbol=symbol)
            
            status = {
                "current_spread": current_spread,
                "current_spread_percent": current_spread * 100,
                "close_threshold": "dynamic",  # 🔥 **架构优化**: 使用动态阈值
                "close_threshold_percent": "dynamic",
                "elapsed_time": elapsed_time,
                "remaining_time": remaining_time,
                "max_wait_time": self.max_wait_time,
                "progress": progress,
                "target_reached": target_reached,
                "timeout": remaining_time <= 0
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取趋同状态失败: {e}")
            return {"error": str(e)}


# 🔥 生产环境：移除测试代码，保持监控模块纯净